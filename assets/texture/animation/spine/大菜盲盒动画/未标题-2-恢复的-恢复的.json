{"skeleton": {"hash": "n8KiUiLUfC2DxoIKtAmUUmO/qI8", "spine": "3.8.75", "x": -334, "y": -1, "width": 657, "height": 277, "images": "./images/菜品盲盒/", "audio": "D:/BaiduNetdiskDownload/吃大席/images/菜品盲盒"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 62.31, "rotation": -90.43, "x": 180.29, "y": 273.8}, {"name": "bone2", "parent": "bone", "length": 36.36, "rotation": -1.05, "x": 62.31}, {"name": "bone3", "parent": "bone2", "length": 27.38, "rotation": 0.5, "x": 36.36}, {"name": "bone4", "parent": "bone3", "length": 27.86, "rotation": -0.95, "x": 27.38}, {"name": "bone5", "parent": "bone4", "length": 25.49, "rotation": 3, "x": 27.86}, {"name": "bone6", "parent": "bone5", "length": 25.49, "rotation": -1.06, "x": 25.49}, {"name": "bone7", "parent": "bone6", "length": 47.22, "rotation": -1.72, "x": 25.49}], "slots": [{"name": "另一个", "bone": "root", "attachment": "另一个"}, {"name": "撕开", "bone": "root", "dark": "000000", "attachment": "撕开"}], "skins": [{"name": "default", "attachments": {"撕开": {"撕开": {"type": "mesh", "uvs": [0, 0.10303, 0, 0.18491, 0, 0.27458, 0, 0.36945, 0, 0.46693, 0, 0.5644, 0, 0.65148, 0, 0.74895, 0, 0.86592, 0, 0.9114, 0.04004, 0.99978, 1, 1, 1, 0, 0, 0], "triangles": [0, 13, 12, 1, 0, 12, 2, 1, 12, 3, 2, 12, 4, 3, 12, 11, 5, 4, 11, 6, 5, 11, 7, 6, 11, 10, 8, 10, 9, 8, 11, 8, 7, 11, 4, 12], "vertices": [3, 1, 26.45, -15.09, 0.99717, 2, -35.57, -15.74, 0.00283, 4, -99.17, -16.77, 0, 3, 1, 49.13, -14.92, 0.91204, 2, -12.9, -15.16, 0.08795, 4, -76.51, -16, 2e-05, 3, 1, 73.97, -14.73, 0.1618, 2, 11.94, -14.51, 0.83818, 4, -51.68, -15.16, 2e-05, 3, 1, 100.25, -14.53, 0.00304, 2, 38.21, -13.83, 0.33761, 3, 1.73, -13.84, 0.65935, 2, 3, 28.73, -13.38, 0.44219, 4, 1.57, -13.35, 0.55781, 2, 4, 28.55, -12.44, 0.46673, 5, 0.04, -12.46, 0.53327, 2, 5, 24.15, -12.91, 0.50525, 6, -1.1, -12.93, 0.49475, 2, 6, 25.9, -12.93, 0.42624, 7, 0.8, -12.91, 0.57376, 1, 7, 33.18, -11.94, 1, 1, 7, 45.78, -11.56, 1, 3, 3, 176.19, -4.51, 0.0001, 6, 95.38, -6.6, 0.00155, 7, 70.06, -4.5, 0.99835, 5, 1, 273.71, 144.79, 0.00031, 3, 173.64, 147.14, 0.101, 5, 123.6, 143.28, 0.04626, 6, 95.44, 145.07, 0.36693, 7, 65.57, 147.1, 0.48549, 5, 1, -3.28, 142.69, 0.80166, 2, -68.2, 141.46, 0.01035, 3, -103.32, 142.37, 0.17014, 5, -153.35, 148.41, 0.00065, 6, -181.56, 145.07, 0.0172, 3, 1, -2.08, -15.3, 0.99999, 3, -100.59, -15.61, 1e-05, 4, -127.7, -17.74, 0], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 158, "height": 277}}, "另一个": {"另一个": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [167, -1, -334, -1, -334, 276, 167, 276], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 501, "height": 277}}}}], "animations": {"animation": {"slots": {"撕开": {"twoColor": [{"time": 0.4, "light": "ffffffff", "dark": "000000"}, {"time": 0.7333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 1.1, "light": "ffffff00", "dark": "000000"}]}}, "bones": {"bone": {"rotate": [{"time": 0.3}, {"time": 0.4, "angle": 20.97}, {"time": 0.8667, "angle": 46.3}], "translate": [{"time": 0.4}, {"time": 0.8667, "x": 31.3, "y": 28.39}]}, "bone3": {"rotate": [{"time": 0.2333}, {"time": 0.3, "angle": 15.09}]}, "bone4": {"rotate": [{"time": 0.1667}, {"time": 0.2333, "angle": 12.5}]}, "bone5": {"rotate": [{"time": 0.1}, {"time": 0.1667, "angle": 12.83}]}, "bone6": {"rotate": [{"time": 0.0667}, {"time": 0.1, "angle": 13.44}]}, "bone7": {"rotate": [{}, {"time": 0.0667, "angle": 15.24}]}}, "deform": {"default": {"撕开": {"撕开": [{"offset": 60, "vertices": [4.21561, -4.15225, 4.2912, -4.07402, 4.25552, -4.11131, 4.10581, -4.26083, 4.18399, -4.18405]}, {"time": 0.3, "offset": 60, "vertices": [-48.85925, -40.11919, -48.1134, -41.01108, -57.36467, -26.57237, -63.15913, 2.77484, -61.09763, 16.24525]}]}}}}}}