{"skeleton": {"hash": "HJ1Nw4F8lbg+LvWHw2Yw1GNoBQk", "spine": "3.8.75", "x": -95.68, "y": -2.23, "width": 188.18, "height": 378.44, "images": "./images/新娘拿着花的动作22222/", "audio": "D:/BaiduNetdiskDownload/吃大席/images/新娘拿着花的动作22222"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 49.5, "rotation": -179.54, "x": 24.13, "y": 9.42}, {"name": "bone2", "parent": "bone", "length": 59.42, "rotation": -124.04, "x": 49.5}, {"name": "bone3", "parent": "bone2", "length": 51.12, "rotation": 31.36, "x": 59.42}, {"name": "bone4", "parent": "bone3", "length": 86.4, "rotation": 4.58, "x": 51.12}, {"name": "bone5", "parent": "bone4", "length": 136.24, "rotation": -1.53, "x": 86.4}, {"name": "bone6", "parent": "bone5", "x": 90.97, "y": 26.75}, {"name": "bone7", "parent": "bone4", "length": 58.46, "rotation": -178.18, "x": 53.88, "y": -30.35}, {"name": "bone8", "parent": "bone7", "length": 67.26, "rotation": -87.71, "x": 58.46}, {"name": "bone9", "parent": "bone8", "x": 104.26, "y": -6.11}, {"name": "target2", "parent": "root", "x": 10.49, "y": 105.74, "color": "ff3f00ff"}, {"name": "target1", "parent": "root", "x": 12.89, "y": 338.21, "color": "ff3f00ff"}, {"name": "target3", "parent": "root", "x": -25.35, "y": 96.39, "color": "ff3f00ff"}], "slots": [{"name": "身体", "bone": "root", "attachment": "身体"}, {"name": "大臂", "bone": "root", "attachment": "大臂"}, {"name": "小臂", "bone": "root", "attachment": "小臂"}, {"name": "h<PERSON>", "bone": "root", "attachment": "h<PERSON>"}, {"name": "眼皮", "bone": "root", "dark": "000000", "attachment": "眼皮"}], "ik": [{"name": "target", "bones": ["bone2", "bone3"], "target": "target2"}, {"name": "target1", "order": 1, "bones": ["bone4", "bone5"], "target": "target1", "bendPositive": false}, {"name": "target3", "order": 2, "bones": ["bone7", "bone8"], "target": "target3", "bendPositive": false}], "skins": [{"name": "default", "attachments": {"身体": {"身体": {"type": "mesh", "uvs": [0.25218, 0.98793, 0.69631, 0.99278, 0.69893, 0.95019, 0.83125, 0.91677, 0.7723, 0.85963, 0.77492, 0.82136, 0.73823, 0.73834, 0.67535, 0.71193, 0.69238, 0.69576, 0.76968, 0.66399, 0.7854, 0.58691, 0.73037, 0.52276, 0.67142, 0.4861, 0.58233, 0.47155, 0.68753, 0.44364, 0.72552, 0.43125, 0.81461, 0.44257, 0.9679, 0.41993, 1, 0.37357, 0.97576, 0.32321, 1, 0.11945, 1, 0.0526, 0.72683, 0.00894, 0.47136, 0, 0.23816, 0, 0.16741, 0.0526, 0.17527, 0.1022, 0.09535, 0.13831, 0.00365, 0.15233, 0, 0.19761, 0, 0.30798, 0.0495, 0.32038, 0.06129, 0.40003, 0.14252, 0.45231, 0.25126, 0.49382, 0.30366, 0.50784, 0.33511, 0.5046, 0.2582, 0.53994, 0.17959, 0.57714, 0.17173, 0.63482, 0.23985, 0.67255, 0.2464, 0.68968, 0.24771, 0.72256, 0.20317, 0.81986, 0.20055, 0.85975, 0.22544, 0.92768, 0.26606, 0.93953], "triangles": [23, 26, 25, 27, 30, 29, 28, 27, 29, 31, 30, 27, 26, 31, 27, 31, 26, 19, 19, 32, 31, 13, 33, 32, 36, 34, 33, 13, 36, 33, 35, 34, 36, 22, 21, 20, 20, 26, 22, 23, 25, 24, 22, 26, 23, 19, 26, 20, 18, 15, 19, 19, 15, 32, 15, 18, 16, 17, 16, 18, 14, 32, 15, 14, 13, 32, 8, 7, 40, 8, 37, 10, 36, 10, 37, 8, 40, 37, 11, 10, 36, 11, 36, 13, 12, 11, 13, 9, 8, 10, 40, 7, 41, 40, 38, 37, 39, 38, 40, 7, 6, 5, 7, 5, 42, 42, 41, 7, 43, 42, 5, 2, 46, 4, 45, 44, 46, 4, 46, 5, 5, 46, 43, 43, 46, 44, 1, 46, 2, 0, 46, 1, 2, 4, 3], "vertices": [2, 1, 59.36, 9.31, 0.72314, 2, -13.24, 2.96, 0.27686, 1, 1, -10.8, 11.74, 1, 3, 1, -11.34, -4.61, 0.87146, 2, 37.88, -47.83, 0.08335, 3, -43.29, -29.63, 0.04519, 3, 1, -32.35, -17.28, 0.61553, 2, 60.14, -58.15, 0.19362, 3, -29.65, -50.03, 0.19085, 3, 1, -23.21, -39.29, 0.3379, 2, 73.27, -38.25, 0.21286, 3, -8.09, -39.87, 0.44924, 3, 1, -23.74, -53.99, 0.14649, 2, 85.74, -30.47, 0.10711, 3, 6.62, -39.72, 0.74639, 3, 1, -18.2, -85.91, 0.0078, 3, 38.24, -32.69, 0.95634, 4, -15.45, -31.56, 0.03585, 3, 1, -8.35, -96.13, 9e-05, 3, 48, -22.37, 0.68931, 4, -4.9, -22.05, 0.3106, 2, 3, 54.3, -24.82, 0.35216, 4, 1.19, -24.99, 0.64784, 2, 3, 66.97, -36.55, 0.04793, 4, 12.87, -37.7, 0.95207, 1, 4, 42.35, -41.4, 1, 1, 4, 67.32, -33.73, 1, 2, 4, 81.77, -25, 0.96264, 5, -3.97, -25.12, 0.03736, 2, 4, 87.93, -11.17, 0.4145, 5, 1.83, -11.12, 0.5855, 3, 1, -11.1, -199.14, 0, 4, 97.95, -28.22, 0.00327, 5, 12.3, -27.9, 0.99673, 2, 1, -17.14, -203.85, 0, 5, 16.97, -33.97, 1, 2, 1, -31.18, -199.39, 0, 5, 12.42, -47.98, 1, 2, 1, -55.47, -207.89, 0, 5, 20.76, -72.33, 1, 2, 1, -60.68, -225.65, 0, 5, 38.49, -77.66, 1, 2, 1, -57, -245.02, 0, 5, 57.88, -74.11, 1, 2, 1, -61.46, -323.23, 0, 5, 136.06, -79.07, 1, 2, 1, -61.67, -348.9, 0, 5, 161.73, -79.45, 1, 2, 1, -18.64, -366.01, 0, 5, 179.12, -36.54, 1, 1, 5, 183.14, 3.78, 1, 1, 5, 183.67, 40.62, 1, 1, 5, 163.64, 52.09, 1, 1, 5, 144.58, 51.12, 1, 1, 5, 130.89, 63.95, 1, 1, 5, 125.72, 78.52, 1, 1, 5, 108.34, 79.34, 1, 1, 5, 65.97, 79.96, 1, 1, 5, 61.09, 72.21, 1, 2, 4, 118.77, 69.95, 0.01068, 5, 30.48, 70.79, 0.98932, 2, 4, 98.18, 57.96, 0.08001, 5, 10.22, 58.25, 0.91999, 2, 4, 81.54, 41.45, 0.31935, 5, -5.97, 41.3, 0.68065, 2, 4, 75.82, 33.4, 0.48739, 5, -11.47, 33.1, 0.51261, 2, 4, 76.86, 28.38, 0.64939, 5, -10.3, 28.12, 0.35061, 3, 3, 111.44, 46.05, 0.00471, 4, 63.8, 41.08, 0.96399, 5, -23.69, 40.46, 0.0313, 3, 3, 96.68, 57.91, 0.0291, 4, 50.04, 54.08, 0.97071, 5, -37.79, 53.09, 0.00019, 4, 1, 70.99, -126.38, 0, 2, 92.69, 88.55, 0.00149, 3, 74.5, 58.29, 0.09741, 4, 27.96, 56.24, 0.9011, 4, 1, 60.34, -111.8, 0, 2, 86.58, 71.57, 0.01981, 3, 60.44, 46.97, 0.26276, 4, 13.04, 46.08, 0.71744, 4, 1, 59.36, -105.22, 0, 2, 81.67, 67.07, 0.04266, 3, 53.91, 45.69, 0.38706, 4, 6.43, 45.32, 0.57028, 4, 1, 59.25, -92.59, 0, 2, 71.27, 59.91, 0.1239, 3, 41.3, 44.99, 0.58176, 4, -6.2, 45.63, 0.29435, 4, 1, 66.59, -55.28, 2e-05, 2, 36.25, 45.11, 0.64857, 3, 3.69, 50.58, 0.34684, 4, -43.24, 54.2, 0.00457, 3, 1, 67.12, -39.97, 3e-05, 2, 23.26, 36.98, 0.83416, 3, -11.63, 50.4, 0.16581, 2, 2, 3.7, 19.27, 0.98552, 3, -37.54, 45.46, 0.01448, 3, 1, 57.02, -9.25, 0.03154, 2, 3.46, 11.41, 0.9646, 3, -41.84, 38.87, 0.00386], "hull": 47, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 0], "width": 158, "height": 384}}, "h花": {"h花": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 9, -58.06, 59.62, 1, 1, 9, 36.33, 48.88, 1, 1, 9, 24.57, -54.46, 1, 1, 9, -69.82, -43.72, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 95, "height": 104}}, "小臂": {"小臂": {"type": "mesh", "uvs": [0.10823, 1, 0.32503, 0.90905, 0.35957, 0.64682, 1, 0.54959, 1, 0, 0.68957, 0, 0.19649, 0.08995, 0.19649, 0.17245, 0.01422, 0.25789, 0, 0.55254, 0.03149, 0.90905], "triangles": [7, 6, 5, 5, 4, 3, 2, 7, 5, 2, 5, 3, 9, 7, 2, 7, 9, 8, 10, 9, 2, 1, 10, 2, 0, 10, 1], "vertices": [1, 8, 77.52, 30.82, 1, 1, 8, 58.42, 27.87, 1, 1, 8, 53.81, 13.62, 1, 1, 8, -1.53, 14.43, 1, 1, 8, -5.01, -16.15, 1, 1, 8, 21.52, -19.16, 1, 1, 8, 64.22, -18.95, 1, 1, 8, 64.74, -14.36, 1, 1, 8, 80.86, -11.38, 1, 1, 8, 83.94, 4.88, 1, 1, 8, 83.5, 25.02, 1], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 86, "height": 56}}, "眼皮": {"眼皮": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 6, -29.24, -49.74, 1, 1, 6, -27.7, 56.25, 1, 1, 6, 31.3, 55.39, 1, 1, 6, 29.76, -50.6, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 59}}, "大臂": {"大臂": {"type": "mesh", "uvs": [0.51305, 0.00028, 0.75612, 0.02933, 0.97084, 0.22547, 1, 0.93018, 0.75612, 1, 0.27807, 0.97559, 0.09577, 0.86117, 0.00664, 0.39625, 0.14438, 0.12384], "triangles": [2, 6, 7, 2, 7, 8, 1, 8, 0, 2, 8, 1, 3, 6, 2, 6, 4, 5, 3, 4, 6], "vertices": [1, 7, -13.07, 3.39, 1, 1, 7, -9.86, 12.66, 1, 1, 7, 7.77, 19.77, 1, 1, 7, 69, 16.43, 1, 1, 7, 74.37, 6.5, 1, 1, 7, 70.89, -11.94, 1, 1, 7, 60.44, -18.31, 1, 1, 7, 19.85, -18.82, 1, 1, 7, -3.4, -11.73, 1], "hull": 9, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 0, 16], "width": 39, "height": 87}}}}], "animations": {"animation": {"slots": {"眼皮": {"twoColor": [{"light": "ffffff00", "dark": "000000"}, {"time": 0.6667, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.7333, "light": "ffffff00", "dark": "000000"}, {"time": 0.7667, "light": "ffffffff", "dark": "000000"}, {"time": 0.8, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 1.2, "light": "ffffff00", "dark": "000000"}, {"time": 1.2333, "light": "ffffffff", "dark": "000000"}, {"time": 1.2667, "light": "ffffff00", "dark": "000000"}]}}, "bones": {"bone6": {"translate": [{"time": 0.6667, "x": 39.62, "y": 0.41}, {"time": 0.7667, "x": 1.4, "y": 0.01}, {"time": 0.8333, "x": 45.29, "y": 0.47}, {"time": 1.1333, "x": 39.62, "y": 0.41}, {"time": 1.2333, "x": 1.4, "y": 0.01}, {"time": 1.3, "x": 45.29, "y": 0.47}, {"time": 1.9333, "x": 91.18, "y": 0.94}]}, "target2": {"translate": [{"y": -6.02}, {"time": 0.3333, "y": -14.13}, {"time": 0.6667, "y": -6.02}, {"time": 1, "y": -14.13}, {"time": 1.3333, "y": -6.02}, {"time": 1.6667, "y": -14.13}, {"time": 2, "y": -6.02}]}, "target1": {"translate": [{"y": -6.02}, {"time": 0.3333, "y": -14.13}, {"time": 0.6667, "y": -6.02}, {"time": 1, "y": -14.13}, {"time": 1.3333, "y": -6.02}, {"time": 1.6667, "y": -14.13}, {"time": 2, "y": -6.02}]}, "target3": {"translate": [{}, {"time": 0.3333, "x": 5.96, "y": -19.67}, {"time": 0.6667, "x": -10.73, "y": 17.28}, {"time": 1.0333, "x": -29.2, "y": 8.94}, {"time": 1.4667, "x": 19.67, "y": -22.05}, {"time": 1.8, "x": -11.92, "y": 16.69}, {"time": 2}]}}}}}