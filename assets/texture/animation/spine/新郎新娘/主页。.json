{"skeleton": {"hash": "dciPtWmBtL1PlQmRmPoS/c/Qm28", "spine": "3.8.75", "x": -135.8, "y": -9, "width": 353.8, "height": 399.42, "images": "./images/新郎新娘新动画/", "audio": "D:/BaiduNetdiskDownload/吃大席/images/新郎新娘新动画"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "length": 43.37, "rotation": 1.09, "x": -60.1, "y": 17.76}, {"name": "bone2", "parent": "bone", "length": 72.69, "rotation": 125.13, "x": 43.37}, {"name": "bone3", "parent": "bone2", "length": 56.11, "rotation": -74.94, "x": 73.03, "y": -0.24}, {"name": "bone6", "parent": "bone3", "length": 61.13, "rotation": 38.34, "x": 56.11}, {"name": "bone8", "parent": "bone6", "length": 129.68, "rotation": 0.39, "x": 65.25, "y": 0.44}, {"name": "bone4", "parent": "root", "length": 48.19, "rotation": -110.3, "x": -70.44, "y": 173.79}, {"name": "bone5", "parent": "bone4", "length": 63.54, "rotation": 166.48, "x": 47.33, "y": 6.24}, {"name": "bone7", "parent": "bone6", "length": 4.15, "rotation": 6.1, "x": 61.13}, {"name": "target2", "parent": "root", "rotation": -72.97, "x": -30.37, "y": 117.29, "color": "ff3f00ff"}, {"name": "bone9", "parent": "bone8", "rotation": -90.42, "x": 112.65, "y": -15.09}, {"name": "bone10", "parent": "bone8", "rotation": -90.42, "x": 107.03, "y": -23.77}, {"name": "bone13", "parent": "root", "length": 96.84, "rotation": 179.91, "x": 148.93, "y": 17.41}, {"name": "bone14", "parent": "bone13", "length": 102.66, "rotation": -117.87, "x": 96.41, "y": -0.02}, {"name": "bone15", "parent": "bone14", "length": 41.46, "rotation": 60.79, "x": 102.66}, {"name": "bone18", "parent": "bone15", "length": 72.45, "rotation": -43.32, "x": 33.75, "y": 4.09}, {"name": "bone11", "parent": "bone18", "length": 55.98, "rotation": 173.88, "x": 54.67, "y": -46.73}, {"name": "bone12", "parent": "bone11", "length": 51.68, "rotation": -68.5, "x": 56.64, "y": -2.51}, {"name": "bone19", "parent": "bone18", "length": 103.12, "rotation": 16.56, "x": 72.45}, {"name": "target6", "parent": "root", "x": 82.41, "y": 140.21, "color": "ff3f00ff"}, {"name": "target9", "parent": "root", "x": 90.99, "y": 332.07, "color": "ff3f00ff"}, {"name": "bone20", "parent": "bone18", "rotation": -86.16, "x": -0.24, "y": 60.58}, {"name": "target11", "parent": "root", "x": -36.69, "y": 169.35, "color": "ff3f00ff"}, {"name": "bone21", "parent": "root", "x": 36.1, "y": 208.06}, {"name": "bone22", "parent": "bone8", "rotation": -90.42, "x": 109.01, "y": -39.7}, {"name": "bone23", "parent": "bone19", "rotation": -87.92, "x": 105.01, "y": 6.12}, {"name": "bone24", "parent": "bone23", "x": -4.66, "y": -22.35}], "slots": [{"name": "阴影", "bone": "root", "attachment": "阴影"}, {"name": "新郎", "bone": "root", "attachment": "新郎"}, {"name": "大臂膀", "bone": "root", "attachment": "大臂膀"}, {"name": "小手臂", "bone": "root", "attachment": "小手臂"}, {"name": "头发头纱", "bone": "root"}, {"name": "眼球", "bone": "root", "attachment": "眼球"}, {"name": "眼皮", "bone": "root", "dark": "000000", "attachment": "眼皮"}, {"name": "新娘", "bone": "root", "attachment": "图层 649"}, {"name": "图层 208", "bone": "root", "attachment": "图层 647"}, {"name": "图层 207", "bone": "root", "attachment": "图层 648"}, {"name": "图层 206", "bone": "root", "attachment": "图层 206"}, {"name": "爱心", "bone": "root"}, {"name": "新娘眼皮", "bone": "root", "dark": "000000", "attachment": "新娘眼皮 拷贝"}], "ik": [{"name": "target", "order": 3, "bones": ["bone2", "bone3"], "target": "target2", "bendPositive": false}, {"name": "target6", "bones": ["bone14", "bone15"], "target": "target6"}, {"name": "target9", "order": 2, "bones": ["bone18", "bone19"], "target": "target9"}, {"name": "target11", "order": 1, "bones": ["bone4", "bone5"], "target": "target11"}], "skins": [{"name": "default", "attachments": {"新娘眼皮": {"新娘眼皮": {"type": "mesh", "uvs": [0.05933, 0.26473, 0.11489, 0.13156, 0.18134, 0.07762, 0.27585, 0.18382, 0.33485, 0.36082, 0.34803, 0.61199, 0.47863, 0.49399, 0.46775, 0.32879, 0.54019, 0.08329, 0.64444, 0.03778, 0.77161, 0.06644, 0.92226, 0.18106, 0.99558, 0.26872, 0.96808, 0.75757, 0.88988, 0.91045, 0.68825, 0.92225, 0.66648, 0.84471, 0.48777, 0.92731, 0.2583, 0.99059, 0.18727, 0.95519, 0.15061, 0.98553, 0.04407, 0.95013, 0.11739, 0.85911, 0.03147, 0.88608, 0.01715, 0.83214, 0.00397, 0.71076, 0.06584, 0.65008, 0.08474, 0.55399, 0.03319, 0.37868], "triangles": [18, 5, 17, 18, 19, 5, 21, 22, 20, 20, 22, 19, 5, 19, 27, 5, 6, 17, 17, 6, 16, 15, 16, 14, 16, 10, 14, 14, 11, 13, 22, 23, 26, 23, 24, 26, 26, 27, 22, 27, 19, 22, 27, 4, 5, 16, 6, 9, 24, 25, 26, 14, 10, 11, 4, 27, 3, 2, 27, 1, 27, 2, 3, 6, 8, 9, 16, 9, 10, 13, 11, 12, 28, 0, 27, 27, 0, 1, 6, 7, 8], "vertices": [1, 25, -36.46, 3.15, 1, 1, 25, -31.28, 8.89, 1, 1, 25, -22.95, 7.43, 1, 1, 25, -14.01, 2.09, 1, 1, 25, -9.09, -5.07, 1, 1, 26, -4.61, 8.39, 1, 1, 26, 8.47, 10.25, 1, 1, 25, 4.58, -6.33, 1, 1, 25, 13.42, 0.85, 1, 1, 25, 24.27, 0.56, 1, 1, 25, 37, -2.69, 1, 1, 25, 51.59, -9.33, 1, 1, 25, 58.49, -13.65, 1, 1, 26, 57.41, -7.66, 1, 1, 26, 48.55, -11.54, 1, 1, 26, 27.18, -10.16, 1, 1, 26, 24.43, -9.01, 1, 1, 26, 7.66, -4.96, 1, 1, 26, -16.01, -3.06, 1, 1, 26, -23, -0.58, 1, 1, 26, -26.9, -0.97, 1, 1, 26, -37.49, 2.15, 1, 1, 26, -33.56, 3.35, 1, 1, 26, -38.39, 4.58, 1, 1, 26, -39.51, 6.69, 1, 1, 26, -40.11, 11.11, 1, 1, 26, -33.47, 12.1, 1, 1, 26, -30.97, 15.08, 1, 1, 25, -39.81, -0.31, 1], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56], "width": 103, "height": 35}, "新娘眼皮 拷贝": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 3, 0, 1, 2, 3], "vertices": [2, 25, 38.18, -39.85, 0.31697, 26, 42.84, -17.5, 0.68303, 2, 25, -67.82, -40.58, 0.09543, 26, -63.16, -18.23, 0.90457, 2, 25, -68.23, 18.42, 0.38839, 26, -63.56, 40.77, 0.61161, 2, 25, 37.77, 19.15, 0.9707, 26, 42.43, 41.5, 0.0293], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 106, "height": 59}}, "眼球": {"眼球": {"type": "mesh", "uvs": [0.15136, 0.96104, 0.19038, 0.66434, 0.19373, 0.37904, 0.1692, 0.28775, 0.13909, 0.23069, 0.07443, 0.23069, 0.03875, 0.45322, 0.03429, 0.77275, 0.04878, 0.94963, 0.12014, 1, 0.62409, 0.98957, 0.95413, 0.71569, 0.97642, 0.56616, 0.99458, 0.49087, 0.99251, 0.20381, 0.94975, 0.19558, 0.89734, 0.18028, 0.86309, 0.29322, 0.85389, 0.42734, 0.85412, 0.62381, 0.86746, 0.75322, 0.81021, 0.80028, 0.22757, 0.97008], "triangles": [13, 12, 15, 13, 15, 14, 16, 19, 18, 1, 3, 2, 12, 16, 15, 17, 16, 18, 19, 11, 20, 16, 11, 19, 16, 12, 11, 6, 9, 7, 9, 8, 7, 4, 1, 9, 1, 4, 3, 10, 22, 21, 11, 10, 21, 11, 21, 20, 4, 6, 5, 0, 9, 1, 6, 4, 9, 9, 0, 22, 9, 22, 10], "vertices": [1, 24, -58.81, -10.17, 1, 1, 24, -55.41, -5.13, 1, 1, 24, -55.12, -0.28, 1, 1, 24, -57.25, 1.27, 1, 1, 24, -59.87, 2.24, 1, 1, 24, -65.5, 2.24, 1, 1, 24, -68.6, -1.54, 1, 1, 24, -68.99, -6.97, 1, 1, 24, -67.73, -9.98, 1, 1, 24, -61.52, -10.84, 1, 1, 24, -17.68, -10.66, 1, 1, 24, 11.04, -6, 1, 1, 24, 12.98, -3.46, 1, 1, 24, 14.56, -2.18, 1, 1, 24, 14.38, 2.7, 1, 1, 24, 10.66, 2.84, 1, 1, 24, 6.1, 3.1, 1, 1, 24, 3.12, 1.18, 1, 1, 24, 2.32, -1.1, 1, 1, 24, 2.34, -4.44, 1, 1, 24, 3.5, -6.64, 1, 1, 24, -1.48, -7.44, 1, 1, 24, -52.17, -10.33, 1], "hull": 23, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 0, 44], "width": 87, "height": 17}}, "图层 208": {"图层 208": {"type": "mesh", "uvs": [0.05806, 0.9939, 0.65306, 0.9939, 0.98456, 0.67615, 1, 0.2184, 1, 0.03928, 0.57656, 0.0061, 0.18556, 0.01274, 0, 0.30796, 0, 0.67284], "triangles": [5, 4, 3, 2, 8, 7, 5, 7, 6, 8, 1, 0, 5, 3, 7, 7, 3, 2, 2, 1, 8], "vertices": [1, 16, 66.88, -2.17, 1, 1, 16, 63.35, 16.54, 1, 1, 16, 35.78, 22.13, 1, 1, 16, -1.2, 15.65, 1, 1, 16, -15.63, 12.92, 1, 1, 16, -15.79, -0.9, 1, 1, 16, -12.93, -13.09, 1, 1, 16, 11.96, -14.43, 1, 1, 16, 41.36, -8.88, 1], "hull": 9, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 0, 16], "width": 32, "height": 82}, "图层 647": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 16, 73.89, 23.26, 1, 1, 16, 78.31, -15.48, 1, 1, 16, -8.13, -25.35, 1, 1, 16, -12.55, 13.4, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 39, "height": 87}}, "爱心": {"爱心": {"type": "mesh", "uvs": [0.38414, 1, 0.52956, 1, 0.84024, 0.76622, 0.96252, 0.56342, 0.96583, 0.17343, 0.78736, 0.02523, 0.56922, 0.11103, 0.35109, 0.02133, 0.08337, 0.12273, 0, 0.37232, 0.08998, 0.71942], "triangles": [4, 6, 5, 7, 9, 8, 10, 9, 7, 10, 7, 6, 4, 3, 6, 2, 6, 3, 0, 10, 6, 6, 1, 0, 2, 1, 6], "vertices": [1, 23, -5.43, -23.06, 1, 1, 23, 3.15, -23.06, 1, 1, 23, 21.48, -11.37, 1, 1, 23, 28.69, -1.23, 1, 1, 23, 28.89, 18.27, 1, 1, 23, 18.36, 25.68, 1, 1, 23, 5.49, 21.39, 1, 1, 23, -7.38, 25.87, 1, 1, 23, -23.18, 20.8, 1, 1, 23, -28.1, 8.32, 1, 1, 23, -22.79, -9.03, 1], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 59, "height": 50}}, "头发头纱": {"头发头纱": {"type": "mesh", "uvs": [0.32305, 0.88448, 0.39953, 0.95572, 0.53533, 0.952, 0.60962, 0.97336, 0.70247, 0.95572, 0.80229, 0.95572, 0.93345, 0.94457, 1, 0.89722, 0.9439, 0.76165, 0.89515, 0.63924, 0.87426, 0.40246, 0.84872, 0.2427, 0.77212, 0.10806, 0.64908, 0.00127, 0.48542, 0, 0.36238, 0.02913, 0.03971, 0.09877, 0.04288, 0.22318, 0.06493, 0.34853, 0.07422, 0.49339, 0.06957, 0.6726, 0, 0.79239, 0, 0.87317, 0.13522, 0.84011, 0.2095, 0.84939, 0.26986, 0.76118, 0.29539, 0.86796], "triangles": [25, 10, 9, 20, 19, 25, 23, 21, 20, 25, 23, 20, 24, 23, 25, 0, 26, 25, 9, 0, 25, 22, 21, 23, 9, 2, 0, 9, 8, 2, 6, 8, 7, 8, 4, 2, 1, 0, 2, 8, 5, 4, 6, 5, 8, 3, 2, 4, 17, 16, 15, 18, 17, 15, 12, 19, 18, 12, 15, 14, 12, 14, 13, 25, 19, 10, 15, 12, 18, 11, 19, 12, 10, 19, 11], "vertices": [81.25, 183.39, 90.46, 165.98, 112.88, 161.7, 124.06, 154.56, 140.09, 154.73, 156.45, 151.02, 178.45, 148.42, 191.55, 155.65, 188.66, 185.5, 186.39, 212.39, 193.69, 262.06, 196.39, 295.77, 189.61, 326.09, 174, 352.29, 147.14, 358.2, 125.68, 356.48, 69.73, 353.43, 64.92, 327.85, 63.2, 301.57, 58.45, 271.95, 49.58, 235.88, 32.64, 214.05, 28.91, 197.52, 52.6, 199.29, 64.35, 194.65, 78.3, 210.51, 77.49, 187.79], "hull": 27, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 0, 52], "width": 168, "height": 210}}, "图层 206": {"图层 206": {"type": "mesh", "uvs": [0.96842, 0.96154, 0.03158, 0.96154, 0.03158, 0.03846, 0.96842, 0.03846], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 21, 64.3, -63.12, 1, 1, 21, -24.67, -61, 1, 1, 21, -22.38, 34.98, 1, 1, 21, 66.6, 32.85, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 95, "height": 104}}, "图层 207": {"图层 207": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 17, -6.9, 29.08, 1, 1, 17, 73.31, 4.13, 1, 1, 17, 62.62, -30.24, 1, 1, 17, -17.59, -5.29, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 84, "height": 36}, "图层 648": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 17, -2.48, 47.23, 1, 1, 17, 79.79, 22.16, 1, 1, 17, 63.46, -31.41, 1, 1, 17, -18.81, -6.34, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 86, "height": 56}}, "新郎": {"新郎": {"type": "mesh", "uvs": [0.78902, 0.98455, 0.63895, 0.98455, 0.62531, 0.99605, 0.37478, 0.99605, 0.35742, 0.9857, 0.37163, 0.94375, 0.37163, 0.92982, 0.39022, 0.86649, 0.37306, 0.85355, 0.35303, 0.83996, 0.32658, 0.83001, 0.25696, 0.77016, 0.3063, 0.7181, 0.30058, 0.70947, 0.29679, 0.63478, 0.33111, 0.52767, 0.3876, 0.51275, 0.37544, 0.50612, 0.26461, 0.47333, 0.19667, 0.42956, 0.1476, 0.37605, 0.06679, 0.35682, 0.0196, 0.33129, 0.01459, 0.28519, 0.04891, 0.259, 0.10469, 0.25303, 0.12257, 0.25933, 0.12757, 0.21622, 0.16762, 0.15913, 0.16047, 0.14919, 0.1855, 0.09348, 0.2048, 0.10873, 0.28275, 0.06695, 0.37211, 0.04692, 0.48795, 0.04062, 0.64741, 0.04327, 0.78399, 0.01807, 0.80381, 0.02537, 0.79237, 0.04659, 0.83313, 0.02603, 0.8982, 0.03532, 0.8932, 0.10495, 0.84386, 0.12684, 0.89963, 0.1662, 0.95326, 0.22257, 0.97257, 0.27165, 0.98397, 0.33899, 0.96467, 0.40399, 0.92534, 0.45638, 0.81593, 0.50479, 0.77374, 0.51839, 0.83023, 0.53825, 0.8381, 0.60125, 0.85311, 0.70703, 0.86813, 0.76824, 0.84382, 0.79012, 0.80306, 0.79045, 0.78518, 0.85344, 0.75729, 0.90053, 0.71224, 0.93336, 0.76015, 0.94927, 0.78375, 0.96857], "triangles": [49, 50, 17, 50, 16, 17, 48, 49, 17, 45, 48, 17, 18, 19, 17, 17, 19, 45, 34, 42, 28, 43, 28, 42, 46, 47, 48, 45, 19, 44, 34, 28, 31, 33, 34, 31, 42, 35, 38, 35, 42, 34, 33, 31, 32, 26, 43, 20, 27, 28, 43, 46, 48, 45, 19, 43, 44, 21, 26, 20, 43, 19, 20, 27, 43, 26, 21, 22, 26, 26, 23, 24, 24, 25, 26, 26, 22, 23, 28, 29, 31, 29, 30, 31, 42, 38, 41, 41, 38, 40, 40, 38, 39, 35, 36, 38, 38, 36, 37, 53, 12, 52, 52, 15, 16, 52, 50, 51, 16, 50, 52, 54, 56, 53, 14, 15, 52, 13, 14, 52, 56, 10, 53, 11, 12, 10, 53, 10, 12, 54, 55, 56, 52, 12, 13, 1, 59, 60, 58, 59, 7, 59, 6, 7, 58, 7, 57, 57, 7, 56, 56, 7, 8, 56, 8, 9, 9, 10, 56, 1, 2, 5, 5, 2, 3, 3, 4, 5, 1, 61, 0, 1, 60, 61, 59, 1, 6, 1, 5, 6], "vertices": [3, 1, 62.77, -8.99, 0.46188, 2, -19.45, -8.87, 0.53811, 3, -7.53, -92.57, 0, 1, 1, 35.91, -8.48, 1, 1, 1, 33.38, -12.87, 1, 1, 1, -11.45, -12.02, 1, 2, 1, -14.48, -7.96, 0.99921, 2, 31.63, 49.1, 0.00079, 2, 1, -11.63, 8.18, 0.82918, 2, 42.02, 36.42, 0.17082, 2, 1, -11.53, 13.55, 0.68683, 2, 46.03, 32.85, 0.31317, 2, 1, -7.74, 37.93, 0.07813, 2, 62.07, 14.09, 0.92187, 3, 1, -10.71, 42.98, 0.02418, 2, 67.84, 13.07, 0.92285, 3, -14.01, -2.8, 0.05297, 3, 1, -14.2, 48.3, 0.00351, 2, 74.14, 12.25, 0.72993, 3, -12.11, 3.26, 0.26656, 3, 2, 80.16, 13.23, 0.48592, 3, -12.03, 9.36, 0.51292, 4, -47.65, 49.61, 0.00115, 3, 2, 105.7, 7.16, 0.00495, 3, -1.63, 33.46, 0.95198, 4, -24.54, 62.06, 0.04306, 3, 3, 19.62, 38.99, 0.78577, 4, -4.44, 53.22, 0.21286, 5, -69.34, 53.25, 0.00137, 3, 3, 21.59, 41.85, 0.7287, 4, -1.12, 54.24, 0.26821, 5, -66, 54.25, 0.00309, 3, 3, 43.8, 60.26, 0.31572, 4, 27.71, 54.9, 0.61409, 5, -37.17, 54.71, 0.07019, 3, 3, 80.05, 81.06, 0.03851, 4, 69.06, 48.74, 0.43342, 5, 4.13, 48.27, 0.52807, 3, 3, 90.84, 76.7, 0.01443, 4, 74.81, 38.62, 0.23515, 5, 9.81, 38.11, 0.75042, 3, 3, 91.5, 79.99, 0.00767, 4, 77.37, 40.79, 0.14837, 5, 12.39, 40.27, 0.84396, 3, 3, 89.13, 103.41, 0, 4, 90.04, 60.63, 0.00727, 5, 25.19, 60.02, 0.99273, 1, 5, 42.18, 72.05, 1, 1, 5, 62.89, 80.69, 1, 1, 5, 70.42, 95.1, 1, 1, 5, 80.34, 103.47, 1, 1, 5, 98.14, 104.24, 1, 1, 5, 108.21, 98.02, 1, 1, 5, 110.44, 88.02, 1, 1, 5, 107.98, 84.84, 1, 1, 5, 124.62, 83.82, 1, 1, 5, 146.6, 76.49, 1, 1, 5, 150.45, 77.74, 1, 1, 5, 171.92, 73.1, 1, 1, 5, 166, 69.69, 1, 1, 5, 182.03, 55.62, 1, 1, 5, 189.64, 39.57, 1, 1, 5, 191.92, 18.82, 1, 1, 5, 190.69, -9.72, 1, 1, 5, 200.24, -34.24, 1, 1, 5, 197.39, -37.77, 1, 1, 5, 189.22, -35.66, 1, 1, 5, 197.1, -43.01, 1, 1, 5, 193.43, -54.63, 1, 1, 5, 166.56, -53.54, 1, 1, 5, 158.17, -44.65, 1, 1, 5, 142.91, -54.52, 1, 1, 5, 121.08, -63.96, 1, 1, 5, 102.11, -67.28, 1, 1, 5, 76.1, -69.13, 1, 2, 4, 116.73, -64.7, 0.00117, 5, 51.04, -65.49, 0.99883, 2, 4, 96.51, -57.65, 0.02146, 5, 30.87, -58.3, 0.97853, 2, 4, 77.84, -38.05, 0.19136, 5, 12.32, -38.58, 0.80864, 2, 4, 72.59, -30.5, 0.43212, 5, 7.13, -30.99, 0.56788, 2, 4, 64.92, -40.6, 0.76051, 5, -0.61, -41.04, 0.23949, 3, 3, 114.01, -7.76, 0.00088, 4, 40.6, -42, 0.97907, 5, -24.94, -42.27, 0.02005, 3, 2, 52.88, -88.71, 0.05779, 3, 83.64, -35.18, 0.25539, 4, -0.23, -44.66, 0.68682, 3, 2, 33.45, -75, 0.1722, 3, 66.77, -51.93, 0.46252, 4, -23.86, -47.34, 0.36528, 3, 2, 30.04, -66.13, 0.2106, 3, 57.44, -53.75, 0.48563, 4, -32.3, -42.98, 0.30377, 3, 2, 34.8, -60.6, 0.27182, 3, 52.82, -48.1, 0.49674, 4, -32.43, -35.68, 0.23144, 3, 2, 18.78, -42.03, 0.66651, 3, 31.76, -60.66, 0.30505, 4, -56.74, -32.47, 0.02844, 3, 2, 8.54, -26.21, 0.87336, 3, 14.4, -68.01, 0.12456, 4, -74.91, -27.47, 0.00208, 3, 1, 49.4, 11.03, 0.02554, 2, 4.44, -11.76, 0.95317, 3, -0.54, -69.54, 0.02129, 3, 1, 57.86, 4.72, 0.30089, 2, -5.85, -14.07, 0.69755, 3, -0.05, -80.08, 0.00156, 2, 1, 61.94, -2.81, 0.4383, 2, -14.22, -12.27, 0.5617], "hull": 62, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 0, 122], "width": 179, "height": 386}}, "大臂膀": {"大臂膀": {"type": "mesh", "uvs": [0.65705, 0, 0.78611, 0.01363, 0.89007, 0.09732, 1, 0.41399, 0.85781, 0.64244, 0.76818, 0.85054, 0.52441, 1, 0.08347, 1, 0.11932, 0.58137, 0.19102, 0.38911, 0.32724, 0.17423], "triangles": [0, 2, 3, 2, 0, 1, 0, 4, 10, 3, 4, 0, 4, 9, 10, 4, 8, 9, 5, 8, 4, 6, 7, 8, 5, 6, 8], "vertices": [1, 6, -16.18, -10.37, 1, 1, 6, -18.69, -3.91, 1, 1, 6, -15.46, 4.42, 1, 1, 6, 4.43, 23.02, 1, 1, 6, 24.78, 26.34, 1, 1, 6, 42.23, 31.18, 1, 1, 6, 59.63, 26.48, 1, 1, 6, 71.56, 6.39, 1, 1, 6, 40.36, -9.93, 1, 1, 6, 24.53, -14.92, 1, 1, 6, 5.33, -17.93, 1], "hull": 11, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 0, 20], "width": 53, "height": 84}}, "小手臂": {"小手臂": {"type": "mesh", "uvs": [0.39541, 0.98184, 0.486, 0.96534, 0.761, 0.88944, 0.80306, 0.62709, 0.93894, 0.58089, 1, 0.32515, 0.97129, 0.14695, 0.84188, 0.00835, 0.52806, 0, 0.24982, 0.0859, 0.08806, 0.4456, 0.20777, 0.69639, 0, 0.76404, 0.07188, 0.95544], "triangles": [5, 8, 6, 6, 8, 7, 8, 5, 9, 4, 3, 5, 5, 10, 9, 3, 10, 5, 11, 10, 3, 2, 11, 3, 13, 12, 11, 11, 0, 13, 2, 1, 11, 1, 0, 11], "vertices": [1, 7, 89.67, 1.57, 1, 1, 7, 87, 5.68, 1, 1, 7, 76.38, 17.57, 1, 1, 7, 50.35, 13.6, 1, 1, 7, 44.26, 19.28, 1, 1, 7, 18.66, 16.41, 1, 1, 7, 1.66, 10.87, 1, 1, 7, -10.31, 1.25, 1, 1, 7, -7.42, -14.52, 1, 1, 7, 4.21, -26.34, 1, 1, 7, 41.11, -26.07, 1, 1, 7, 64.11, -14.34, 1, 1, 7, 73.14, -23.08, 1, 1, 7, 90.91, -15.1, 1], "hull": 14, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 0, 26], "width": 51, "height": 100}}, "新娘": {"图层 649": {"type": "mesh", "uvs": [0.6936, 0.94801, 0.80771, 0.92123, 0.8146, 0.90705, 0.78933, 0.87963, 0.77248, 0.85884, 0.77401, 0.80576, 0.76176, 0.78307, 0.72423, 0.73959, 0.68441, 0.72131, 0.65914, 0.70682, 0.69207, 0.69736, 0.71121, 0.68822, 0.76176, 0.65576, 0.78014, 0.61764, 0.77324, 0.58171, 0.73955, 0.53543, 0.70202, 0.51778, 0.66143, 0.50045, 0.6285, 0.48375, 0.58638, 0.47619, 0.56494, 0.47619, 0.55715, 0.47004, 0.62133, 0.45836, 0.68862, 0.44397, 0.72371, 0.42669, 0.76193, 0.43701, 0.80328, 0.43675, 0.87471, 0.42953, 0.95617, 0.41097, 0.97747, 0.39318, 0.99376, 0.36946, 0.98061, 0.34007, 0.93173, 0.32512, 0.90044, 0.31942, 0.88158, 0.32114, 0.89034, 0.29824, 0.93684, 0.29804, 0.98134, 0.28014, 0.98634, 0.2573, 0.96284, 0.21286, 0.93234, 0.18612, 0.92184, 0.18283, 0.92384, 0.16863, 0.91984, 0.16349, 0.95984, 0.15305, 0.98234, 0.1261, 0.96084, 0.08824, 0.89068, 0.05764, 0.80568, 0.04077, 0.77518, 0.03562, 0.75418, 0.0204, 0.64158, 0.01857, 0.57208, 0.01877, 0.54284, 0.00345, 0.49287, 0.00015, 0.42772, 0.00932, 0.39917, 0.02622, 0.38846, 0.04054, 0.36258, 0.01924, 0.29386, 0.01079, 0.19302, 0.01924, 0.18321, 0.04421, 0.20552, 0.06587, 0.18677, 0.08754, 0.20016, 0.10479, 0.12661, 0.13186, 0.10519, 0.14912, 0.10787, 0.16894, 0.08199, 0.15095, 0.06503, 0.16087, 0.06503, 0.17482, 0.01773, 0.16821, 0.01059, 0.17776, 0.02398, 0.18767, 0.01863, 0.19758, 0.04183, 0.19685, 0.00181, 0.22342, 0.00644, 0.25726, 0.0215, 0.29681, 0.05045, 0.31492, 0.05045, 0.36779, 0.09446, 0.41449, 0.15006, 0.45786, 0.24735, 0.4936, 0.31453, 0.50647, 0.33885, 0.50409, 0.28673, 0.52077, 0.27283, 0.54221, 0.22998, 0.56127, 0.17554, 0.59511, 0.16048, 0.64277, 0.20681, 0.6604, 0.24851, 0.67422, 0.25893, 0.70928, 0.25893, 0.72263, 0.22882, 0.79554, 0.21144, 0.85749, 0.21608, 0.91182, 0.27283, 0.94089, 0.2404, 0.96663, 0.2404, 0.99045, 0.68979, 0.99045], "triangles": [59, 61, 60, 62, 61, 59, 64, 63, 62, 57, 62, 59, 57, 59, 58, 46, 43, 47, 45, 43, 46, 45, 44, 43, 67, 66, 65, 69, 68, 67, 70, 69, 67, 72, 71, 70, 41, 43, 42, 73, 72, 70, 75, 73, 70, 74, 73, 75, 77, 76, 75, 54, 56, 55, 52, 54, 53, 52, 56, 54, 39, 38, 35, 79, 78, 77, 40, 39, 41, 38, 37, 35, 41, 49, 43, 43, 48, 47, 43, 49, 48, 39, 35, 41, 35, 37, 36, 75, 67, 77, 67, 79, 77, 35, 49, 41, 67, 80, 79, 52, 57, 56, 52, 51, 49, 49, 51, 50, 32, 30, 34, 30, 32, 31, 30, 29, 34, 75, 70, 67, 80, 22, 81, 52, 35, 57, 49, 35, 52, 35, 62, 57, 35, 64, 62, 65, 64, 67, 35, 34, 67, 64, 35, 67, 34, 24, 67, 24, 80, 67, 34, 33, 32, 34, 27, 24, 29, 28, 34, 28, 27, 34, 26, 24, 27, 25, 24, 26, 80, 24, 22, 81, 21, 82, 24, 23, 22, 22, 21, 81, 85, 83, 82, 21, 85, 82, 94, 93, 9, 9, 16, 14, 84, 83, 85, 85, 21, 20, 15, 14, 16, 92, 88, 87, 17, 16, 85, 18, 17, 20, 20, 19, 18, 17, 85, 20, 85, 16, 87, 87, 86, 85, 16, 9, 87, 8, 94, 9, 13, 11, 14, 13, 12, 11, 14, 11, 9, 9, 11, 10, 92, 91, 89, 90, 89, 91, 92, 89, 88, 9, 93, 92, 9, 92, 87, 94, 6, 95, 98, 97, 96, 95, 6, 5, 8, 6, 94, 7, 6, 8, 4, 98, 5, 5, 96, 95, 5, 98, 96, 1, 3, 2, 0, 98, 4, 3, 0, 4, 1, 0, 3, 100, 99, 98, 101, 98, 0, 100, 98, 101], "vertices": [2, 12, 20.45, -8.09, 0.8844, 13, 45.87, -61.08, 0.1156, 2, 12, 2.27, -19.49, 0.66388, 13, 64.98, -70.84, 0.33612, 2, 12, 1.1, -24.92, 0.64155, 13, 70.24, -69.06, 0.35845, 2, 12, 4.94, -35.5, 0.53923, 13, 77.37, -60.34, 0.46077, 3, 12, 7.48, -43.53, 0.41607, 13, 82.95, -54.04, 0.58314, 14, -57.25, -5.57, 0.00079, 3, 12, 6.93, -63.9, 0.18456, 13, 100.73, -44.07, 0.77399, 14, -40.58, -17.29, 0.04145, 3, 12, 8.74, -72.64, 0.12447, 13, 107.31, -38.03, 0.77562, 14, -32.3, -20.62, 0.09991, 4, 12, 14.38, -89.5, 0.04226, 13, 118.88, -24.55, 0.58496, 14, -15.13, -25.24, 0.3711, 15, -22.59, -52.34, 0.00168, 4, 12, 20.53, -96.77, 0.01747, 13, 121.98, -15.55, 0.36361, 14, -5.68, -24.14, 0.60066, 15, -15.55, -45.93, 0.01826, 4, 12, 24.46, -102.65, 0.00413, 13, 125.01, -9.16, 0.12205, 14, 1.39, -24.11, 0.80114, 15, -9.82, -41.79, 0.07268, 4, 12, 19.32, -106.66, 0.00079, 13, 131.08, -11.51, 0.03518, 14, 1.89, -30.61, 0.80729, 15, -5.62, -46.77, 0.15674, 4, 12, 16.42, -110.38, 0.00011, 13, 135.76, -12.09, 0.01225, 14, 3.39, -35.08, 0.77847, 15, -1.8, -49.53, 0.20916, 2, 14, 9.95, -48.39, 0.62334, 15, 11.29, -56.51, 0.37666, 2, 14, 20.71, -58.43, 0.4666, 15, 25.89, -58.38, 0.5334, 2, 14, 32.64, -64.72, 0.32573, 15, 39.25, -56.53, 0.67427, 3, 14, 49.94, -69.74, 0.17395, 15, 56.23, -50.51, 0.80922, 18, -16.22, -50.51, 0.01682, 3, 14, 58.59, -68.46, 0.12089, 15, 62.51, -44.42, 0.83348, 18, -9.94, -44.42, 0.04562, 3, 14, 67.39, -66.77, 0.06931, 15, 68.67, -37.92, 0.81517, 18, -3.78, -37.92, 0.11552, 3, 14, 75.44, -65.98, 0.03976, 15, 74.74, -32.58, 0.73632, 18, 2.29, -32.58, 0.22392, 3, 14, 81.39, -62.05, 0.02129, 15, 77.29, -25.92, 0.59944, 18, 4.84, -25.92, 0.37927, 3, 14, 83.2, -59.25, 0.01293, 15, 77.12, -22.6, 0.49264, 18, 4.67, -22.6, 0.49443, 3, 14, 85.8, -59.55, 0.00585, 15, 79.41, -21.31, 0.31873, 18, 6.96, -21.31, 0.67542, 3, 14, 83.97, -70.52, 0.00016, 15, 84.32, -31.29, 0.05755, 18, 11.87, -31.29, 0.94228, 3, 14, 82.77, -82.44, 0, 15, 90.3, -41.67, 0.00701, 18, 17.86, -41.67, 0.99299, 3, 14, 85.29, -90.71, 0, 15, 97.17, -46.92, 8e-05, 18, 24.72, -46.92, 0.99992, 2, 14, 78.67, -93.6, 0, 18, 21.03, -53.13, 1, 2, 14, 75.17, -99.12, 0, 18, 21.41, -59.65, 1, 2, 14, 71.31, -110.08, 0, 18, 24.68, -70.81, 1, 1, 18, 32.36, -83.36, 1, 1, 18, 39.33, -86.42, 1, 1, 18, 48.54, -88.6, 1, 1, 18, 59.72, -86.03, 1, 1, 18, 65.12, -78.06, 1, 1, 18, 67.09, -73.03, 1, 1, 18, 66.3, -70.08, 1, 1, 18, 75.15, -71.08, 1, 1, 18, 75.55, -78.42, 1, 1, 18, 82.72, -85.14, 1, 1, 18, 91.52, -85.55, 1, 1, 18, 108.4, -81.1, 1, 1, 18, 118.45, -75.83, 1, 1, 18, 119.64, -74.12, 1, 1, 18, 125.1, -74.2, 1, 1, 18, 127.05, -73.48, 1, 1, 18, 131.33, -79.62, 1, 1, 18, 141.82, -82.72, 1, 1, 18, 156.2, -78.7, 1, 1, 18, 167.46, -67.11, 1, 1, 18, 173.34, -53.41, 1, 1, 18, 175.11, -48.51, 1, 1, 18, 180.8, -44.94, 1, 1, 18, 180.73, -27.14, 1, 1, 18, 180.17, -16.17, 1, 1, 18, 185.85, -11.3, 1, 1, 18, 186.78, -3.35, 1, 1, 18, 182.81, 6.78, 1, 1, 18, 176.13, 11, 1, 1, 18, 170.56, 12.45, 1, 1, 18, 178.55, 16.89, 1, 2, 14, 256.11, -121.33, 0, 18, 181.32, 27.88, 1, 2, 14, 262.12, -106.22, 0, 18, 177.39, 43.66, 1, 2, 14, 254.95, -99.67, 0, 18, 167.74, 44.79, 1, 2, 14, 246.06, -98.07, 0, 18, 159.58, 40.91, 1, 2, 14, 240.72, -91.03, 0, 18, 151.14, 43.5, 1, 2, 14, 234.01, -89.18, 0, 18, 144.61, 41.1, 1, 2, 14, 231.68, -73.76, 0, 18, 133.73, 52.26, 1, 2, 14, 227.99, -67.3, 0, 18, 126.96, 55.35, 1, 2, 14, 221.38, -63.49, 0, 18, 119.37, 54.6, 1, 2, 14, 229.41, -63.85, 0, 18, 126.09, 58.98, 1, 2, 14, 227.69, -59.52, 0, 18, 122.17, 61.49, 1, 2, 14, 223.2, -56.59, 0, 18, 116.82, 61.26, 1, 2, 14, 229.42, -51.72, 0, 18, 119.03, 68.84, 1, 2, 14, 226.97, -48.77, 0, 18, 115.32, 69.8, 1, 2, 14, 222.62, -48.46, 0, 18, 111.61, 67.52, 1, 2, 14, 219.9, -45.67, 0, 18, 107.77, 68.2, 1, 2, 14, 218.13, -48.89, 0, 18, 108.21, 64.55, 1, 2, 14, 213.05, -38.01, 0, 18, 97.74, 70.43, 1, 2, 14, 201.78, -31.51, 0, 18, 84.79, 69.13, 1, 2, 14, 187.77, -25.18, 0, 18, 69.72, 66.09, 1, 2, 14, 179.45, -25.2, 0, 18, 62.97, 61.22, 1, 2, 14, 162.46, -14.09, 0, 18, 42.69, 60.33, 1, 3, 14, 143.64, -10.09, 0, 15, 97.52, 52.61, 0.00312, 18, 25.08, 52.61, 0.99688, 3, 14, 124.9, -8.32, 0, 15, 81.27, 43.1, 0.04633, 18, 8.82, 43.1, 0.95367, 3, 14, 105, -13.66, 7e-05, 15, 68.22, 27.15, 0.26094, 18, -4.22, 27.15, 0.73898, 3, 14, 95.07, -19.79, 0.00741, 15, 63.74, 16.38, 0.59029, 18, -8.71, 16.38, 0.4023, 3, 14, 93.75, -23.47, 0.01472, 15, 64.81, 12.62, 0.73985, 18, -7.63, 12.62, 0.24543, 3, 14, 92.98, -12.73, 0.10513, 15, 57.91, 20.9, 0.88423, 18, -14.53, 20.9, 0.01064, 2, 14, 87.29, -6.11, 0.24102, 15, 49.44, 22.96, 0.75898, 2, 14, 84.74, 3.85, 0.51269, 15, 41.56, 29.56, 0.48731, 2, 14, 78.31, 18, 0.87508, 15, 28.07, 37.3, 0.12492, 2, 13, 103.78, 70.72, 0.00017, 14, 64.27, 29.52, 0.99983, 3, 13, 102.04, 60.99, 0.00715, 14, 54.74, 26.89, 0.90457, 15, 3.74, 30.77, 0.08828, 3, 13, 101.18, 52.69, 0.03622, 14, 46.89, 24.08, 0.57027, 15, -0.99, 23.9, 0.39351, 3, 13, 91.02, 44.52, 0.18396, 14, 35.13, 29.72, 0.03923, 15, -13.83, 21.62, 0.77681, 3, 13, 86.83, 42, 0.27412, 14, 31.05, 32.41, 0.00157, 15, -18.72, 21.42, 0.72431, 2, 13, 60.97, 32.41, 0.75337, 15, -46, 25.44, 0.24663, 2, 13, 39.1, 22.99, 0.95875, 15, -69.71, 27.69, 0.04125, 2, 13, 21.39, 11.93, 0.99917, 15, -90.55, 26.5, 0.00083, 2, 12, 86.89, -13.2, 0.21041, 13, 16.21, -1.41, 0.78959, 2, 12, 92.16, -3.4, 0.97092, 13, 5.09, -1.91, 0.02908, 2, 12, 92.3, 5.75, 0.99799, 13, -2.84, -6.49, 0.00201, 1, 12, 21.3, 6.81, 1], "hull": 102, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 154, 156, 156, 158, 158, 160, 160, 162, 162, 164, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 176, 178, 178, 180, 180, 182, 182, 184, 184, 186, 186, 188, 188, 190, 190, 192, 192, 194, 194, 196, 196, 198, 198, 200, 200, 202, 0, 202], "width": 158, "height": 384}, "新娘": {"type": "mesh", "uvs": [0.16671, 0.90364, 0.21134, 0.90084, 0.29033, 0.91256, 0.37445, 0.90287, 0.46267, 0.91867, 0.54577, 0.91867, 0.61245, 0.89829, 0.65554, 0.91205, 0.76838, 0.9146, 0.84429, 0.89013, 0.87815, 0.85752, 0.8248, 0.76425, 0.7653, 0.69596, 0.65656, 0.63175, 0.61494, 0.61966, 0.54164, 0.6064, 0.61917, 0.53512, 0.63031, 0.50505, 0.5922, 0.48256, 0.54399, 0.46931, 0.54809, 0.44077, 0.62811, 0.41223, 0.6517, 0.39643, 0.69376, 0.39082, 0.76044, 0.37163, 0.76865, 0.34717, 0.77378, 0.31353, 0.75429, 0.30436, 0.75326, 0.29519, 0.80763, 0.2962, 0.76865, 0.23793, 0.75737, 0.19461, 0.73685, 0.17066, 0.77583, 0.14161, 0.75326, 0.12224, 0.73582, 0.11307, 0.72864, 0.09829, 0.69889, 0.06924, 0.62093, 0.0565, 0.56143, 0.05701, 0.48757, 0.03713, 0.40447, 0.03204, 0.35626, 0.05497, 0.33471, 0.07077, 0.30188, 0.06567, 0.26188, 0.07841, 0.26906, 0.09982, 0.24033, 0.12224, 0.25367, 0.13549, 0.15211, 0.13906, 0.07517, 0.1676, 0.07209, 0.21296, 0.16852, 0.2629, 0.16959, 0.27055, 0.11497, 0.30285, 0.12977, 0.3465, 0.14071, 0.3644, 0.16755, 0.38513, 0.17471, 0.39184, 0.17693, 0.40364, 0.1731, 0.41654, 0.16449, 0.4205, 0.14812, 0.42027, 0.14588, 0.43151, 0.16688, 0.44338, 0.18636, 0.4482, 0.20971, 0.44885, 0.23446, 0.44472, 0.24836, 0.4077, 0.30322, 0.44053, 0.36785, 0.45913, 0.42273, 0.47162, 0.43812, 0.46703, 0.43094, 0.48104, 0.39811, 0.48996, 0.32528, 0.49404, 0.27552, 0.51493, 0.23038, 0.5394, 0.24473, 0.57988, 0.30213, 0.58935, 0.32855, 0.60274, 0.31476, 0.62273, 0.28975, 0.64086, 0.22754, 0.68636, 0.21054, 0.72458, 0.16455, 0.77862, 0.14455, 0.81302, 0.13993, 0.87112, 0.15327, 0.8994], "triangles": [72, 70, 20, 70, 69, 20, 67, 66, 60, 60, 66, 65, 60, 65, 61, 67, 60, 68, 68, 60, 59, 63, 61, 64, 65, 64, 61, 22, 21, 20, 20, 69, 68, 63, 62, 61, 22, 20, 68, 53, 22, 68, 68, 59, 58, 22, 27, 23, 53, 68, 55, 68, 58, 57, 28, 22, 53, 48, 28, 52, 24, 23, 25, 27, 22, 28, 30, 28, 46, 32, 46, 35, 36, 35, 39, 28, 48, 46, 39, 35, 46, 37, 36, 38, 39, 46, 43, 36, 39, 38, 56, 55, 68, 25, 23, 27, 68, 57, 56, 25, 27, 26, 55, 54, 53, 39, 43, 40, 43, 42, 40, 42, 41, 40, 28, 30, 29, 30, 32, 31, 32, 30, 46, 28, 53, 52, 52, 51, 49, 51, 50, 49, 52, 49, 48, 32, 34, 33, 32, 35, 34, 48, 47, 46, 46, 44, 43, 46, 45, 44, 80, 74, 15, 74, 73, 15, 15, 73, 16, 16, 19, 18, 72, 19, 73, 73, 19, 16, 80, 79, 74, 74, 79, 75, 79, 77, 76, 79, 76, 75, 79, 78, 77, 16, 18, 17, 71, 70, 72, 19, 72, 20, 13, 12, 83, 82, 81, 15, 81, 80, 15, 13, 83, 82, 13, 82, 14, 14, 82, 15, 85, 3, 86, 12, 6, 84, 85, 84, 3, 12, 84, 83, 88, 87, 1, 86, 1, 87, 5, 4, 6, 4, 3, 6, 10, 9, 8, 3, 2, 86, 8, 7, 6, 0, 88, 1, 84, 6, 3, 86, 2, 1, 10, 8, 6, 6, 12, 11, 10, 6, 11], "vertices": [2, 12, 120.6, 18.36, 0.46047, 13, -27.63, 12.64, 0.53953, 2, 12, 110.4, 16.19, 0.60287, 13, -20.89, 4.67, 0.39713, 2, 12, 91.68, 20.11, 0.97384, 13, -15.5, -13.68, 0.02616, 1, 12, 72.61, 13.98, 1, 1, 12, 51.59, 19.62, 1, 1, 12, 32.38, 18.01, 1, 1, 12, 17.76, 7.22, 1, 1, 12, 7.26, 12.79, 1, 2, 12, -18.92, 11.79, 0.99808, 13, 44.09, -107.22, 0.00192, 3, 12, -35.52, -1.07, 0.96857, 13, 63.27, -115.77, 0.03071, 14, -120.98, -17.84, 0.00072, 3, 12, -42.07, -16.9, 0.92553, 13, 80.32, -114.07, 0.07005, 14, -111.68, -32.23, 0.00442, 3, 12, -26.1, -59.27, 0.56983, 13, 110.11, -79.97, 0.34595, 14, -67.73, -43.16, 0.08422, 3, 12, -9.67, -89.9, 0.24341, 13, 129.34, -51.03, 0.44997, 14, -33.2, -47.05, 0.30661, 4, 12, 18.01, -117.46, 0.03676, 13, 140.55, -13.6, 0.16082, 14, 5.21, -39.92, 0.79677, 15, 1.01, -53.92, 0.00565, 4, 12, 28.11, -122.02, 0.01606, 13, 139.8, -2.55, 0.07075, 14, 14.7, -34.21, 0.88, 15, 4, -43.24, 0.0332, 3, 12, 45.42, -125.8, 0.00058, 14, 27.65, -22.11, 0.67489, 15, 5.12, -25.56, 0.32453, 2, 14, 42.31, -55.59, 0.02519, 15, 38.76, -39.87, 0.97481, 2, 14, 52.05, -66.03, 0.00329, 15, 53, -40.78, 0.99671, 2, 15, 62.4, -30.76, 0.9943, 18, -18.4, -26.62, 0.0057, 2, 15, 67.22, -18.93, 0.86075, 18, -10.4, -16.65, 0.13925, 2, 15, 80.57, -18.3, 0.23624, 18, 2.57, -19.86, 0.76376, 2, 15, 95.99, -35.17, 0.00137, 18, 12.55, -40.42, 0.99863, 1, 18, 18.89, -47.07, 1, 1, 18, 19.8, -57.13, 1, 1, 18, 26, -73.9, 1, 1, 18, 36.94, -77.72, 1, 1, 18, 52.21, -81.57, 1, 1, 18, 57.2, -77.84, 1, 1, 18, 61.47, -78.33, 1, 1, 18, 58.85, -90.68, 1, 1, 18, 87.21, -86.4, 1, 1, 18, 107.59, -87.26, 1, 1, 18, 119.42, -84.48, 1, 1, 18, 131.25, -95.7, 1, 1, 18, 141.05, -92.07, 1, 1, 18, 145.96, -88.82, 1, 1, 18, 153.05, -88.35, 1, 1, 18, 167.59, -83.86, 1, 1, 18, 176.53, -67.04, 1, 1, 18, 178.65, -53.4, 1, 1, 18, 190.71, -38.09, 1, 1, 18, 196.33, -19.5, 1, 1, 18, 187.68, -6.66, 1, 1, 18, 181.26, -0.48, 1, 1, 18, 184.9, 6.62, 1, 1, 18, 180.62, 16.78, 1, 1, 18, 170.49, 16.84, 1, 1, 18, 161.3, 25.19, 1, 1, 18, 154.68, 23.19, 1, 1, 18, 157.04, 46.7, 1, 1, 18, 146.95, 66.55, 1, 1, 18, 126.19, 70.86, 1, 1, 18, 99.4, 52.78, 1, 1, 18, 95.84, 53.15, 1, 1, 18, 83.13, 68.2, 1, 1, 18, 62.46, 68.28, 1, 1, 18, 53.8, 67.21, 1, 1, 18, 43.2, 62.72, 1, 1, 18, 39.83, 61.61, 1, 1, 18, 34.31, 62.04, 1, 1, 18, 28.52, 63.94, 1, 1, 18, 27.04, 66.23, 1, 1, 18, 27.79, 69.95, 1, 1, 18, 22.71, 71.36, 1, 1, 18, 16.41, 67.5, 1, 1, 18, 13.42, 63.43, 1, 1, 18, 12.19, 58.14, 1, 1, 18, 13.11, 52.15, 1, 2, 15, 87.92, 51.96, 0.00013, 18, 29.43, 45.99, 0.99987, 2, 15, 73.99, 38.12, 0.024, 18, 12.35, 36.1, 0.976, 2, 15, 67.13, 22.21, 0.16964, 18, 1.23, 22.81, 0.83036, 2, 15, 62.84, 8.88, 0.61097, 18, -6.68, 11.25, 0.38903, 2, 15, 65.39, 5.59, 0.81339, 18, -5.17, 7.37, 0.18661, 2, 15, 58.69, 6.47, 0.9938, 18, -11.34, 10.12, 0.0062, 1, 15, 53.66, 13.54, 1, 1, 15, 49.78, 30.1, 1, 1, 15, 38.73, 40.41, 1, 1, 15, 26.15, 49.47, 1, 2, 14, 77.23, 26.82, 0.00255, 15, 7.63, 44.05, 0.99745, 3, 13, 115.65, 67.02, 0.00772, 14, 65.56, 19.06, 0.06372, 15, 4.46, 30.4, 0.92856, 3, 13, 112.13, 58.45, 0.04455, 14, 56.32, 18.28, 0.27017, 15, -1.72, 23.5, 0.68527, 3, 13, 101.53, 55.91, 0.16526, 14, 49.22, 26.55, 0.5526, 15, -12.56, 24.64, 0.28213, 3, 13, 90.98, 55.7, 0.31769, 14, 44.22, 35.84, 0.58751, 15, -22.58, 27.97, 0.0948, 3, 13, 65.16, 56.06, 0.63532, 14, 32.74, 58.96, 0.36354, 15, -46.79, 36.92, 0.00114, 2, 13, 48.04, 49.64, 0.80307, 14, 19.22, 71.26, 0.19693, 2, 13, 21.07, 44.85, 0.95442, 14, 2.63, 93.07, 0.04558, 3, 12, 129.27, -23.38, 0.02025, 13, 5.07, 40, 0.96594, 14, -9, 105.08, 0.01381, 3, 12, 128.06, 3.75, 0.30107, 13, -18.27, 26.12, 0.69874, 14, -32, 119.51, 0.00019, 2, 12, 123.88, 16.65, 0.44646, 13, -27.67, 16.33, 0.55354], "hull": 89, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 124, 126, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 152, 154, 156, 158, 158, 160, 160, 162, 164, 166, 166, 168, 168, 170, 170, 172, 172, 174, 174, 176, 0, 176, 154, 156, 162, 164, 118, 120, 120, 122, 122, 124, 130, 132, 132, 134, 126, 128, 128, 130], "width": 232, "height": 467}}, "眼皮": {"眼皮": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 10, 48.67, -17.3, 1, 1, 10, -80.33, -17.3, 1, 1, 10, -80.33, 26.7, 1, 1, 10, 48.67, 26.7, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 129, "height": 44}}, "阴影": {"阴影": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [218, -9, -132, -9, -132, 52, 218, 52], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 350, "height": 61}}}}], "animations": {"animation": {"slots": {"眼皮": {"twoColor": [{"light": "ffffff00", "dark": "000000"}, {"time": 0.0667, "light": "ffffffff", "dark": "000000", "curve": "stepped"}, {"time": 0.1333, "light": "ffffffff", "dark": "000000"}, {"time": 0.2, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 0.2333, "light": "ffffff00", "dark": "000000"}, {"time": 0.3, "light": "ffffffff", "dark": "000000", "curve": "stepped"}, {"time": 0.3667, "light": "ffffffff", "dark": "000000"}, {"time": 0.4333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 1.7333, "light": "ffffff00", "dark": "000000"}, {"time": 1.8, "light": "ffffffff", "dark": "000000", "curve": "stepped"}, {"time": 1.8667, "light": "ffffffff", "dark": "000000"}, {"time": 1.9333, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 1.9667, "light": "ffffff00", "dark": "000000"}, {"time": 2.0333, "light": "ffffffff", "dark": "000000", "curve": "stepped"}, {"time": 2.1, "light": "ffffffff", "dark": "000000"}, {"time": 2.1667, "light": "ffffff00", "dark": "000000"}]}, "新娘眼皮": {"twoColor": [{"light": "ffffffff", "dark": "000000"}, {"time": 0.0667, "light": "ffffff00", "dark": "000000"}, {"time": 1.0667, "light": "ffffff00", "dark": "000000", "curve": "stepped"}, {"time": 1.1, "light": "ffffff00", "dark": "000000"}, {"time": 1.1667, "light": "ffffffff", "dark": "000000"}, {"time": 1.2333, "light": "ffffff00", "dark": "000000"}]}}, "bones": {"target2": {"translate": [{}, {"time": 0.2333, "x": 1.3, "y": -10.4}, {"time": 0.4333, "x": -7.15, "y": -9.75}, {"time": 0.6333, "x": -13, "y": -3.25}, {"time": 0.8333}, {"time": 1.0667, "x": 1.3, "y": -10.4}, {"time": 1.2667, "x": -7.15, "y": -9.75}, {"time": 1.4667, "x": -13, "y": -3.25}, {"time": 1.6667}, {"time": 1.9, "x": 1.3, "y": -10.4}, {"time": 2.1, "x": -7.15, "y": -9.75}, {"time": 2.3, "x": -13, "y": -3.25}, {"time": 2.5}, {"time": 2.7333, "x": 1.3, "y": -10.4}, {"time": 2.9333, "x": -7.15, "y": -9.75}, {"time": 3.1333, "x": -13, "y": -3.25}, {"time": 3.3333}]}, "bone4": {"translate": [{}, {"time": 0.2667, "x": 3.11, "y": -11.67}, {"time": 0.6667, "x": -15.22, "y": -13.31}, {"time": 0.8333, "x": -2.22, "y": -4.12}, {"time": 1.0667, "x": 7.26, "y": -8.11}, {"time": 1.3, "x": -6.21, "y": -15.6}, {"time": 1.4667, "x": -19.62, "y": -11.54}, {"time": 1.5667, "x": -11.7, "y": -9.11}, {"time": 1.6667}, {"time": 1.9333, "x": 3.11, "y": -11.67}, {"time": 2.3333, "x": -15.22, "y": -13.31}, {"time": 2.5, "x": -2.22, "y": -4.12}, {"time": 2.7333, "x": 7.26, "y": -8.11}, {"time": 2.9667, "x": -6.21, "y": -15.6}, {"time": 3.1333, "x": -19.62, "y": -11.54}, {"time": 3.2333, "x": -11.7, "y": -9.11}, {"time": 3.3333}]}, "target11": {"translate": [{}, {"time": 0.4, "x": 11.03, "y": -12.98}, {"time": 0.8667, "x": -2.6, "y": -2.6}, {"time": 1.4, "x": 7.79, "y": -7.79}, {"time": 1.7, "x": 13.41, "y": -0.75}, {"time": 1.8667, "x": 14.12, "y": -27.32}, {"time": 2.3, "x": -3.85, "y": 5.22}, {"time": 2.7667, "x": 18.17, "y": -9.74}, {"time": 3.3333}]}, "target6": {"translate": [{}, {"time": 0.3333, "x": 4.17, "y": -11.46}, {"time": 0.6667, "x": -16.67, "y": -2.08}, {"time": 1, "y": -7.29}, {"time": 1.3333, "x": -10.94, "y": -3.65}, {"time": 1.6667}, {"time": 2, "x": 4.17, "y": -11.46}, {"time": 2.3333, "x": -16.67, "y": -2.08}, {"time": 2.6667, "y": -7.29}, {"time": 3, "x": -10.94, "y": -3.65}, {"time": 3.3333}]}, "target9": {"translate": [{}, {"time": 0.3333, "x": -0.93, "y": -13.54}, {"time": 0.6667, "x": -16.12, "y": -3.79}, {"time": 1, "x": 31.13, "y": -14.01}, {"time": 1.3333, "x": -14.69, "y": -7.11}, {"time": 1.6667}, {"time": 2, "x": 3.47, "y": -17.94}, {"time": 2.3333, "x": -16.12, "y": -3.79}, {"time": 2.6667, "x": 12.06, "y": -11.08}, {"time": 3, "x": -14.69, "y": -7.11}, {"time": 3.3333}]}, "bone21": {"scale": [{}, {"time": 0.0667, "x": 1.162, "y": 1.162}, {"time": 0.1333}, {"time": 0.2, "x": 1.162, "y": 1.162}, {"time": 0.2667, "curve": "stepped"}, {"time": 1.8667}, {"time": 1.9333, "x": 1.162, "y": 1.162}, {"time": 2}, {"time": 2.0667, "x": 1.162, "y": 1.162}, {"time": 2.1333, "curve": "stepped"}, {"time": 3.0667}, {"time": 3.1333, "x": 1.162, "y": 1.162}, {"time": 3.2}, {"time": 3.2667, "x": 1.162, "y": 1.162}, {"time": 3.3333}]}, "bone12": {"rotate": [{}, {"time": 0.5, "angle": -5.49}, {"time": 0.8333, "angle": -2.73}, {"time": 1.1667, "angle": -13.88}, {"time": 1.6667, "angle": -10.07}, {"time": 2.1667, "angle": -3.71}, {"time": 2.6667, "angle": 0.87}, {"time": 3.3333}]}, "bone23": {"translate": [{}, {"time": 0.0667, "x": 20.58, "y": -0.57, "curve": "stepped"}, {"time": 1.1, "x": 20.58, "y": -0.57}, {"time": 1.1667}, {"time": 1.2333, "x": 20.58, "y": -0.57}]}, "bone11": {"rotate": [{}, {"time": 0.5, "angle": 7.05}, {"time": 1.1667, "angle": -4.1}, {"time": 1.6667, "angle": 5.6}, {"time": 2.1667, "angle": 0.63}, {"time": 2.6667, "angle": 4.87}, {"time": 3.3333}]}, "bone20": {"translate": [{}, {"time": 0.3333, "x": 5.62, "y": 0.54}, {"time": 0.6333, "x": 7.6, "y": 2.64}, {"time": 1.0667, "x": 6.42, "y": 10.22}, {"time": 1.3, "x": 6.72, "y": -0.51}, {"time": 1.9333, "x": 12.81, "y": 1.21}, {"time": 3.3333}]}}, "deform": {"default": {"新娘": {"新娘": [{"offset": 38, "vertices": [1.8338, 0.06579, -0.9268, 1.58378, 0.97454, 1.55492, 0.36671, 0.01316, -0.18535, 0.31675, 0.19491, 0.31104, 0.3535, 0.37992, -0.5021, 0.13141, -0.11604, 0.50598, -0.39075, 0.34161, 1.06073, 1.13976, -1.50632, 0.39419, -0.34818, 1.51765, -1.17221, 1.02486]}, {"time": 0.4, "offset": 38, "vertices": [4.76784, 0.17105, -2.72333, 3.91735, 3.31045, 3.4355, 6.27161, 6.47902, -8.842, 1.76942, 0.00949, 9.01717, -1.76913, 14.44587, -11.1921, -9.30316, -11.30876, 9.16098, -13.90109, -4.30944, 4.39711, 9.91406, -10.71686, -1.66531, -3.72511, 10.18541, -10.53798, 2.56461]}, {"time": 3.3333, "offset": 38, "vertices": [1.8338, 0.06579, -0.9268, 1.58378, 0.97454, 1.55492, 0.36671, 0.01316, -0.18535, 0.31675, 0.19491, 0.31104, 0.3535, 0.37992, -0.5021, 0.13141, -0.11604, 0.50598, -0.39075, 0.34161, 1.06073, 1.13976, -1.50632, 0.39419, -0.34818, 1.51765, -1.17221, 1.02486]}]}, "新娘眼皮": {"新娘眼皮": [{"time": 1.1, "vertices": [0.35681, 0.88037, 0.47579, 0.77057, 0.44344, 0.5965, 0.32957, 0.41034, 0.17873, 0.30896, -0.00747, 0.31476, 0.02847, 0.04037, 0.1493, 0.02289, 0.29759, -0.1637, 0.2892, -0.39087, 0.21831, -0.65671, 0.0762, -0.96078, -0.01593, -1.10449, -0.35732, -0.98056, -0.43659, -0.79422, -0.40299, -0.34689, -0.37825, -0.28964, -0.28988, 0.06076, -0.24483, 0.55588, -0.19126, 0.70172, -0.19865, 0.78354, -0.13105, 1.00473, -0.10672, 0.922, -0.07991, 1.02283, -0.03539, 1.04599, 0.05728, 1.05759, 0.07657, 0.9183, 0.13834, 0.8653, 0.28503, 0.95111]}]}, "大臂膀": {"大臂膀": [{"time": 0.2333, "vertices": [0.47917, 3.74699, -1.45999, 7.83598]}, {"time": 0.2667, "vertices": [-3.30888, 4.17666, -3.68271, 6.67371, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, -4.60439, -0.60506]}, {"time": 0.6333, "vertices": [0.0096, -2.68381, -1.45999, 7.83598]}, {"time": 0.9333, "vertices": [-0.58623, -0.24454, -0.01195, 10.26764]}, {"time": 1.1667, "vertices": [-1.04965, 1.65266, -1.45999, 7.83598]}, {"time": 1.6667, "vertices": [0.71162, 3.1927, -1.45999, 7.83598]}, {"time": 1.7333, "vertices": [1.45345, 3.24442, -0.18575, 8.26076, -0.37999, -0.0086, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.35446, -0.60532]}, {"time": 2, "vertices": [-2.66229, 0.76519, -3.75151, 3.74417, -3.71278, 0.34673, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1.77228, -3.02658]}, {"time": 2.3, "vertices": [1.48041, -3.67291, -0.71289, -1.07455, -3.60991, -0.08169, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.36734, -5.7505]}, {"time": 2.6333, "vertices": [3.65706, 0.56469, -0.3856, 3.54685, -4.30055, 3.32085, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.36734, -5.7505]}, {"time": 2.9667, "vertices": [3.2787, -1.88623, -3.15909, 0.21095, -8.61864, 2.98367, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 3.8213, -2.80931]}, {"time": 3.2, "vertices": [9.2551, -0.16303, 1.75977, -0.42903, -8.71457, 2.2459, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.94325, 0.04584]}, {"time": 3.3333, "vertices": [9.29161, -4.82233, 1.96564, -1.4306, -6.84559, 1.21781, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4.94325, 0.04584]}]}, "头发头纱": {"头发头纱": [{"time": 1.1667, "offset": 4, "vertices": [-4.63834, 0.38098]}]}, "图层 208": {"图层 647": [{"offset": 4, "vertices": [-3.98409, -3.91867]}, {"time": 1.1667, "offset": 4, "vertices": [-2.46797, -6.04813]}]}}}}}}