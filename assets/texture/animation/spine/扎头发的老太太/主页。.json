{"skeleton": {"hash": "0syqvKpnNJ4iQ4fzaoDzwPTB88A", "spine": "3.8.75", "x": -113.83, "y": -40, "width": 214.71, "height": 378.81, "images": "./images/扎头发的老太太/", "audio": "D:/BaiduNetdiskDownload/吃大席/images/扎头发的老太太"}, "bones": [{"name": "root"}, {"name": "bone", "parent": "root", "x": 9.16, "y": 8.79}, {"name": "bone2", "parent": "root", "length": 144.96, "rotation": 94.31, "x": -2.43, "y": 25.93}, {"name": "bone3", "parent": "bone2", "length": 132.63, "rotation": 9.02, "x": 144.96}], "slots": [{"name": "阴影", "bone": "root", "attachment": "阴影"}, {"name": "扎头发的老太太", "bone": "root", "attachment": "扎头发的老太太"}], "skins": [{"name": "default", "attachments": {"扎头发的老太太": {"扎头发的老太太": {"type": "mesh", "uvs": [0.36251, 0.97983, 0.43658, 1, 0.53288, 0.95702, 0.52493, 0.91577, 0.50374, 0.90142, 0.48709, 0.90716, 0.50374, 0.83099, 0.53856, 0.92437, 0.54764, 0.93616, 0.55319, 0.97441, 0.61072, 0.99736, 0.70811, 0.97855, 0.75151, 0.94859, 0.74697, 0.91258, 0.73789, 0.89314, 0.69752, 0.89314, 0.70761, 0.77599, 0.77064, 0.74356, 0.77064, 0.59504, 0.8556, 0.49252, 0.90405, 0.43802, 0.91263, 0.46097, 0.98126, 0.44949, 0.9752, 0.39371, 0.99639, 0.37203, 0.99942, 0.35801, 0.95905, 0.33697, 0.93483, 0.16596, 0.89698, 0.14907, 0.83239, 0.15162, 0.80817, 0.17138, 0.80817, 0.18954, 0.8657, 0.33392, 0.8457, 0.34006, 0.83309, 0.3748, 0.8341, 0.38882, 0.81643, 0.39137, 0.79524, 0.41974, 0.7251, 0.47325, 0.68826, 0.50193, 0.6489, 0.51373, 0.64688, 0.48727, 0.71149, 0.46598, 0.75327, 0.44097, 0.80219, 0.39618, 0.81872, 0.35743, 0.80564, 0.31402, 0.7893, 0.29963, 0.78583, 0.23299, 0.75957, 0.18161, 0.75957, 0.12717, 0.74224, 0.07242, 0.67536, 0.04457, 0.62087, 0.02768, 0.48063, 0.01588, 0.41376, 0.01963, 0.36026, 0.02307, 0.31964, 0.00211, 0.26317, 0.00055, 0.19084, 0.00524, 0.09276, 0.04059, 0.01433, 0.09102, 0.00641, 0.1439, 0.0287, 0.1805, 0.00988, 0.22962, 0.00542, 0.30699, 0.03464, 0.36049, 0.09954, 0.41054, 0.15898, 0.43307, 0.2001, 0.44527, 0.19857, 0.45697, 0.23176, 0.54551, 0.23176, 0.55552, 0.2508, 0.68886, 0.26971, 0.75209, 0.29213, 0.76625, 0.34319, 0.92186], "triangles": [2, 5, 3, 3, 5, 4, 5, 2, 1, 1, 0, 5, 11, 10, 8, 0, 76, 5, 11, 8, 15, 12, 15, 13, 13, 15, 14, 15, 12, 11, 8, 10, 9, 8, 7, 15, 15, 7, 6, 5, 76, 6, 6, 76, 75, 15, 6, 16, 16, 6, 73, 18, 17, 16, 75, 73, 6, 75, 74, 73, 40, 18, 16, 40, 16, 73, 40, 39, 18, 73, 72, 40, 18, 39, 19, 19, 39, 38, 72, 71, 40, 71, 41, 40, 71, 70, 41, 38, 37, 19, 19, 37, 20, 35, 20, 37, 35, 37, 36, 70, 69, 41, 42, 41, 47, 43, 42, 47, 21, 20, 22, 20, 23, 22, 41, 69, 47, 53, 52, 49, 69, 54, 49, 47, 49, 48, 49, 47, 69, 49, 52, 50, 20, 35, 23, 34, 32, 23, 23, 32, 26, 33, 32, 34, 53, 49, 54, 54, 69, 55, 68, 56, 55, 69, 68, 55, 56, 68, 66, 44, 43, 47, 68, 67, 66, 60, 56, 63, 45, 44, 47, 23, 26, 24, 46, 45, 47, 23, 35, 34, 24, 26, 25, 65, 64, 66, 66, 64, 56, 63, 56, 64, 32, 27, 26, 31, 27, 32, 27, 31, 28, 31, 29, 28, 60, 59, 56, 56, 58, 57, 56, 59, 58, 31, 30, 29, 52, 51, 50, 63, 62, 60, 60, 62, 61], "vertices": [1, 2, -19.39, 35.83, 1, 1, 2, -27.48, 20.4, 1, 1, 2, -14.38, -1.45, 1, 1, 2, -0.18, -0.8, 1, 1, 2, 5.05, 3.4, 1, 1, 2, 3.37, 7.13, 1, 1, 2, 29.07, 1.59, 1, 1, 2, -3.34, -3.51, 1, 1, 2, -7.51, -5.17, 1, 1, 2, -20.64, -5.38, 1, 1, 2, -29.4, -17.18, 1, 1, 2, -24.57, -38.64, 1, 1, 2, -15.06, -48.76, 1, 1, 2, -2.7, -48.71, 1, 1, 2, 4.08, -47.25, 1, 1, 2, 4.73, -38.55, 1, 1, 2, 44.52, -43.74, 1, 1, 2, 54.56, -58.15, 1, 1, 2, 105.21, -61.97, 1, 1, 2, 138.79, -82.9, 1, 1, 2, 156.59, -94.74, 1, 1, 2, 148.63, -95.99, 1, 1, 2, 151.42, -111.07, 1, 1, 2, 170.55, -111.2, 1, 1, 2, 177.6, -116.32, 1, 1, 2, 182.33, -117.33, 1, 1, 2, 190.16, -109.18, 1, 1, 2, 248.87, -108.36, 1, 1, 2, 255.25, -100.64, 1, 1, 2, 255.43, -86.66, 1, 1, 2, 249.08, -80.94, 1, 1, 2, 242.89, -80.47, 1, 1, 2, 192.71, -89.15, 1, 1, 2, 190.95, -84.69, 1, 1, 2, 179.3, -81.08, 1, 1, 2, 174.5, -80.93, 1, 1, 2, 173.92, -77.06, 1, 1, 2, 164.59, -71.77, 1, 2, 2, 147.48, -55.29, 0.98864, 3, -6.19, -55, 0.01136, 2, 2, 138.3, -46.61, 0.94318, 3, -13.9, -44.99, 0.05682, 2, 2, 134.91, -37.83, 0.80485, 3, -15.86, -35.79, 0.19515, 2, 2, 143.97, -38.08, 0.46545, 3, -6.96, -37.45, 0.53455, 2, 2, 150.18, -52.54, 0.17, 3, -3.09, -52.71, 0.83, 2, 2, 158.03, -62.18, 0.08663, 3, 3.15, -63.46, 0.91337, 2, 2, 172.51, -73.87, 0.02543, 3, 15.62, -77.28, 0.97457, 2, 2, 185.46, -78.43, 0.0082, 3, 27.69, -83.81, 0.9918, 2, 2, 200.47, -76.73, 0.00095, 3, 42.79, -84.48, 0.99905, 2, 2, 205.65, -73.58, 0.00014, 3, 48.39, -82.18, 0.99986, 1, 3, 70.74, -86.71, 1, 1, 3, 89.15, -85.24, 1, 1, 3, 107.26, -89.54, 1, 1, 3, 126.35, -90.21, 1, 1, 3, 138.95, -78.35, 1, 1, 3, 147.28, -68.23, 1, 1, 3, 158.2, -39.69, 1, 1, 3, 160.28, -25.34, 1, 1, 3, 161.8, -13.82, 1, 1, 3, 170.8, -6.94, 1, 1, 3, 174.13, 4.81, 1, 1, 3, 176.17, 20.38, 1, 1, 3, 169.29, 43.78, 1, 1, 3, 156.42, 64.24, 1, 1, 3, 139.22, 70.08, 1, 1, 3, 125.92, 68.28, 1, 1, 3, 110.52, 76.11, 1, 1, 3, 84.99, 83.15, 1, 2, 2, 197.14, 90.53, 0.00027, 3, 65.73, 81.23, 0.99973, 2, 2, 179.02, 77.84, 0.01228, 3, 45.84, 71.54, 0.98772, 2, 2, 170.37, 65.62, 0.04928, 3, 35.39, 60.82, 0.95072, 2, 2, 165.54, 57.07, 0.11909, 3, 29.28, 53.14, 0.88091, 2, 2, 161.58, 57.7, 0.16902, 3, 25.46, 54.38, 0.83098, 2, 2, 130.85, 52.83, 0.64685, 3, -5.66, 54.39, 0.35315, 2, 2, 127.43, 53.09, 0.70144, 3, -8.99, 55.18, 0.29856, 2, 2, 81.65, 52.41, 0.99444, 3, -54.31, 61.7, 0.00556, 1, 2, 59.78, 49.97, 1, 1, 2, 54.59, 45.5, 1, 1, 2, 0.69, 38.5, 1], "hull": 77, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 144, 144, 146, 146, 148, 148, 150, 150, 152, 0, 152], "width": 216, "height": 342}}, "阴影": {"阴影": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [1, 1, 76.84, -48.79, 1, 1, 1, -87.16, -48.79, 1, 1, 1, -87.16, 47.21, 1, 1, 1, 76.84, 47.21, 1], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 164, "height": 96}}}}], "animations": {"animation": {"bones": {"bone2": {"translate": [{"time": 0.1}, {"time": 0.2333, "y": 32.48}, {"time": 0.3, "y": 38.83}, {"time": 0.4333}], "scale": [{}, {"time": 0.1, "x": 0.925}, {"time": 0.1667}, {"time": 0.2333, "x": 1.054, "curve": "stepped"}, {"time": 0.3, "x": 1.054}, {"time": 0.5}]}, "bone3": {"rotate": [{"time": 0.1}, {"time": 0.3, "angle": 5.85}, {"time": 0.5}]}, "bone": {"scale": [{"time": 0.1}, {"time": 0.3, "x": 0.892, "y": 0.892}, {"time": 0.5}]}}}}}