attribute vec4 position;
attribute vec4 color;

varying vec4 colorVarying;

void main(void){
    colorVarying = color;
    gl_Position = position;
}

// 标量
float myFloat = 1.0;
bool myBool = true;

myFloat = float(myBool);
myBool = bool(myFloat);

// 向量
vec4 myVec4 = vec4(1.0);
vec3 myVec3 = vec3(1.0, 0.0, 1.0);

vec3 temp = vec3(myVec3);
vec2 myVec2 = vec2(temp);

myVec4 = vec4(myVec2, temp, 1.0);

// 矩阵
mat3 myMat3 = mat3(1.0, 0.0, 0.0, // 列1
                    0.0, 1.0, 0.0, // 列2
                    0.0, 0.0, 1.0); // 列3

// 分量
vec3 myVec3 = vec3(1.0, 2.0, 3.0);
vec3 temp;
temp = myVec3.xyz;
temp = myVec3.xxy;
temp = myVec3.zyx;

mat4 myMat4 = mat4(1.0);
vec4 col0 = myMat4[0];
float m1_1 = myMat4[1][1];
float m2_2 = myMat4[2].z;

vec3 v, u;
float f;
v = u + f;
// v.x = u.x + f;
// v.y = u.y + f;
// v.z = u.z + f;

// 结构体
struct customStruct{
    vec4 color;
    vec2 position;
} customVertex;
// customStruct 类型的 customVertex 变量
// 初始化
customVertex = customStruct(vec4(1.0), 
                            vec2(1.0, 1.0));

// 数组
// 创建数组
float floatArray[4];
vec4 vec4Array[2];
// 基本上来说，除了uniform 变量以外，都不能使用变量索引而是应该直接使用常数获取数值
// 数组不能在定义的时候初始化

uniform float myFloat; // √
uniform float myFloat = 1.0; // × 不能直接赋值
float myFloat = 1.0; // √
const float myFloat = 1.0; // √
const float myFloat; // × 必须在定义的同时赋值
attribute float myFloat; // √
attribute float myFloat = 1.0; // × 不能直接赋值