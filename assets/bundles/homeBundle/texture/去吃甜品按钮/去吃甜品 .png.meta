{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "db2ac18e-bb09-445e-b2cf-d2644938e388", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "db2ac18e-bb09-445e-b2cf-d2644938e388@6c48a", "displayName": "去吃甜品 ", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "db2ac18e-bb09-445e-b2cf-d2644938e388", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "db2ac18e-bb09-445e-b2cf-d2644938e388@f9941", "displayName": "去吃甜品 ", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 261, "height": 69, "rawWidth": 261, "rawHeight": 69, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-130.5, -34.5, 0, 130.5, -34.5, 0, -130.5, 34.5, 0, 130.5, 34.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 69, 261, 69, 0, 0, 261, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-130.5, -34.5, 0], "maxPos": [130.5, 34.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "db2ac18e-bb09-445e-b2cf-d2644938e388@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "db2ac18e-bb09-445e-b2cf-d2644938e388@6c48a"}}