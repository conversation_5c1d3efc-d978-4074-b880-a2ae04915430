{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "1eedb07f-bf5c-4be8-a8da-b27fa772ed1a", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "1eedb07f-bf5c-4be8-a8da-b27fa772ed1a@6c48a", "displayName": "老人", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "1eedb07f-bf5c-4be8-a8da-b27fa772ed1a", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "1eedb07f-bf5c-4be8-a8da-b27fa772ed1a@f9941", "displayName": "老人", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": -2, "trimX": 0, "trimY": 4, "width": 207, "height": 196, "rawWidth": 207, "rawHeight": 200, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-103.5, -98, 0, 103.5, -98, 0, -103.5, 98, 0, 103.5, 98, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 196, 207, 196, 0, 0, 207, 0], "nuv": [0, 0, 1, 0, 0, 0.98, 1, 0.98], "minPos": [-103.5, -98, 0], "maxPos": [103.5, 98, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "1eedb07f-bf5c-4be8-a8da-b27fa772ed1a@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "1eedb07f-bf5c-4be8-a8da-b27fa772ed1a@6c48a"}}