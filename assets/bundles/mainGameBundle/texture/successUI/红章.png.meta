{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "f72d32db-1b2b-4e4b-be1f-81fdb4ee1d34", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "f72d32db-1b2b-4e4b-be1f-81fdb4ee1d34@6c48a", "displayName": "红章", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "f72d32db-1b2b-4e4b-be1f-81fdb4ee1d34", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "f72d32db-1b2b-4e4b-be1f-81fdb4ee1d34@f9941", "displayName": "红章", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 156, "height": 167, "rawWidth": 156, "rawHeight": 167, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-78, -83.5, 0, 78, -83.5, 0, -78, 83.5, 0, 78, 83.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 167, 156, 167, 0, 0, 156, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-78, -83.5, 0], "maxPos": [78, 83.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "f72d32db-1b2b-4e4b-be1f-81fdb4ee1d34@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "f72d32db-1b2b-4e4b-be1f-81fdb4ee1d34@6c48a"}}