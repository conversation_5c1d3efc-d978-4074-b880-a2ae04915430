{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "ece2b334-9269-45b9-8ba4-************", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "ece2b334-9269-45b9-8ba4-************@6c48a", "displayName": "继续游戏  ", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "ece2b334-9269-45b9-8ba4-************", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "ece2b334-9269-45b9-8ba4-************@f9941", "displayName": "继续游戏  ", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 411, "height": 151, "rawWidth": 411, "rawHeight": 151, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-205.5, -75.5, 0, 205.5, -75.5, 0, -205.5, 75.5, 0, 205.5, 75.5, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 151, 411, 151, 0, 0, 411, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-205.5, -75.5, 0], "maxPos": [205.5, 75.5, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "ece2b334-9269-45b9-8ba4-************@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "ece2b334-9269-45b9-8ba4-************@6c48a"}}