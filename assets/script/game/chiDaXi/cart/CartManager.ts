import { _decorator, Component, instantiate, Node, tween } from 'cc';
import { QueueManager } from '../queue/QueueManager';
import { ResManager } from '../../../manager/ResManager';
import { prefabNameEnums, colorIdEnum, gameStateEnum } from '../Index';
import { Cart } from './Cart';
import { LevelData_CDX } from '../data/LevelData_CDX';
import { Dish } from '../dish/Dish';
import { RuntimeData } from '../data/GameData';
const { ccclass, property } = _decorator;

@ccclass('CartManager')
export class CartManager extends Component {
    public static instance: CartManager;

    private _createCount: number;

    protected onLoad(): void {
        CartManager.instance = this;
    }
    
    // 生成指定数量的餐车
    createCart(createCount: number){
        this._createCount = createCount;

        this.servingFLag = true;

        for(let i = 0; i < this._createCount; i++){
            this.scheduleOnce(()=>{
                this.createCartToQueue();
                if(i == this._createCount - 1){
                    this.servingFLag = false;
                }
            }, 0.5 * i);
        }
    }
    // 生成餐车，并加入队列中
    createCartToQueue(){
        // 必须使用false比较
        if(QueueManager._ins.isHaveEmptyPos() === false) return;
        if(!LevelData_CDX._ins.isHaveDish()) return;

        let path = prefabNameEnums.餐车;
        let cartPre = ResManager.instance.getPrefab(path);
        let cart = instantiate(cartPre);
        cart.setParent(this.node);
        cart.setScale(0.67, 0.67, 0.67);

        QueueManager._ins.addItem(cart);
    }

    public createCartWithoutQueue() {
        if (!LevelData_CDX._ins.isHaveDish()) return;

        let path = prefabNameEnums.餐车;
        let cartPre = ResManager.instance.getPrefab(path);
        let cart = instantiate(cartPre);
        cart.setParent(this.node);
        cart.setScale(0.67, 0.67, 0.67);

        return cart;
    }

    public getShowingDishIds(){
        let allCarts = QueueManager._ins.getAllItems();
        let dishColorIds: colorIdEnum[] = [];
        allCarts.forEach(cart=>{
            dishColorIds.push(...cart.getComponent(Cart).dishes.map(dish=>dish.getComponent(Dish).colorId));
        });
        return dishColorIds;
    }

    /** 正在展示餐车上的所有盘子 */
    public getShowingDishes(){
        let allCarts = QueueManager._ins.getAllItems();
        return allCarts.map(cart=>{
            return cart.getComponent(Cart).dishes;
        }).flat();
    }

    /** 重新设置正在展示的菜品盘子颜色 */
    public resetShowingDishColor(dishColorIds: colorIdEnum[]){
        let allCarts = QueueManager._ins.getAllItems();
        let index = 0;
        allCarts.forEach(cart=>{
            cart.getComponent(Cart).dishes.forEach(dish=>{
                dish.getComponent(Dish).initDish(dishColorIds[index ++]);
            })
        });
    }

    /** 当前展示的所有餐车自动发餐 */
    public allCartAutoServing(){
        let allCarts = QueueManager._ins.getAllItems();
        this.targetCartAutoServing(allCarts);
    }

    public targetCartAutoServing(allCarts: Node[], isCheckWaitingArea = false){
        if(allCarts.length === 0) return;

        // let cart = allCarts.shift();
        // cart.getComponent(Cart).serveDish(false, ()=>{
        //     console.log("回调");
        //     this.targetCartAutoServing(allCarts);
        // });
        allCarts.forEach(cart=>{
            cart.getComponent(Cart).serveDish(isCheckWaitingArea);
        });
    }

    public get servingFLag(): boolean{
        return QueueManager._ins.getAllItems().some(item=>item.getComponent(Cart).servingFlag);
    }

    public set servingFLag(val: boolean){
        if(RuntimeData._ins.gameState == gameStateEnum.start) return;
        QueueManager._ins.getAllItems().forEach(item=>item.getComponent(Cart).servingFlag = val);
    }
}


