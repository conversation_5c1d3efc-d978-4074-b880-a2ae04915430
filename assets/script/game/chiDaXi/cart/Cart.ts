import { _decorator, Component, easing, Node, tween, UITransform, v3, Vec3 } from 'cc';
import { RuntimeData } from '../data/GameData';
import { DishManager } from '../dish/DishManager';
import { colorIdEnum, gameStateEnum } from '../Index';
import { MainGame_CDX } from '../MainGame_CDX';
import { TableManager } from '../table/TableManager';
import { Dish } from '../dish/Dish';
import { Table } from '../table/Table';
import { WaitingManager } from '../waitingArea/WaitingManager';
import { Constants } from '../data/Constants';
import { QueueManager } from '../queue/QueueManager';
import { LevelData_CDX } from '../data/LevelData_CDX';
import { AudioManager } from '../../../manager/AudioManager';
import { VoicePackageManager } from '../../../common/VoicePackageManager';
import { AdManager } from '../../../ads/AdManager';
const { ccclass, property } = _decorator;

@ccclass('Cart')
export class Cart extends Component {
    private _colorId: colorIdEnum;

    private _dishs: Node[] = [];
    public servingFlag: boolean = false;

    protected onLoad(): void {
        this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this);
    }

    start() {
        let colorId = RuntimeData._ins.getRandomColorId();
        this.initData(colorId);
    }

    initData(colorId: colorIdEnum){
        this._colorId = colorId;

        // 生成餐车上的盘子
        this.createDish();

        // 改变关卡有关
        // 判断是否是第二关之后的关卡，是的话盖住随机盘子
        // 是特殊菜关卡的话也扣住随机盘子
        let curLevel = RuntimeData._ins.curLevel;
        if(curLevel >= 2 || RuntimeData._ins.isSpecialDishLevel){
            this._closeRandomDish();
        }
    }

    createDish(){
        let rowCount = 2;
        let colCount = 2;
        let spaceX = 20;
        let spaceY = 20;
        let left = 15;
        let top = 15;
        let cartUITransform = this.node.getComponent(UITransform);
        // 希望餐车的宽度
        let contentWidth = cartUITransform.width / this.node.scale.x;
        let itemWidth = (contentWidth - left * 2 - spaceX * (colCount - 1)) / colCount;
        let startX = -contentWidth / 2 + left + itemWidth / 2;
        let startY = contentWidth / 2 - top - itemWidth / 2;

        let createCount = 0;
        let randomNum = Math.random();
        if(randomNum < 0.5){
            createCount = 2;
        }else if(randomNum < 0.9){
            createCount = 3;
        }else{
            createCount = 4;
        }

        let offsetY = 30;

        // 生成 4 个盘子，盘子颜色随机生成
        for(let i = 0; i < rowCount; i++){
            let y = startY - i * (itemWidth + spaceY);
            for(let j = 0; j < colCount; j++){
                let x = startX + j * (itemWidth + spaceX);

                if(this._dishs.length >= createCount) break;
                if(!LevelData_CDX._ins.isHaveDish()) return;
                
                let colorId = LevelData_CDX._ins.getDishColor();
                let dish = DishManager.instance.createDish(colorId);
                dish.setParent(this.node);
                dish.setPosition(x, y + offsetY);

                let dishWidth = dish.getComponent(UITransform).width;
                let dishScale = itemWidth / dishWidth * 1.19;
                dish.setScale(dishScale, dishScale);

                this._dishs.push(dish);
            }
        }
    }

    // 扣住随机数量的盘子
    private _closeRandomDish(){
        // 概率扣住盘子
        if(Math.random() < 0.5) return;

        let length = this._dishs.length;
        let closeCount = Math.random() * length / 2;
        if(closeCount < 2) closeCount = 1;
        else closeCount = Math.random() > 0.1 ? 1 : 2;
        for(let i = 0; i < closeCount; i++){
            this._dishs[length - 1 - i].getComponent(Dish).close();
        }
    }
    
    /**
     * 上菜
     * @param isCheckWaitingArea 是否检测等待区是否可以放置盘子，默认检测
     * @returns 
     */
    serveDish(isCheckWaitingArea = true, ...cbs: Function[]){
        if(cbs.length > 0) this.endServeCbs.push(...cbs);

        this.servingFlag = true;

        if(this._dishs.length <= 0){
            // console.log("盘子都用光了")
            this.checkDestory();
            return;
        }

        if(!TableManager._ins.hasEmptyTable()){
            MainGame_CDX._ins.success();
            return;
        }

        let colorsOnCart: number[] = [];
        this._dishs.forEach(dish=>{
            let colorId = dish.getComponent(Dish).colorId;
            colorsOnCart[colorId] = colorsOnCart[colorId] ? colorsOnCart[colorId] + 1 : 1;
        });

        // 餐车上的盘子是否有对应的桌子
        let targetTable: Node = null;
        let tables = TableManager._ins.getTables(true);
        tables.some(table=>{
            let tableScript = table.getComponent(Table);
            let tableColorId = tableScript.colorId;
            if(colorsOnCart[tableColorId] && colorsOnCart[tableColorId] > 0 && !tableScript.isFull){
                targetTable = table;
                return true;
            }
        });

        // if(!targetTable){
        //     console.log("没有目标桌子");
        //     tables.forEach(table => {
        //         let tableScript = table.getComponent(Table);
        //         let tableColorId = tableScript.colorId;
        //         console.log(colorsOnCart, tableColorId, colorsOnCart[tableColorId], colorsOnCart[tableColorId] > 0, !tableScript.isFull);
        //     });
        // } else {
        //     console.log("目标桌子满了：", targetTable.getComponent(Table).isFull);
        // }
        if(!targetTable || targetTable.getComponent(Table).isFull){
            if(isCheckWaitingArea){
                // 没有桌子可以上菜，并且还有盘子，等待区自动补位
                for(let i = 0; i < this._dishs.length; i++){
                    let dish = this._dishs[i];
                    let flag = WaitingManager.instance.placeItemToWaitingSlot(dish);
                    if(flag){
                        dish.getComponent(Dish).open();
                        this._dishs.splice(i, 1);
                        i--;
                    }else{
                        console.log("餐车上有剩余的盘子，游戏失败");
                        // if(GameModel.instance.releaseType == releaseType.test_TEST)
                            // alert("Cart.ts, 餐车上有剩余的盘子，游戏失败！");

                        RuntimeData._ins.gameState = gameStateEnum.pause;
                        this._dishs.forEach(dish=>{
                            dish.getComponent(Dish).flashRedLight();
                        });
                        this.scheduleOnce(() => {
                            MainGame_CDX._ins.gameOver();
                        }, 1);
                        return;
                    }
                }
            }
            this.checkDestory();
            return;
        }

        // 开始上菜
        this.serveTable(targetTable, isCheckWaitingArea);
    }

    // 服务指定桌子
    serveTable(targetTable: Node, isCheckWaitingArea = true){
        let tableScript = targetTable.getComponent(Table);
        let colorId = tableScript.colorId;

        let dish = this.getDishByColorId(colorId);
        if(!dish) {
            // 继续判定下一个桌子
            this.serveDish(isCheckWaitingArea);
            return;
        }
        // console.log("开始上菜", colorId);
        MainGame_CDX._ins.placeDishToTable(dish, targetTable);
        let isFull = targetTable.getComponent(Table).isFull;

        if (!isFull) {
            let fillTime = Constants._ins.queueFillTime;
            this.scheduleOnce(() => {
                this.serveTable(targetTable, isCheckWaitingArea);
            }, fillTime * 0.56);
        } else {
            // 继续判定下一个桌子
            this.serveDish(isCheckWaitingArea);
        }
    }

    private endServeCbs: Function[] = [];
    /** 餐车结束服务 */
    private endServe(){
        this.servingFlag = false;
        // console.log("判断等待区自动补位");
        // WaitingManager.instance.checkServing();
        MainGame_CDX._ins.touchFlag;

        this.endServeCbs.forEach(cb=>{
            cb && cb();
        });
    }

    checkDestory(){
        if(this._dishs.length <= 0){
            QueueManager._ins.removeItem(this.node);
        }else{
            this.endServe();
        }

        // 操作回调
        MainGame_CDX._ins.optOnce();
        // console.log("餐车服务结束");
    }

    /** 从盘子数组中获取一个指定颜色的盘子 */
    getDishByColorId(colorId: colorIdEnum): Node{
        let index = this._dishs.findIndex(val=>val.getComponent(Dish).colorId == colorId);
        if(index >= 0){
            return this._dishs.splice(index, 1)[0];
        }
    }

    onTouchStart(){
        if(!MainGame_CDX._ins.touchFlag) return;

        // console.log("点击桌子，上菜", this._colorId);
        // MainGame_CDX._ins.serveDish(this._colorId, this.node);
        // TableManager._ins.curOpt = this.node;
        // AudioManager.playSound("ding", 1);

        // 先掀开所有盖住的盘子
        let openTime = 0;
        this._dishs.forEach(val=>{
            let dishScript = val.getComponent(Dish);
            openTime = dishScript.open();
        });

        this.scheduleOnce(() => {
            AudioManager.playSound("so", 1);
            this.serveDish();
        }, openTime);

        // 增加开吃语音
        let voiceCount = 2;
        let playP = 0.11;
        let voiceName = "kaiChi";
        let volume = 1;
        VoicePackageManager.instance.playVoicePackage(voiceName, voiceCount, playP, volume);

        // 震动
        AdManager.shortVibrate();
    }

    get dishes(): Node[]{
        return this._dishs;
    }

    get colorId(){
        return this._colorId;
    }

    get colorIds(): colorIdEnum[] {
        return this._dishs.map(dish=>dish.getComponent(Dish).colorId);
    }
}


