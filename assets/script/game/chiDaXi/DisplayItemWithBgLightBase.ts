import { _decorator, Component, easing, instantiate, Label, Node, Sprite, tween, UITransform, v3, view } from 'cc';
import { Tools } from 'db://assets/script/common/Tools';
import { RuntimeData } from './data/GameData';
const { ccclass, property } = _decorator;

@ccclass('DisplayItemWithBgLightBase')
export class DisplayItemWithBgLightBase extends Component {
    @property(Node)
    protected itemSprNode: Node = null;
    @property(Node)
    protected maskSprNode: Node = null;
    @property(Node)
    protected itemNameSprNode: Node = null;
    @property(Node)
    protected bgLight: Node = null;

    protected start(): void {
        this.bgLight.setScale(0, 0, 0);
    }

    showBgLight(){
        tween(this.bgLight)
            .to(0.34, { scale: v3(1, 1, 1) }, { easing: easing.backInOut })
            .repeat(
                Number.MAX_SAFE_INTEGER,
                tween()
                    .by(0.34, { scale: v3(0.07, 0.07) }, { easing: easing.sineOut })
                    .by(0.23, { scale: v3(-0.07, -0.07) }, { easing: easing.sineIn })
            )
            .start();

        this.node.once(Node.EventType.TOUCH_START, () => {
            tween(this.itemSprNode)
                .to(0.34, { scale: v3(0, 0) }, { easing: easing.backIn })
                .call(() => {
                    this.node.destroy();
                })
                .start();
        })
    }

    /**
     * 
     * @param args 参数列表，第一个为itemSprName, 第二个为itemNameSprName
     */
    initData(...args: any[]){
        let [itemSprName, itemNameSprName] = args;
        if(this.itemSprNode)
            Tools.setSpriteFrame(this.itemSprNode, itemSprName);
        if(this.maskSprNode)
            Tools.setSpriteFrame(this.maskSprNode, itemSprName);
        if(this.itemNameSprNode)
            Tools.setSpriteFrame(this.itemNameSprNode, itemNameSprName);
    }
}