import { _decorator, color, Component, easing, instantiate, isValid, Label, Node, Sprite, tween, UIOpacity, UITransform, v2, v3, Vec3 } from 'cc';
import { colorIdEnum, textureNamesEnum } from '../Index';
import { Tools } from '../../../common/Tools';
import { Particle } from '../../particle/Particle';
import { DishManager } from './DishManager';
import { Table } from '../table/Table';
import { MainGame_CDX } from '../MainGame_CDX';
import { GlobalLabelNodesManager } from '../../../common/GlobalLabelNodesManager';
import { DishProductGenerator } from './DishProductGenerator';
const { ccclass, property } = _decorator;

/** 主玩法吃大席游戏内盘子的类 */
@ccclass('Dish')
export class Dish extends Component {
    private _colorId: number;
    private _dishId: number;
    private _sprNode: Node;

    private _lockedMask: Node;

    // 盘子正常摞起来的Y间隙
    private _deltaY = 10;

    private _servingFlag = false;

    private floorCountNumLabel: Label;
    private floorCountNumLabelNodePos: Vec3;

    private _lightFrameParent: Node;

    private _operable: boolean = true;
    public leaveOperableArea(){
        this._operable = false;
    }

    private _lid: Node;
    public get isCoveredByLid(){
        return this._lid && this._lid.active;
    }

    protected onLoad(): void {
        this._sprNode = this.node.getChildByName('SprNode');
        this.initDish(colorIdEnum.红色);

        this._locked();
        this.floorCountNumLabel = this.node.getChildByName("FloorCount").getComponent(Label);
        this.floorCountNumLabelNodePos = this.floorCountNumLabel.node.getPosition();

        this._lightFrameParent = this.node.getChildByName("LightFrame");
    }

    protected start(): void {
        GlobalLabelNodesManager.instance.addLabelNode(this.floorCountNumLabel.node, this.floorCountNumLabelNodePos);
    }

    initDish(colorId: colorIdEnum, floorCount?: number){
        this._colorId = colorId;
        this._dishId = 100 + this._colorId;
        let imgName = textureNamesEnum.盘子 + this._dishId;
        Tools.setSpriteFrame(this._sprNode, imgName);
        this.node.getComponent(UITransform).setContentSize(this._sprNode.getComponent(UITransform).contentSize);

        if(floorCount !== undefined){
            let showFloor = this.node.getChildByName("ShowFloor");
            showFloor.removeAllChildren();
            this.floorCount = floorCount;
            this._locked();
        }

        this._sprNode.once(Node.EventType.TOUCH_START, ()=>{
            DishProductGenerator.instance.onDishTouch(this.node);
        });
    }

    private _locked() {
        if (!this._lockedMask || !isValid(this._lockedMask)) {
            // 生成不可用遮罩
            let lockedMask = instantiate(this._sprNode);
            lockedMask.name = "LockedMask";
            lockedMask.setParent(this.node);
            lockedMask.getComponent(Sprite).color = color(0, 0, 0, 88);
            lockedMask.active = false;
            this._lockedMask = lockedMask;
        }
    }

    public get colorId(): colorIdEnum{
        return this._colorId;
    }

    /** 扣起来 */
    close(){
        // let lid = instantiate(this._sprNode);
        // lid.setParent(this._sprNode);
        // lid.setPosition(0, 0);
        // lid.setScale(1.05, 1.05);
        // lid.name = "Lid";
        // Tools.setSpriteFrame(lid, textureNamesEnum.盘子 + "lid");
        this._lid = this.node.getChildByName("Lid");
        this._lid.active = true;
    }
    /** 揭开盖子 */
    open(){
        if(!this._lid) return;

        let lid = this._lid;
        this._lid = null;
        let openTime = 0;
        if (lid) {
            // lid.setParent(this.node.parent);
            // lid.setPosition(this.node.position);
            openTime = 0.23;
            // tween(lid)
            //     .to(openTime, {position: v3(-160, 0), scale: v3(1, 0.67)}, {easing: easing.sineInOut})
            //     .by(0.23, {position: v3(500)}, {easing: easing.backIn})
            //     .call(()=>{
            //         lid.destroy();
            //     })
            //     .start();
            let particleParentNode = DishManager.instance.node;
            let particlePos = Tools.getToNodePosForNode(lid, particleParentNode);
            Particle.placeDishToTableEff(this.node.parent.parent, v3(particlePos.x, particlePos.y));
            tween(lid.addComponent(UIOpacity))
                .to(openTime, {opacity: 0}, {easing: easing.sineInOut})
                .call(()=>{
                    lid.destroy();
                })
                .start();
        }
        return openTime;
    }

    serveTabel(table: Node, dishFloorBeZeroCallback: Function, finishedCallback: Function){
        let tableScript = table.getComponent(Table);
        let dish = this.node;
        let deltaTime = 0.07;
        this._servingFlag = true;

        let surplusSeatCount = tableScript.surplusNeedDishCountByColorId(this.colorId);
        let dishFloorCount = this.floorCount;
        let serveCount = Math.min(surplusSeatCount, dishFloorCount);
        // console.log("桌子剩余座位数量：", surplusSeatCount,  "盘子层数：", dishFloorCount, "上菜数量：", serveCount)
        for (let i = 0; i < serveCount; i++) {
            this.scheduleOnce(() => {
                dish = this.reduceFloorCount();
                if (dish == this.node) {
                    dishFloorBeZeroCallback && dishFloorBeZeroCallback();
                    this._operable = false;
                }

                MainGame_CDX._ins.placeDishToTable(dish, table, this.colorId);
            }, deltaTime * i);
        }

        this.scheduleOnce(() => {
            this._servingFlag = false;
            finishedCallback && finishedCallback();
        }, deltaTime * serveCount);
    }

    removeLockedMask(){
        let lockedMask = this.node.getChildByName("LockedMask");
        if(lockedMask){
            lockedMask.destroy();
            this.showRealFloor();
        }
    }

    // 生成真实的层数，并展示动效
    public showRealFloor(): void{
        let showFloor = this.node.getChildByName("ShowFloor");
        for (let i = -1; i < this._floorCount - 1; i++) {
            let floorNode = this._sprNode;
            if (i >= 0) {
                floorNode = instantiate(this._sprNode);
                floorNode.setParent(showFloor);
                floorNode.setPosition(0, this._deltaY);
            }

            let targetY = this._deltaY * (i + 1);
            let scale = floorNode.scale.x;
            // floorNode.scale = v3(scale, scale * 0.5);
            let animTime = 0.29;
            let productGeneratorFillTime = 0.23;
            tween(floorNode)
                .delay(productGeneratorFillTime)
                .to(animTime * 3 / 4, { position: v3(0, targetY + 20) }, { easing: easing.sineOut })
                .to(animTime / 4 + 0.012 * (i - 1), { position: v3(0, targetY) }, { easing: easing.sineIn })
                .start();
        }

        this.open();
    }

    public hideRealFloor() {
        let showFloor = this.node.getChildByName("ShowFloor");
        for (let i = 1; i <= this._floorCount - 1; i++) {
            let floorNode = showFloor.children[i];

            let targetY = this._deltaY;
            let scale = floorNode.scale.x;
            // floorNode.scale = v3(scale, scale * 0.5);
            let animTime = 0.29;
            let productGeneratorFillTime = 0.23;
            tween(floorNode)
                .delay(productGeneratorFillTime)
                .to(animTime / 4, { position: v3(0, targetY + 20) }, { easing: easing.sineOut })
                .to(animTime * 3 / 4 + 0.012 * (i - 1), { position: v3(0, targetY) }, { easing: easing.sineIn })
                .start();
        }
    }
    resumeShowRealFloor() {
        let showFloor = this.node.getChildByName("ShowFloor");
        for (let i = 1; i <= this._floorCount - 1; i++) {
            let floorNode = showFloor.children[i];

            let targetY = this._deltaY * (i + 1);
            let scale = floorNode.scale.x;
            // floorNode.scale = v3(scale, scale * 0.5);
            let animTime = 0.29;
            let productGeneratorFillTime = 0.23;
            tween(floorNode)
                .delay(productGeneratorFillTime)
                .to(animTime * 3 / 4, { position: v3(0, targetY + 20) }, { easing: easing.sineOut })
                .to(animTime / 4 + 0.012 * (i - 1), { position: v3(0, targetY) }, { easing: easing.sineIn })
                .start();
        }
    }

    /** 闪红 */
    flashRedLight(){
        let flashNode = instantiate(this._sprNode);
        flashNode.setParent(this.node);
        flashNode.getComponent(Sprite).color = color(255, 0, 0);
        let opacityCom = flashNode.addComponent(UIOpacity);
        opacityCom.opacity = 0;
        tween(opacityCom)
            .to(0.12, { opacity: 255 }, { easing: easing.sineOut })
            .to(0.23, { opacity: 0 }, { easing: easing.sineIn })
            .union()
            .repeat(5)
            .start();


        let showFloor = this.node.getChildByName("ShowFloor");
        let flashNode1 = instantiate(showFloor);
        flashNode1.setParent(this.node);
        flashNode1.children.forEach(child => {
            child.getComponent(Sprite).color = color(255, 0, 0);
            let opacityCom = child.addComponent(UIOpacity);
            opacityCom.opacity = 0;
            tween(opacityCom)
                .to(0.12, { opacity: 255 }, { easing: easing.sineOut })
                .to(0.23, { opacity: 0 }, { easing: easing.sineIn })
                .union()
                .repeat(5)
                .start();
        })
    }

    private _floorCount: number = 1;
    set floorCount(floorCountNum: number){
        this._floorCount = floorCountNum;
        this.updateFloorRender();
    }
    get floorCount(): number{
        return this._floorCount;
    }

    public reduceFloorCount(val = 1): Node {
        if (this._floorCount >= val) {
            this._floorCount -= val;
        }else{
            this._floorCount = 0;
        }

        this.updateFloorRender();

        if(this._floorCount > 0){
            let dishClone = instantiate(this._sprNode);
            dishClone.setParent(this.node.parent);
            dishClone.setPosition(this.node.position.x, this.node.position.y + this._floorCount * this._deltaY);
            dishClone.setScale(this.node.scale);
            dishClone.off(Node.EventType.TOUCH_START);
            return dishClone;
        }else{
            this.removeLockedMask();
            this._sprNode.off(Node.EventType.TOUCH_START);
            return this.node;
        }
    }

    updateFloorRender() {
        let floorCount = this.floorCountNumLabel;
        floorCount.string = this._floorCount.toString();
        floorCount.node.active = this._floorCount > 1;

        let showFloor = this.node.getChildByName("ShowFloor");
        // 还没增加过层数展示，生成层数展示
        if(showFloor.children.length <= 0){
            if (this._floorCount > 1){
                // for (let i = 0; i < this._floorCount - 1; i++) {
                    let floorNode = instantiate(this._sprNode);
                    floorNode.setParent(showFloor);
                    floorNode.setPosition(0, this._deltaY);

                    floorNode.once(Node.EventType.TOUCH_START, () => {
                        DishProductGenerator.instance.onDishTouch(this.node);
                    });
                // }
            }
        }else{
            // 判断盘子是否从被压住的状态变为可操作状态

            // 已经增加过层数展示，只需要更新数量即可
            let deltaFloorNum = showFloor.children.length + 1 - this._floorCount;
            if(deltaFloorNum > 0){
                for (let i = 0; i <= deltaFloorNum; i++) {
                    showFloor.children[showFloor.children.length - 1].destroy();
                }
            }
            if(this._floorCount <= 1){
                showFloor.children[0].destroy();
            }
        }
    }

    showLightFrame() {
        if(!this._operable) return;

        let parentNode = this._lightFrameParent;
        if(parentNode == null) return;
        if(parentNode.children.length > 0) return;

        let lightNode = instantiate(this._sprNode);
        lightNode.setParent(parentNode);
        Tools.setSpriteFrame(lightNode, "dishLightFrame_10" + this.colorId);

        let showFloor = this.node.getChildByName("ShowFloor");
        let lightNode1 = instantiate(showFloor);
        lightNode1.setParent(parentNode);
        lightNode1.children.forEach(child => {
            child.setParent(parentNode);
            Tools.setSpriteFrame(child, "dishLightFrame_10" + this.colorId);
        });
        lightNode1.destroy();
    }

    hideLightFrame() {
        let parentNode = this._lightFrameParent;
        if (parentNode == null || parentNode.children.length <= 0) return false;

        parentNode.destroyAllChildren();
        // parentNode.destroy();
        // this._lightFrameParent = null;
        return true;
    }

    public get unlocked(): boolean{
        return !(this._lockedMask && isValid(this._lockedMask));
    }

    public get servingFlag(): boolean{
        return this._servingFlag;
    }

    // protected lateUpdate(dt: number): void {
    //     if(this._lightFrameParent.children.length > 0){
    //         this._lightFrameParent.children[0].setPosition(this._sprNode.position);
    //         for(let i = 1; i < this._lightFrameParent.children.length; i++){
    //             this._lightFrameParent.children[i].setPosition(this.node.getChildByName("ShowFloor").children[i - 1].position);
    //         }
    //     }
    // }
}


