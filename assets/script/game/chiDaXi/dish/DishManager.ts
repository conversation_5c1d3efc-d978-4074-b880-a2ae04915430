import { _decorator, Component, easing, find, Node, tween, v3, Vec3 } from 'cc';
import { colorIdEnum, prefabNameEnums } from '../Index';
import { Tools } from '../../../common/Tools';
import { Dish } from './Dish';
import { TableManager } from '../table/TableManager';
import { Particle } from '../../particle/Particle';
import { TaskProgress } from '../taskProgress/TaskProgress';
import { Constants } from '../data/Constants';
import { AdManager } from '../../../ads/AdManager';
const { ccclass, property } = _decorator;

@ccclass('DishManager')
export class DishManager extends Component {
    public static instance: DishManager;

    @property(TaskProgress)
    taskProgress: TaskProgress;

    protected onLoad(): void {
        DishManager.instance = this;
    }

    createDish(colorId: colorIdEnum): Node{
        let dish = Tools.newPrefab(prefabNameEnums.盘子, this.node);
        dish.getComponent(Dish).initDish(colorId);

        return dish;
    }

    // 把盘子放到指定桌子上
    public placeDishToTable(dish: Node, targetTable: Node, dishColor: colorIdEnum){
        let pos = Tools.getToNodePosForNode(dish, targetTable.parent);
        dish.setParent(targetTable.parent);
        dish.setPosition(pos.x, pos.y);
        dish.setScale(1, 1, 1);

        let targetPos = TableManager._ins.getDishPos(targetTable, dishColor);
        let moveTime = this.palceDishAnim(dish, targetPos, 0.3 * Constants._ins.tableScale);

        // 更新进度条
        this.taskProgress.updateRenderInCurLevel();

        return moveTime;
    }

    /** 
     * 放置盘子时候的飞行动作 
     * @param dish 指定的盘子
     * @param targetPos 放置到指定位置
     * @param args 飞到目标地之后的scale大小、飞行结束之后的回调函数
     */
    palceDishAnim(dish: Node, targetPos: Vec3, ...args: any[]){
        if(!targetPos) return 0;

        let [targetScale = 0.3, callback] = args;

        let moveV = Math.random() * 300 + 1500;
        let moveVector = v3();
        Vec3.subtract(moveVector, targetPos, dish.position);
        let moveDis = moveVector.length();
        let moveTime = moveDis / moveV;
        tween(dish)
            .parallel(
                tween<Node>()
                    .by(moveTime, { position: moveVector }, { easing: easing.sineOut }),
                tween<Node>()
                    .to(moveTime / 4, { scale: v3(1.5, 1.5, 1.5) }, { easing: easing.sineOut })
                    .delay(moveTime / 2)
                    .to(moveTime / 4, { scale: v3(targetScale * 0.85, targetScale * 0.85, targetScale * 0.85) }, { easing: easing.sineOut })
                    .to(moveTime / 4, { scale: v3(targetScale, targetScale, targetScale) }, { easing: easing.sineIn })
            )
            .start();

        tween(this.node)
            .delay(moveTime)
            .call(() => {
                // AudioManager.playSound("putDishPlateOnTable", 1);
                Particle.placeDishToTableEff(dish.parent, targetPos);
                // dish.destroy();
                callback && callback();
                // 震动
                AdManager.shortVibrate();
            })
            .start();

        return moveTime;
    }
}


