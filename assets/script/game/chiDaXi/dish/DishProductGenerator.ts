import { _decorator, Component, director, easing, EventTouch, find, isValid, Node, tween, UITransform, v3, Vec3, view } from 'cc';
import { Tools } from '../../../common/Tools';
import { DishManager } from './DishManager';
import { LevelData_CDX } from '../data/LevelData_CDX';
import { WaitingManager } from '../waitingArea/WaitingManager';
import { Dish } from './Dish';
import { MainGame_CDX } from '../MainGame_CDX';
import { RuntimeData } from '../data/GameData';
import { gameStateEnum } from '../Index';
import { Constants } from '../data/Constants';
import { AdManager } from '../../../ads/AdManager';
import { GuideGame } from '../../../common/GuideGame';
import { AutoGame } from '../../../common/AutoGame';
import { TableManager } from '../table/TableManager';
import { Table } from '../table/Table';
const { ccclass, property } = _decorator;

@ccclass('DishProductGenerator')
export class DishProductGenerator extends Component {
    public static instance: DishProductGenerator;

    private _items: IGrid<Node>[][] = [];
    private _rowCount: number;
    private _colCount: number;

    public bottom: number = 230;

    private _fillingCount: number = 0;
    public get fillingFlag(): boolean{
        return this._fillingCount > 0;
    }

    protected onLoad(): void {
        DishProductGenerator.instance = this;
    }

    public startGame(){
        this.initMap();

        this.createInitDishes();

        this.scheduleOnce(() => {
            this.updateRender();
        }, 0.1);
    }

    private initMap(){
        let winWidth = view.getVisibleSize().width;
        let winHeight = view.getVisibleSize().height;

        // 队列间隔
        let spaceX = 50;
        // 队列行间距
        let spaceY = 23;

        let rowCount = -1;
        let colCount = 5;
        let itemWidth = 120; // (winWidth - left * 2 - (colCount - 1) * spaceY) / colCount;
        let left = (winWidth - itemWidth * colCount - spaceX * (colCount - 1)) / 2; // 88;
        // spaceX = (winWidth - left * 2 - itemWidth * colCount) / (colCount - 1);
        let maxY = Constants._ins.waitingAreaY - 80;
        let areaHeight = maxY - (-winHeight / 2 + this.bottom);
        rowCount = Math.ceil((areaHeight + spaceY) / (itemWidth + spaceY));

        let fristX = -winWidth / 2 + left + itemWidth / 2;
        let firstY = maxY - itemWidth / 2;

        this._rowCount = rowCount;
        this._colCount = colCount;

        for(let i = 0; i < rowCount; i++){
            let y = firstY - (i * (itemWidth + spaceY));
            let rowArr: IGrid<Node>[] = [];
            for(let j = 0; j < colCount; j++){
                let x = fristX + j * (itemWidth + spaceX);
                let gridNode = new Node("GridNode");
                gridNode.setParent(this.node);
                gridNode.setPosition(x, y);
                gridNode.addComponent(UITransform).setContentSize(itemWidth, itemWidth);
                if(i == 0){
                    gridNode.getComponent(UITransform).setAnchorPoint(0.5, 1);
                    gridNode.getComponent(UITransform).setContentSize(itemWidth, 3000);
                    gridNode.setPosition(x, y + 150);
                    gridNode.on(Node.EventType.TOUCH_START, this.onGridNodeTouch, this);
                }
                rowArr.push({
                    pos: v3(x, y),
                    cellPos: [i, j],
                    item: null,
                    gridNode
                });
                // let singleSpr = Tools.newPrefab("SingleSprite", this.node, v3(x, y));
                // Tools.setSpriteFrame(singleSpr, "singleColor");
                // singleSpr.getComponent(UITransform).setContentSize(50, 50);
            }
            this._items.push(rowArr);
        }
    }

    /** 创建初始的盘子们 */
    private createInitDishes(){
        if (RuntimeData._ins.isShowGuideLevel) {
            for (let j = 1; j < 4; j++) {
                this.createDishOnGrid(0, j);
            }
            // 生成引导小手
            GuideGame.instance.updateGuideFinger();
        } else {
            for (let i = 0; i < this._rowCount; i++) {
                for (let j = 0; j < this._colCount; j++) {
                    this.createDishOnGrid(i, j);
                }
            }
        }

        // 展示外发光
        this.scheduleOnce(() => {
            for (let i = 0; i < this._colCount; i++) {
                let grid = this._items[0][i];
                grid.item?.getComponent(Dish).showLightFrame();
            }
        }, 1);
    }

    /** 重置当前屏幕内盘子（按照配置信息， 颜色、层数会改变） */
    public resetCurDishes() {
        LevelData_CDX._ins.dishColorIds = [...LevelData_CDX._ins.colorIdsInScene, ...LevelData_CDX._ins.dishColorIds];

        // 清除现有盘子，等待重新生成
        for (let i = 0; i < this._rowCount; i++) {
            for (let j = 0; j < this._colCount; j++) {
                let grid = this._items[i][j];
                if(grid.item){
                    let dish = grid.item;
                    dish.destroy();
                    grid.item = null;
                }
            }
        } 
        for (let j = 0; j < this._rowCount; j++) {
            for (let i = 0; i < this._colCount; i++) {
                this.createDishOnGrid(j, i);
            }
        }

        this.updateRender();
        // 展示外发光
        this.scheduleOnce(() => {
            for (let i = 0; i < this._colCount; i++) {
                let grid = this._items[0][i];
                grid.item?.getComponent(Dish).showLightFrame();
            }
        }, 0.78);
    }

    /** 在指定位置（格子）创建盘子 */
    private createDishOnGrid(...cellPos: number[]){
        let [row, col] = cellPos;
        let colorIdArr = LevelData_CDX._ins.getDishColorUntilDiff();
        if(!colorIdArr || colorIdArr.length === 0) return;
        // console.log("createDishOnGrid", this.dishPlateCount ++);
        let colorId = colorIdArr[0];
        let dish = DishManager.instance.createDish(colorId);
        let count = colorIdArr.length; // Math.floor(Math.random() * 6) + 5; // 
        let dishScript = dish.getComponent(Dish);
        // 设置盘子层数
        dishScript.floorCount = count;
        // 判断是否随机扣盖子
        // 计算盲盒关卡出现的次数，根据这个次数决定单次显示盲盒总数上限
        // 1: 1,2;  2: 1,3;  3: 1,4;  4: 1,5;  5: 1,6;
        // 根据LevelData_CDX中的flag信息，出现盲盒的关卡为11, 12, 13, 14, 15, 21, 22......
        // 以此可以计算出盲盒关卡的出现次数
        let bigLevelNum = RuntimeData._ins.passTotalLevelForRankingList + 1;
        let showCoverByLidModeNum = 0;
        if(bigLevelNum >= 11 && bigLevelNum <= 15){
            showCoverByLidModeNum = bigLevelNum - (11 - 1);
        }
        let curIndex = LevelData_CDX._ins.dishCount - LevelData_CDX._ins.dishColorIds.length;
        let curCreateRatio = curIndex / LevelData_CDX._ins.dishCount;
        let maxCountMoment = Math.min(Math.min(showCoverByLidModeNum, 5) + 1, 2 + Math.floor(Math.min(4, curCreateRatio * 8)));
        // console.log("当前最高同时存在数量：", maxCountMoment);
        if (LevelData_CDX._ins.closeRandomDishFlag && this.getCoverByLidCount() < maxCountMoment)
            dishScript.close();

        let scale = dish.getScale();
        tween(dish)
            .delay(0)
            .call(()=>{
                dish.setScale(0, 0);
            })
            .to(0.23, { scale }, { easing: easing.backOut })
            .start();

        let grid = this._items[row][col];
        grid.item = dish;
        dish.setPosition(grid.pos);
    }

    /** 格子节点被点击 */
    onGridNodeTouch(event: EventTouch){
        // if(!MainGame_CDX._ins.touchFlag) return;
        if(RuntimeData._ins.gameState !== gameStateEnum.start) return;
        if(MainGame_CDX._ins.usingAdPropFlag) return;
        if(AutoGame.instance.autoFlag) return;

        let gridNode = event.target as Node;
        let grid = this.getGridByGridNode(gridNode);
        this.optGrid(grid);
    }

    /** 盘子被点击 */
    onDishTouch(dish: Node) {
        if (RuntimeData._ins.gameState !== gameStateEnum.start) return;
        if (AutoGame.instance.autoFlag) return;

        let grid = this.getGirdByItem(dish);
        this.optGrid(grid);
    }

    /** 操作指定格子 */
    optGrid(grid: IGrid<Node>): boolean{
        if (!grid || !grid.item) return false;
        // console.log("onGridNodeTouch", event, grid);
        let dish = grid.item;
        if (dish && dish.getComponent(Dish).unlocked) {
            let flag = WaitingManager.instance.placeItemToWaitingSlot(
                dish,
                undefined,
                undefined,
                () => {
                    this.scheduleOnce(() => {
                        dish.getChildByName("Shadow").destroy();
                        WaitingManager.instance.checkServing(
                            () => {
                                MainGame_CDX._ins.optOnce();
                            }
                        );

                        // this.checkGameSuccess();
                    }, 0.07);

                    this.fillTargetCol(grid.cellPos[1]);
                }
            );
            if(flag){
                dish.getComponent(Dish).leaveOperableArea();
                // 提前增加服务判定进入次数
                WaitingManager.instance.addServingEntersOnce();
                // 如果可以放置到等待区，则移除盘子发光效果，并清空对应格子数据
                grid.item?.getComponent(Dish)?.hideLightFrame();
                grid.item = null;
            }

            GuideGame.instance.addGuideCount();

            return flag;
        }

        return false;
    }

    /** 指定格子上菜，不经过待餐区 */
    public servingByGrid(item: Node) {
        let targetGrid: IGrid<Node> = null;
        for (let i = 0; i < this._rowCount; i++) {
            for (let j = 0; j < this._colCount; j++) {
                if (this._items[i][j].item == item) {
                    targetGrid = this._items[i][j];
                    break;
                }
            }
            if (targetGrid) break;
        }
    }

    public getGirdByItem(item: Node) {
        let targetGrid: IGrid<Node> = null;
        for (let i = 0; i < this._rowCount; i++) {
            for (let j = 0; j < this._colCount; j++) {
                if (this._items[i][j].item == item) {
                    targetGrid = this._items[i][j];
                    break;
                }
            }
            if (targetGrid) break;
        }
        return targetGrid;
    }

    checkGameSuccess(){
        let isSuccess = true;
        for (let i = 0; i < this._rowCount; i++) {
            for (let j = 0; j < this._colCount; j++) {
                if (this._items[i][j].item) {
                    isSuccess = false;
                    break;
                }
            }
            if (!isSuccess) break;
        }
        if (isSuccess) {
            RuntimeData._ins.gameState = gameStateEnum.preOver;
            this.scheduleOnce(() => {
                MainGame_CDX._ins.success();
            }, 0.78);
        }
    }

    /** 检测盘子生成器（本脚本所有的格子）是否都空了 */
    checkDishProductGeneratorEmpty() {
        let isEmpty = true;
        for (let i = 0; i < this._rowCount; i++) {
            for (let j = 0; j < this._colCount; j++) {
                if (this._items[i][j].item) {
                    isEmpty = false;
                    break;
                }
            }
            if (!isEmpty) break;
        }
        return isEmpty;
    }

    public getGridByGridNode(gridNode: Node){
        for(let i = 0; i < this._rowCount; i++){
            for(let j = 0; j < this._colCount; j++){
                if(this._items[i][j].gridNode === gridNode) return this._items[i][j];
            }
        }
        return null;
    }

    /** 刷新渲染（盘子阴影等） */
    updateRender(){
        for(let i = 0; i < this._colCount; i++){
            for(let j = 0; j < this._rowCount; j++){
                let grid = this._items[j][i];
                let dish = grid.item;
                if(dish && isValid(dish)){
                    let dish = grid.item;
                    dish.getComponent(Dish).removeLockedMask();

                    break;
                }
            }
        }
    }

    /** 填充所有列 */
    fillGrid() {
        for (let i = 0; i < this._colCount; i++) {
            if(this._items[0][i].item) continue;
            
            this.fillTargetCol(i);
        }
    }

    /** 填充指定列 */
    fillTargetCol(colNum: number, startIndex = 0){
        if(this._items[startIndex][colNum].item) return;

        this._fillingCount += 1;
        if (startIndex == 0) {
            this.updateRender();
        }

        let moveTime = 0.23;
        // 整列向上移动
        for (let i = startIndex + 1; i < this._rowCount; i++){
            let grid = this._items[i][colNum];
            let item = grid.item;
            if(!item) continue;

            let nextGrid = this._items[i - 1][colNum];
            nextGrid.item = item;
            grid.item = null;
            tween(item)
                .to(moveTime, {position: nextGrid.pos}, {easing: easing.sineInOut})
                .start();
        }
        this.createDishOnGrid(this._rowCount - 1, colNum);

        // this.scheduleOnce(()=>{
        //     this.fillTargetCol(colNum, startIndex);
        // }, moveTime);

        let dish = this._items[0][colNum].item;
        this.scheduleOnce(() => {
            if (this.getGirdByItem(dish))
                dish?.getComponent(Dish).showLightFrame();

            this._fillingCount -= 1;
        }, 0.78);
    }

    /** 获取场内还未选中的盘子数量 */
    public getShowingDishCount(){
        let count = 0;
        for(let i = 0; i < this._rowCount; i++){
            for(let j = 0; j < this._colCount; j++){
                let dish = this._items[i][j].item;
                if (dish && isValid(dish)){
                    count += dish.getComponent(Dish).floorCount;
                }
            }
        }
        return count;
    }
    /** 获取场内当前所有可操作的item */
    getAllOptItems(){
        return this._items[0].map(val=>val.item).filter(val=>!!val);
    }
    /** 获取场内所有item */
    getAllItems(){
        return this._items.flat(2).map(val=>val.item).filter(val=>!!val);
    }

    /** 获取场内有多少盖住的盘子 */
    public getCoverByLidCount(){
        let count = 0;
        for(let row = 1; row < this._rowCount; row++){
            for(let col = 0; col < this._colCount; col++){
                let grid = this._items[row][col];
                let dish = grid.item;
                if (dish) {
                    let dishScript = dish.getComponent(Dish);
                    if(dishScript.isCoveredByLid) count += 1;
                }
            }
        }
        return count;
    }

    /** 检测是否还有可操作菜品是否还有对应的桌子 */
    public checkOptItemHaveSameTable() {
        let items = this.getAllOptItems();
        if (items.length <= 0) return false;

        let allTables = TableManager._ins.getTables();

        let flag = false;
        for (let i = 0; i < items.length; i++) {
            let item = items[i];
            let itemScript = item.getComponent(Dish);
            if (allTables.some(table => table.getComponent(Table).getSeatIndex(itemScript.colorId) !== -1)) {
                flag = true;
                break;
            }
        }

        return flag;
    }
}

export interface IGrid<T> {
    pos: Vec3;
    cellPos: number[];
    item: T;
    gridNode: Node;
}
