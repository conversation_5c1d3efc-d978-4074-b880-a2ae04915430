import Singleton from "../../../base/Singleton";
import { IHandbookItemInfo } from "./HandbookItemData";

/** 老物件数据类 */
export class OldObjectData extends Singleton {
    public static get instacne() {
        return this.getInstance<OldObjectData>();
    }

    public oldObjects: IHandbookItemInfo[] = [
        {
            type: 4,
            id: 1,
            name: "脸盆",
            unlocked: false
        },
        {
            type: 4,
            id: 2,
            name: "暖壶",
            unlocked: false
        }, 
        {
            type: 4,
            id: 3,
            name: "鸡蛋",
            unlocked: false
        }, 
        {
            type: 4,
            id: 4,
            name: "钟摆",
            unlocked: false
        }, 
        {
            type: 4,
            id: 5,
            name: "怀表",
            unlocked: false
        }, 
        {
            type: 4,
            id: 6,
            name: "收音机",
            unlocked: false
        }, 
        {
            type: 4,
            id: 7,
            name: "电风扇",
            unlocked: false
        }, 
        {
            type: 4,
            id: 8,
            name: "缝纫机",
            unlocked: false
        }, 
        {
            type: 4,
            id: 9,
            name: "VCD",
            unlocked: false
        }, 
        {
            type: 4,
            id: 10,
            name: "照相机",
            unlocked: false
        }, 
        {
            type: 4,
            id: 11,
            name: "洗衣机",
            unlocked: false
        }, 
        {
            type: 4,
            id: 12,
            name: "留声机",
            unlocked: false
        }, 
        {
            type: 4,
            id: 13,
            name: "冰箱",
            unlocked: false
        },
        {
            type: 4,
            id: 14,
            name: "电视机",
            unlocked: false
        },
        {
            type: 4,
            id: 15,
            name: "摩托车",
            unlocked: false
        },
        {
            type: 4,
            id: 16,
            name: "宝箱",
            unlocked: false
        },
        {
            type: 4,
            id: 17,
            name: "沙漏",
            unlocked: false
        },
        {
            type: 4,
            id: 18,
            name: "台灯",
            unlocked: false
        },
        {
            type: 4,
            id: 19,
            name: "香水",
            unlocked: false
        },
        {
            type: 4,
            id: 20,
            name: "电话",
            unlocked: false
        },
        {
            type: 4,
            id: 21,
            name: "打字机",
            unlocked: false
        },
        {
            type: 4,
            id: 22,
            name: "电脑",
            unlocked: false
        },
        {
            type: 4,
            id: 23,
            name: "自行车 ",
            unlocked: false
        },
        {
            type: 4,
            id: 24,
            name: "手机",
            unlocked: false
        },
        {
            type: 4,
            id: 26,
            name: "老爷车",
            unlocked: false
        },
        {
            type: 4,
            id: 25,
            name: "游戏机",
            unlocked: false
        },
        {
            type: 4,
            id: 27,
            name: "镜子",
            unlocked: false
        },
        {
            type: 4,
            id: 28,
            name: "布鞋",
            unlocked: false
        },
        {
            type: 4,
            id: 29,
            name: "磁带",
            unlocked: false
        },
        {
            type: 4,
            id: 30,
            name: "香烟",
            unlocked: false
        },
        {
            type: 4,
            id: 31,
            name: "可口可乐",
            unlocked: false
        },
        {
            type: 4,
            id: 32,
            name: "茅台",
            unlocked: false
        },
        {
            type: 4,
            id: 33,
            name: "拖拉机",
            unlocked: false
        }
    ];
}

export enum OldObjectTypeEnum{

}