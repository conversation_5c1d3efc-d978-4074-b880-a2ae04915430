import { resolve } from "path";
import Singleton from "../../../base/Singleton";
import { releaseType } from "../../../Enum/Enum";
import { GameModel } from "../../../model/GameModel";
import { RuntimeData } from "./GameData";
import { AdManager } from "../../../ads/AdManager";
import { CoupleData } from "./CoupleData";

/** 服务器数据管理类 */
export class RemoteServerData extends Singleton{
    public static get instance(){
        return super.getInstance<RemoteServerData>();
    }

    public downloadServerDataFlag: boolean = false;
    // 服务器上的用户信息
    public serverUserInfoData: IServerUserInfo;

    /**
     * 返回的res为用户数据
     * 获取openid、unionid、session_key
     */
    async posttwx(code: string): Promise<string> {
        if(GameModel.instance.releaseType == releaseType.test_TEST) return;
        return new Promise((resolve, reject) => {
            let platform = "dy";
            if (GameModel.instance.releaseType == releaseType.applet_wechat)
                platform = "wx";
            let data = {
                gameCode: 1069,
                code, // 登录时获取
                platform, // 微信平台改为：wx
            };
            let url = 'https://game.vsane.com/common';
            let jsonData = this.jsonToUrl(data);
            let httpRe = new XMLHttpRequest(); //创建http对象
            httpRe.open('POST', url, true);
            // 设置请求头
            httpRe.setRequestHeader('Content-type', 'application/x-www-form-urlencoded;charset=utf-8');
            httpRe.send(jsonData);
            httpRe.onreadystatechange = function () {
                if (httpRe.readyState === 4 && httpRe.status === 200) {
                    let json1 = JSON.parse(httpRe.responseText);
                    let openid = json1.res.openid;
                    return resolve(openid);
                }
            };
        })
    }

    /**
     * 用户登录成功后初始化用户信息
     * @param args []
     */
    async initUserInfoData(openid, curBigLevel: number, curSmallLevel: number, smallLevelCount: number, handbookItemUnlockedList: string, shareGameNum: number, watchAdNum: number, maleName: string, femaleName: string) {
        if (GameModel.instance.releaseType == releaseType.test_TEST) return;
        openid = await AdManager.getOpenid();
        let platform = "dy";
        if (GameModel.instance.releaseType == releaseType.applet_wechat)
            platform = "wx";
        let data = {
            gameCode: 1069, // 方法id，无须改动
            verify: 'x71jf452vsanexns', // 后端验证，无需改动
            methodName: 'Game1069ChiDaXiInitUserPut', // 方法
            platform, // 微信平台改为：wx
            openid,
            curBigLevel,
            curSmallLevel,
            smallLevelCount,
            handbookItemUnlockedList, //数组转为字符串
            shareGameNum,
            watchAdNum,
            maleName,
            femaleName,
        };
        let url = 'https://game.vsane.com/game';
        let jsonData = this.jsonToUrl(data);
        let httpRe = new XMLHttpRequest(); //创建http对象
        httpRe.open('POST', url, true);
        // 设置请求头
        httpRe.setRequestHeader('Content-type', 'application/x-www-form-urlencoded;charset=utf-8');
        httpRe.send(jsonData);
        httpRe.onreadystatechange = function () {
            if (httpRe.readyState === 4 && httpRe.status === 200) {
                let json1 = JSON.parse(httpRe.responseText);
                console.log(json1);
            }
        };
    }

    async updateUserScore(openid, curBigLevel: number, curSmallLevel: number, smallLevelCount: number) {
        if (GameModel.instance.releaseType == releaseType.test_TEST) return;
        openid = await AdManager.getOpenid();
        let platform = "dy";
        if (GameModel.instance.releaseType == releaseType.applet_wechat)
            platform = "wx";
        let data = {
            gameCode: 1069, // 方法id，无须改动
            verify: 'x7i52fvsanexns', // 后端验证，无需改动
            methodName: 'Game1069ChiDaXiScorePut', // 方法
            platform, // 微信平台改为：wx
            openid: openid,
            curBigLevel,
            curSmallLevel,
            smallLevelCount,
        };
        let url = 'https://game.vsane.com/game';
        let jsonData = this.jsonToUrl(data);
        let httpRe = new XMLHttpRequest(); //创建http对象
        httpRe.open('POST', url, true);
        // 设置请求头
        httpRe.setRequestHeader('Content-type', 'application/x-www-form-urlencoded;charset=utf-8');
        httpRe.send(jsonData);
        httpRe.onreadystatechange = function () {
            if (httpRe.readyState === 4 && httpRe.status === 200) {
                let json1 = JSON.parse(httpRe.responseText);
                console.log(json1);
            }
        };
    }

    /**
     * 用户图鉴更新
     */
    async updateUserBooks(openid: string, handbookItemUnlockedList: string) {
        if (GameModel.instance.releaseType == releaseType.test_TEST) return;
        openid = await AdManager.getOpenid();
        let platform = "dy";
        if (GameModel.instance.releaseType == releaseType.applet_wechat)
            platform = "wx";
        let data = {
            gameCode: 1069, // 方法id，无须改动
            verify: 'x7i52fvsanexns', // 后端验证，无需改动
            methodName: 'Game1069ChiDaXiBooksPut', // 方法
            platform, // 微信平台改为：wx
            openid: openid,
            handbookItemUnlockedList, //数组转为字符串
        };
        let url = 'https://game.vsane.com/game';
        let jsonData = this.jsonToUrl(data);
        let httpRe = new XMLHttpRequest(); //创建http对象
        httpRe.open('POST', url, true);
        // 设置请求头
        httpRe.setRequestHeader('Content-type', 'application/x-www-form-urlencoded;charset=utf-8');
        httpRe.send(jsonData);
        httpRe.onreadystatechange = function () {
            if (httpRe.readyState === 4 && httpRe.status === 200) {
                let json1 = JSON.parse(httpRe.responseText);
                console.log(json1);
            }
        };
    }

    /**
     * 用户成就更新
     */
    async updateUserFeat(openid, num: number, feat: number) {
        if (GameModel.instance.releaseType == releaseType.test_TEST) return;
        openid = await AdManager.getOpenid();
        let platform = "dy";
        if (GameModel.instance.releaseType == releaseType.applet_wechat)
            platform = "wx";
        let data = {
            gameCode: 1069, // 方法id，无须改动
            verify: 'x7i52fvsanexns', // 后端验证，无需改动
            methodName: 'Game1069ChiDaXiFeatPut', // 方法
            platform, // 微信平台改为：wx
            openid: openid,
            feat, //1表示分享成就，2表示观看广告成就
        };
        if (data.feat == 1) {
            data['shareGameNum'] = num;
        } else if (data.feat == 2) {
            data['watchAdNum'] = num;
        }
        let url = 'https://game.vsane.com/game';
        let jsonData = this.jsonToUrl(data);
        let httpRe = new XMLHttpRequest(); //创建http对象
        httpRe.open('POST', url, true);
        // 设置请求头
        httpRe.setRequestHeader('Content-type', 'application/x-www-form-urlencoded;charset=utf-8');
        httpRe.send(jsonData);
        httpRe.onreadystatechange = function () {
            if (httpRe.readyState === 4 && httpRe.status === 200) {
                let json1 = JSON.parse(httpRe.responseText);
                console.log(json1);
            }
        };
    }

    async updateShareGameNum(shareGameNum: number){
        if (GameModel.instance.releaseType == releaseType.test_TEST) return;
        let openid = await AdManager.getOpenid();
        this.updateUserFeat(openid, shareGameNum, 1);
    }
    async updateWatchAdNum(watchAdNum: number) {
        if (GameModel.instance.releaseType == releaseType.test_TEST) return;
        let openid = await AdManager.getOpenid();
        this.updateUserFeat(openid, watchAdNum, 2);
    }

    /**
     * 用户新郎新娘邀请更新
     */
    async updateUserInvite(openid, maleName: string, femaleName: string) {
        if (GameModel.instance.releaseType == releaseType.test_TEST) return;
        openid = await AdManager.getOpenid();
        let platform = "dy";
        if (GameModel.instance.releaseType == releaseType.applet_wechat)
            platform = "wx";
        let data = {
            gameCode: 1069, // 方法id，无须改动
            verify: 'x7i52fvsanexns', // 后端验证，无需改动
            methodName: 'Game1069ChiDaXiInvitationPut', // 方法
            platform, // 微信平台改为：wx
            openid: openid,
            maleName,
            femaleName,
        };
        let url = 'https://game.vsane.com/game';
        let jsonData = this.jsonToUrl(data);
        let httpRe = new XMLHttpRequest(); //创建http对象
        httpRe.open('POST', url, true);
        // 设置请求头
        httpRe.setRequestHeader('Content-type', 'application/x-www-form-urlencoded;charset=utf-8');
        httpRe.send(jsonData);
        httpRe.onreadystatechange = function () {
            if (httpRe.readyState === 4 && httpRe.status === 200) {
                let json1 = JSON.parse(httpRe.responseText);
                console.log(json1);
            }
        };
    }

    // 通过openid获取用户信息
    public getUserInfo(openid): Promise<any> {
        if (GameModel.instance.releaseType == releaseType.test_TEST) return;
        return new Promise((resolve, reject) => {
            let platform = "dy";
            if (GameModel.instance.releaseType == releaseType.applet_wechat)
                platform = "wx";
            let url = 'https://game.vsane.com/game';
            let data = {
                methodName: 'Game1069ChiDaXiUserInfoGet',
                gameCode: 1069,
                platform, // 微信平台改为：wx
                openid: openid, //用于游戏中用户数据的查询
            };
            let self = this;
            let jsonData = this.jsonToUrl(data);
            url += '?' + jsonData;
            let httpRe = new XMLHttpRequest();
            httpRe.open('GET', url, true);
            httpRe.setRequestHeader('Content-type', 'application/x-www-form-urlencoded;charset=utf-8');
            httpRe.send();
            httpRe.onreadystatechange = function () {
                if (httpRe.readyState === 4 && httpRe.status === 200) {
                    let json1 = JSON.parse(httpRe.responseText);
                    // 函数外使用须延迟0.2秒使用数据
                    // console.log("获取到的数据：", json1);
                    self.serverUserInfoData = json1;
                    return resolve(json1);
                }
            };
        })
    }

    // json 对象转url字符串
    jsonToUrl(data) {
        let params = [];
        Object.entries(data).forEach(([key, value]) => {
            let param = key + '=' + value;
            params.push(param);
        });
        // 将数组转化为字符串，用‘&’连接
        return params.join('&');
    }

    /** 把本地图鉴解锁情况，转换为服务器端的存储形式 */
    public handbookItemList(){
        return JSON.stringify(RuntimeData._ins.handbookItems.map(itme => itme.unlocked ? 1 : 0));
    }
    /** 把服务器端图鉴解锁信息转换为本地配置 */
    public parseServerHandbookItemList(str: string){
        if(!str) return null;
        let arr = JSON.parse(str) as number[];
        return arr;
    }

    public async saveDataToServer(){
        if (GameModel.instance.releaseType == releaseType.test_TEST) return;
        let openid = await AdManager.getOpenid();
        this.updateShareGameNum(GameModel.instance.shareGameNum);
        this.updateWatchAdNum(GameModel.instance.watchAdNum);
        this.updateUserBooks(openid, this.handbookItemList());
        this.updateUserInvite(openid, CoupleData.instance.curMaleName, CoupleData.instance.curFemaleName);
        this.updateUserScore(
            openid,
            RuntimeData._ins.passTotalLevelForRankingList,
            RuntimeData._ins.curLevel,
            RuntimeData._ins.targetLevel
        );
    }

    /**
   *  某游戏某模式某道具的调用次数和成功次数上报
   *  func: game_tools_date 表示每日刷新数据 game_tools_total 表示累计数据
   */
    insertAppletToolsData(modeName: gameModeName, propName: propNameEnum, usePropSuccess: boolean) {
        if (GameModel.instance.releaseType == releaseType.test_TEST) return;
        let platform = "dy";
        if (GameModel.instance.releaseType == releaseType.applet_wechat)
            platform = "wx";
        let data = {
            gid: 1069, // 游戏id
            func: 'game_tools_total', // 方法
            platform, // 微信平台改为：wx
            mname: modeName, // 模式名称
            tname: propName, // 道具名称
            call_f: 1, // 调用次数
            call_s: usePropSuccess ? 1 : 0, // 成功次数
        };
        let url = 'https://game.vsane.com/minicommon';
        let jsonData = this.jsonToUrl(data);
        let httpRe = new XMLHttpRequest(); //创建http对象
        httpRe.open('POST', url, true);
        // 设置请求头
        httpRe.setRequestHeader('Content-type', 'application/x-www-form-urlencoded;charset=utf-8');
        httpRe.send(jsonData);
        httpRe.onreadystatechange = function () {
            if (httpRe.readyState === 4 && httpRe.status === 200) {
                let json1 = JSON.parse(httpRe.responseText);
                console.log(json1);
            }
        };
    }
}

export interface IServerUserInfo {
    openid: string;
    blevel: string;
    slevel: string;
    dlevel: string;
    books: string; //数组转为字符串
    sharenum: string;
    watchadnum: string;
    male: string;
    female: string;
}

export enum propNameEnum{
    resortProp = "打乱",
    resetTabelColorProp = "重置桌面颜色",
    autoServeProp = "自动上菜"
}
export enum gameModeName{
    吃大席 = "吃大席"
}