import { clamp } from "cc";
import Singleton from "../../../base/Singleton";
import { Tools } from "../../../common/Tools";
import { colorIdEnum } from "../Index";
import { MainGame_CDX } from "../MainGame_CDX";
import { RuntimeData } from "./GameData";
import { DishProductGenerator } from "../dish/DishProductGenerator";
import { Dish } from "../dish/Dish";
import { AutoGame } from "../../../common/AutoGame";

/**
 * 游戏内的小玩法（Vip餐桌，菜品盲盒，锁定桌子等）
 */
const enum smallGameplayMode{
    /** Vip 餐桌模式 */
    vipMode = 0,
    /** 指定步数解锁桌子模式 */
    lockTableMode,
    /** 菜品盲盒模式 */
    coverDishMode,
    /** 双拼餐桌模式 */
    moreColorTableMode,
    /** 普通模式 */
    none
}

export class LevelData_CDX extends Singleton{
    public static get _ins(){
        return super.getInstance<LevelData_CDX>();
    }

    // 桌子数组
    levelConfig: number[][] = [];
    // 菜品数组
    dishColorIds: colorIdEnum[] = [];
    // 已经生成到场景内的颜色数组
    public colorIdsInScene: colorIdEnum[] = [];
    /** 已经使用桌子数量 */
    private _usedTableCount: number = 0;
    /** 菜品总数量 */
    dishCount: number = 0;
    /** 剩余菜品数量 */
    get surplusDishCount(){
        return this.dishColorIds.length;
    }
    /** 本关卡可用小玩法 */
    public smallGameplayMode: Record<smallGameplayMode, boolean>;
    /** 本关小玩法确定完成 */
    public smallGameplayModeIsSetted: boolean = false;

    /** 游戏开始前就要确定的内容 */
    onload(){
        this.smallGameplayMode = {
            [smallGameplayMode.vipMode]: this.createVipTableFlag,
            [smallGameplayMode.lockTableMode]: this.isShowLockTableMode(),
            [smallGameplayMode.coverDishMode]: this.isShowCoveredByLidMode(),
            [smallGameplayMode.moreColorTableMode]: this.isShowMoreColorTableMode(),
            [smallGameplayMode.none]: RuntimeData._ins.passTotalLevelForRankingList >= 20
        };
        // 排除互斥的模式，随机其中一个开启
        let onlyOneKeys = [smallGameplayMode.lockTableMode, smallGameplayMode.coverDishMode, smallGameplayMode.moreColorTableMode, smallGameplayMode.none];
        for (let i = onlyOneKeys.length - 1; i >= 0; i--) {
            if (!this.smallGameplayMode[onlyOneKeys[i]]) {
                onlyOneKeys.splice(i, 1);
            } else {
                this.smallGameplayMode[onlyOneKeys[i]] = false;
            }
        }
        // 如果有可以开启的模式，随机其中一个开启
        if (onlyOneKeys.length > 0) {
            let randomIndex = Math.floor(Math.random() * onlyOneKeys.length);
            // 只把其中一个设置为true
            this.smallGameplayMode[onlyOneKeys[randomIndex]] = true;
        }
        this.smallGameplayModeIsSetted = true;
    }

    /** 初始化关卡数据 */
    init(levelConfig: number[][], dishColorIds: colorIdEnum[], usedTableCount: number) {
        this.levelConfig = levelConfig;
        this.dishColorIds = dishColorIds;
        this._usedTableCount = usedTableCount;
        this.dishCount = this.dishColorIds.length;

        // for(let i = 0; i < 1000; i++){
        //     this._getSwitchLen(i);
        // }
        
        // console.log("当前大关卡数:", RuntimeData._ins.passTotalLevelForRankingList + 1, "当前小关卡数：", RuntimeData._ins.curLevel, "桌子总数量:", this.levelConfig.length);
    }

    initGuideLevel() {
        this.levelConfig.length = 0;
        this.dishColorIds.length = 0;
        this._usedTableCount = 0;

        let seatCount = 10;

        let temp = [];
        temp.length = seatCount;
        temp.fill(1, 0, seatCount);
        this.levelConfig.push(temp);

        // 菜品
        this.levelConfig.forEach(val => {
            this.dishColorIds.push(...val);
        });
        this.shuffleDishColor();

        this.dishCount = this.dishColorIds.length;
    }

    getRandomSeatCount(curIndex: number, createCount: number){
        // 每张桌子座椅数量
        let seatCountP: number[];
        if(curIndex < createCount / 6){
            seatCountP = [0.7, 0.3];
        }else if(curIndex < createCount / 6 * 2){
            seatCountP = [0.6, 0.4];
        }else if(curIndex < createCount / 6 * 3){
            seatCountP = [0.5, 0.5];
        }else if(curIndex < createCount / 6 * 4){
            seatCountP = [0.4, 0.6];
        }else{
            seatCountP = [0.3, 0.7];
        }
        let randomNum = Math.random();
        let randomSeatCoutn = 2;
        let tempNum = 0;
        for(let i = 0; i < seatCountP.length; i++){
            tempNum += seatCountP[i];
            if(randomNum <= tempNum){
                randomSeatCoutn = i == 0 ? 4 : 6;
                break;
            }
        }

        return randomSeatCoutn;
    }

    // 打乱盘子颜色出现顺序
    public shuffleDishColor(shuffleArr?: any[], minLen?: number, maxLen?: number, dynamicShuffleLen = true){
        // 分成每个区域[10, 15] 个的，n个区域
        let curLevel = RuntimeData._ins.curLevelForLevelConfig;
        // if(curLevel == 1){
        //     minLen = 5;
        //     maxLen = 10;
        // }

        if (dynamicShuffleLen){
            minLen = this._getSwitchLen(1);
            maxLen = Math.min(minLen * (Math.random() * 0.8 + 1.7), 50);
        }

        // 当玩家不是第一次玩，即玩家已经成功吃完一场大席（通关一次）
        // 则下一次第一关的难度，以第4关为起点
        if (RuntimeData._ins.passTotalLevelForRankingList >= 1) {
            let tempNum = curLevel + 2;
            if(tempNum > 5) tempNum = 5;
            curLevel = Math.max(4, tempNum);
        }  

        // 再次手动影响关卡难度
        // 1. 第一大关（即吃第一场大席）第一关低难度，
        // 2. 第二大关第2小关开始难度提升
        if (RuntimeData._ins.passTotalLevelForRankingList == 0
            || RuntimeData._ins.passTotalLevelForRankingList == 1 && RuntimeData._ins.curLevel == 1
        ) {
            curLevel = 1;
        }

        let groups: number[][] = [];
        let index = 0;
        if(shuffleArr == undefined) shuffleArr = this.dishColorIds;
        let length = shuffleArr.length;
        while(index < length){
            let spliceLen = Math.floor(Math.random() * (maxLen - minLen + 1)) + minLen;
            let targetIndex = index + spliceLen;
            if(targetIndex >= length) targetIndex = length - 1;

            groups.push(shuffleArr.slice(index, targetIndex + 1));
            index = targetIndex + 1;

            if(dynamicShuffleLen){
                minLen = this._getSwitchLen(index, length);
                maxLen = Math.min(minLen * (Math.random() * 0.8 + 1.7), 50);
            }
        }

        // 遍历每个组
        for(let i = 1; i < groups.length; i++){
            let preGroup = groups[i - 1];
            let curGroup = groups[i];
            let ratio = Math.random() / 3 * curLevel / 5 + 1 / 3;
            let exchangeCount = Math.min(preGroup.length * ratio, curGroup.length * ratio);
            for(let j = 0; j < exchangeCount; j++){
                let preIndex =  j; // Math.floor(Math.random() * preGroup.length); //
                let curIndex =  j; // Math.floor(Math.random() * curGroup.length); //

                [preGroup[preIndex], curGroup[curIndex]] = [curGroup[curIndex], preGroup[preIndex]];
            }

            Tools.aginSortArr(preGroup);
            Tools.aginSortArr(curGroup);
        }

        shuffleArr.length = 0;
        for(let i = 0; i < groups.length; i++){
            shuffleArr.push(...groups[i]);
        }
    }
    private _getSwitchLen(curCreateDishIndex: number, count = 100) {
        let min = 5;
        let max = 20;

        let a = 1;
        let c = 5;

        // 如果开局锁桌子，则降低交换系数
        // if (curCreateDishIndex < 20) {
        //     a = 1;
        // }
        let ratio = curCreateDishIndex / count;
        // console.log("当前索引值：", curCreateDishIndex, count, ratio);
        if(curCreateDishIndex < 20){
            a = 0.8;
        } else if (ratio < 0.4){
            a = 1.15;
        } else if (ratio < 0.6) {
            a = 1.2;
        } else if (ratio < 0.7) {
            a = 1.23;
        }else{
            a = 1.5;
        }

        // 自动档 干预
        if(AutoGame.instance.autoFlag) a = Math.min(a, 1.2);

        let x = 0.05 * Math.pow(curCreateDishIndex, 1.08);
        let result = clamp(a * Math.pow(1.1, x) + c - 1, min, max);
        
        return result;
    }

    /** 获取桌子 */
    getTableConfig(){
        if(this.levelConfig.length == 0) return null;

        this._usedTableCount += 1;
        // console.log("当前剩余桌子数量：", this.levelConfig.length - 1);
        MainGame_CDX._ins.renderSurplusCount();
        return this.levelConfig.shift();
    }
    /** 获取桌子，直到不同颜色的出现 */
    getTableConfigFilterColor(filterColors: colorIdEnum[]) {
        let targetTableConfig: number[];
        for(let i = 0; i < this.levelConfig.length; i++){
            let tableConfig = this.levelConfig[i];
            if (!filterColors.includes(tableConfig[0])) {
                this._usedTableCount += 1;
                // console.log("排除指定颜色，防止场上全部为同一颜色桌子", filterColors, tableConfig[0]);
                MainGame_CDX._ins.renderSurplusCount();
                return this.levelConfig.splice(i, 1)[0];
            }
        }

        // console.log("找不到不同颜色的桌子，直接正常返回");
        return this.getTableConfig();
    }
    /** 向桌子数组添加指定桌子 */
    public addTableConfig(colorId: colorIdEnum, seatCount: number) {
        // console.log("当前剩余桌子数量：", this.levelConfig.length - 1);
        let tableConfig: number[] = [];
        tableConfig.length = seatCount;
        tableConfig.fill(colorId, 0, seatCount);
        this.levelConfig.push(tableConfig);
    }

    /** 判断关卡配置哩是否还有桌子 */
    public isHaveTable(): boolean{
        return this.levelConfig.length > 0;
    }

    getDishColor(){
        if(!this.isHaveDish()) return;

        let colorId = this.dishColorIds.shift();
        return colorId;
    }
    getDishColorUntilDiff(targetArr: any[] = this.dishColorIds, tempArr?: any[]){
        if(RuntimeData._ins.isShowGuideLevel) return this.getDishColor_guideLevel();

        let result: colorIdEnum[];
        if(tempArr) result = tempArr;
        else result = [];
        let colorId = targetArr.shift();
        if(colorId == undefined) return;
        result.push(colorId);

        let maxLen = Math.random() > 0.3 ? (Math.ceil(Math.random() * 3) + 7) : (Math.ceil(3) + 4);
        while (targetArr[0] == colorId){
            result.push(targetArr.shift());
            if(result.length == maxLen) break;
        }

        return result;
    }
    // getDishColorWithFloorCount(targetColorId: colorIdEnum, floorCount: number){
    //     if (this.dishColorIds[0] !== targetColorId) return this.getDishColorUntilDiff();

    //     let result: colorIdEnum[] = [];
    //     let colorId = this.dishColorIds.shift();
    //     if (colorId == undefined) return;
    //     result.push(colorId);

    //     let maxLen = Math.random() > 0.3 ? (Math.ceil(Math.random() * 3) + 7) : (Math.ceil(3) + 4);
    //     while (targetArr[0] == colorId) {
    //         result.push(targetArr.shift());
    //         if (result.length == maxLen) break;
    //     }

    //     return result;
    // }
    getDishColor_guideLevel() {
        let result: colorIdEnum[] = [];
        let colorId = this.dishColorIds.shift();
        if (colorId == undefined) return;
        result.push(colorId);

        let index = 0;
        let max =  Math.ceil(Math.random() * 2) + 3;
        if(this.dishColorIds.length >= 6){
            max = 5;
        }else if (this.dishColorIds.length >= 2) {
            max = 4;
        }else{
            max = this.dishColorIds.length;
        }
        while (this.dishColorIds[0] == colorId) {
            index++;
            if (index >= max) break;
            result.push(this.dishColorIds.shift());
        }

        return result;
    }

    /** 是否还有餐盘 */
    public isHaveDish(): boolean{
        return this.dishColorIds.length > 0;
    }

    /** 获取指定颜色索引数组（包含场景内已经生成的盘子，使用前先把场景内盘子合并回配置数组中） */
    public getIndexArrByColorId(colorId: colorIdEnum) {
        let tempArr = [...this.colorIdsInScene, ...this.dishColorIds];

        let result: number[] = [];
        for (let i = 0; i < tempArr.length; i++){
            if (tempArr[i] == colorId){
                result.push(i);
            }
        }
        return result;
    }

    public updateColorIdsInSceneArr(){
        let dishesInScene = DishProductGenerator.instance.getAllItems();
        let colorIdsInScene = [];
        dishesInScene.forEach(item => {
            let dishScript = item.getComponent(Dish);
            for(let i = 0; i < dishScript.floorCount; i++)
                colorIdsInScene.push(dishScript.colorId);
        });
        this.colorIdsInScene = colorIdsInScene;
    }
    /** 移除指定索引位置的颜色配置（包含游戏场景内已经生成的盘子，使用前先把场景内盘子合并回配置数组中） */
    public removeColorIdByIndex(indexArr: number[]){
        for (let i = 0; i < indexArr.length; i++) {
            let index = indexArr[i];
            if (index >= this.colorIdsInScene.length && index - this.colorIdsInScene.length < this.dishColorIds.length) {
                this.dishColorIds[index - this.colorIdsInScene.length] = undefined;
            } else {
                this.colorIdsInScene[index] = undefined;
            }
        }

        // 刨除空位
        for (let i = this.dishColorIds.length - 1; i >= 0; i--){
            if(this.dishColorIds[i] == undefined)
                this.dishColorIds.splice(i, 1);
        }
        for (let i = this.colorIdsInScene.length - 1; i >= 0; i--){
            if(this.colorIdsInScene[i] == undefined)
                this.colorIdsInScene.splice(i, 1);
        }
    }
    /** 移除最后一张符合指定颜色的桌子配置 */
    public removeTableConfigByColorId(colorId: colorIdEnum, seatCount: number){
        for(let i = this.levelConfig.length - 1; i >= 0; i--){
            let tempTableConfig = this.levelConfig[i];
            if(tempTableConfig[0] == colorId){
                if (tempTableConfig.length <= seatCount) {
                    seatCount -= tempTableConfig.length;
                    this.levelConfig.splice(i, 1);
                }else{
                    let deltaNum = tempTableConfig.length - seatCount;
                    let maxSplice = deltaNum;
                    if(deltaNum >= 6){
                        maxSplice = seatCount;
                    }else{
                        maxSplice = tempTableConfig.length - 6;
                    }
                    seatCount -= maxSplice;
                    tempTableConfig.splice(0, maxSplice);
                }
                if(seatCount <= 0)
                    break;
            }
        }
    }

    /** 是否创建加急餐桌 */
    public get createVipTableFlag(): boolean{
        // 特殊菜关卡
        if(RuntimeData._ins.isSpecialDishLevel)
            return true;
        // 普通模式，指定关卡
        let curLevel = RuntimeData._ins.curLevelForLevelConfig;
        if(curLevel == 3 || curLevel == 5){
            return true;
        }
        return false;
    }
    /** 是否扣住餐车上一部分餐盘 */
    public get closeRandomDishFlag(): boolean{
        // 特殊菜关卡
        if(RuntimeData._ins.isSpecialDishLevel)
            return true;

        let showInCurLevelFlag = this.smallGameplayMode[smallGameplayMode.coverDishMode];
        if (showInCurLevelFlag){
            let p = 0.2;
            let baseLevel = 11;
            let passLevel = RuntimeData._ins.passTotalLevelForRankingList - baseLevel;
            p = Math.min(0.5, p + passLevel * 0.1);
            if (Math.random() < p)
                return true;
        }
        return false;
    }
    /** 是否可以展示菜品盲盒模式（只是可以出现这个模式，还不确定最后是否展示） */
    private isShowCoveredByLidMode() {
        let bigLevelNum = RuntimeData._ins.passTotalLevelForRankingList + 1;
        let smallLevelNum = RuntimeData._ins.curLevel;
        if (bigLevelNum >= 11 && bigLevelNum <= 15 || bigLevelNum >= 21) {
            if (smallLevelNum >= 0)
                return true;
        }
        return false;
    }
    /** 当前是否是盲盒模式 */
    public get isCoverByLidMode(): boolean{
        return this.smallGameplayModeIsSetted && this.smallGameplayMode[smallGameplayMode.coverDishMode];
    }
    /** 是否开局锁桌子 */
    public get lockTableFlag(): boolean{
        let showInCurLevelFlag = this.smallGameplayMode[smallGameplayMode.lockTableMode];
        if (showInCurLevelFlag) {
            // 干预自动版 降低锁桌子概率到极低
            if (AutoGame.instance.autoFlag && Math.random() > 0.09)
                return false;
            return true;
        }
        return false;
    }
    /** 指定关卡是否可以展示锁桌子模式（只是可以出现这个模式，还不确定最后是否展示） */
    private isShowLockTableMode() {
        let bigLevelNum = RuntimeData._ins.passTotalLevelForRankingList + 1;
        let smallNum = RuntimeData._ins.curLevel;
        if (bigLevelNum >= 16) {
            if(smallNum >= 2)
                return true;
        }
        return false;
    }

    public get showMoreColorTableFlag(): boolean{
        let isMode = this.smallGameplayMode[smallGameplayMode.moreColorTableMode];
        if(isMode){
            let p = 0.2;
            let baseLevel = 5;
            let passLevel = RuntimeData._ins.passTotalLevelForRankingList - baseLevel;
            p = Math.min(0.5, p + passLevel * 0.1);
            // p = 1;
            if (Math.random() < p)
                return true;
        }
        return false;
    }
    /** 指定关卡是否可以展示双拼桌子模式（只是可以出现这个模式，还不确定最后是否展示） */
    private isShowMoreColorTableMode() {
        let bigLevelNum = RuntimeData._ins.passTotalLevelForRankingList + 1;
        let smallNum = RuntimeData._ins.curLevel;
        if (bigLevelNum >= 5 && bigLevelNum <= 10 || bigLevelNum >= 21) {
            return true;
        }
        return false;
    }

    getColorCountByLevelNum(){
        let colorCount = Object.keys(colorIdEnum).length / 2;

        let curLevle = RuntimeData._ins.curLevelForLevelConfig;

        return Math.min(colorCount, curLevle + 5);
    }
}