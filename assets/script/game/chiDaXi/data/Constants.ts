import { size, Size } from "cc";
import Singleton from "../../../base/Singleton";
import { colorIdEnum } from "../Index";

export class Constants extends Singleton{
    public static get _ins(): Constants{
        return this.getInstance();
    }

    // 桌子尺寸
    public readonly tableScale: number = 1.3;
    public readonly tableSize: Size = size(200 * this.tableScale, 200 * this.tableScale);

    // 盘子尺寸
    public readonly diskSize: Size = size(57, 53);

    // 队列填充时间（移动一个格子的时间）
    public readonly queueFillTime: number = 0.5;

    // 待餐区 Y值
    public readonly waitingAreaY = -365;
}