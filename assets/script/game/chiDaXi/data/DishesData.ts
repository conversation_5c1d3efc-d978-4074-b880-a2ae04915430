import Singleton from "../../../base/Singleton";
import { IHandbookItemInfo } from "./HandbookItemData";

export class DishesData extends Singleton{
    public static get instacne(){
        return this.getInstance<DishesData>();
    }

    public dishes: IDishInfo[] = [
        //#region 凉菜
        {
            type: dishTypeEnum.cold,
            id: 1,
            name: "芥末粉皮"
        },
        {
            type: dishTypeEnum.cold,
            id: 2,
            name: "拌黄瓜"
        },
        {
            type: dishTypeEnum.cold,
            id: 3,
            name: "拌莲菜"
        },
        {
            type: dishTypeEnum.cold,
            id: 4,
            name: "拌猪耳朵"
        },
        {
            type: dishTypeEnum.cold,
            id: 5,
            name: "烤鸭"
        },
        {
            type: dishTypeEnum.cold,
            id: 6,
            name: "凉拌猪肝"
        },
        {
            type: dishTypeEnum.cold,
            id: 7,
            name: "水煮花生"
        },
        {
            type: dishTypeEnum.cold,
            id: 8,
            name: "五香牛肉"
        },
        //#endregion
        //#region 热菜
        {
            type: dishTypeEnum.hot,
            id: 1,
            name: "葱烧鱿鱼"
        },
        {
            type: dishTypeEnum.hot,
            id: 2,
            name: "地三鲜"
        },
        {
            type: dishTypeEnum.hot,
            id: 3,
            name: "红烧肉"
        },
        {
            type: dishTypeEnum.hot,
            id: 4,
            name: "红烧鱼"
        },
        {
            type: dishTypeEnum.hot,
            id: 5,
            name: "老式锅包肉"
        },
        {
            type: dishTypeEnum.hot,
            id: 6,
            name: "料子鸡"
        },
        {
            type: dishTypeEnum.hot,
            id: 7,
            name: "水果山楂汤"
        },
        {
            type: dishTypeEnum.hot,
            id: 8,
            name: "酸辣皮肚"
        },
        //#endregion
        //#region 八扣碗
        {
            type: dishTypeEnum.baDaKouWan,
            id: 1,
            name: "方块肉"
        },
        {
            type: dishTypeEnum.baDaKouWan,
            id: 2,
            name: "条子肉"
        },
        {
            type: dishTypeEnum.baDaKouWan,
            id: 3,
            name: "扣酥肉"
        },
        {
            type: dishTypeEnum.baDaKouWan,
            id: 4,
            name: "扣莲藕"
        },
        {
            type: dishTypeEnum.baDaKouWan,
            id: 5,
            name: "扣红薯"
        },
        {
            type: dishTypeEnum.baDaKouWan,
            id: 6,
            name: "扣焖子"
        },
        {
            type: dishTypeEnum.baDaKouWan,
            id: 7,
            name: "扣丸子"
        },
        {
            type: dishTypeEnum.baDaKouWan,
            id: 8,
            name: "八宝饭"
        }
        //#endregion
    ];
}

export interface IDishInfo extends IHandbookItemInfo{
    type: dishTypeEnum;
    id:number;
    name:string;
    unlocked?: boolean;
}

export enum dishTypeEnum{
    cold = 1,
    hot,
    baDaKouWan
}

export const dishTypeName: Record<dishTypeEnum, string> = {
    [dishTypeEnum.cold]: "凉菜",
    [dishTypeEnum.hot]: "热菜",
    [dishTypeEnum.baDaKouWan]: "八大碗"
}