import { RuntimeData } from "../../GameData";
import { achievementIdEnum } from "../AchievementItemsData";
import { AchievementItemInfo } from "./AchievementItemInfo";

export class ItemInfo_CollectSpecialDish extends AchievementItemInfo{
    constructor(){
        super(achievementIdEnum.collectSpecialDish, "打卡特色菜", "收集特色菜总数", [-1, 1, 10, 20]);
    }

    initData(): void {
        this.progressBarNumDescription[0] = "一次挑战成功";
    }

    checkLocked(): void {
        super.checkLocked();

        this.checkByCondition(0, RuntimeData._ins.successSpecialLevelOneTime == 1);
        // let flag = false;
        // // 判断一次挑战成功成就是否已经解锁了
        // if(RuntimeData._ins.getAchievementItem(this.id, 0)){
        //     console.log("本身就已经解锁了");
        //     flag = true;
        // }else{
        //     // 判断是否一次就解锁了特殊菜(一次挑战成功)
        //     if(RuntimeData._ins.successSpecialLevelOneTime == 1){
        //         console.log("特殊菜关卡一次就挑战成功了，解锁成就！");
        //         flag = true;
        //         RuntimeData._ins.saveAchievementItem(this.id, 0);
        //     }else{
        //         console.log("特殊菜关卡没有一次就挑战成功，不解锁成就！");
        //     }
        // }
        // if(flag){
        //     this.unlockedProgress[0] = true;
        // }
    }
}