import { achievementIdEnum } from "../AchievementItemsData";
import { RuntimeData } from "../../GameData";
import { AchievementItemInfo } from "./AchievementItemInfo";

export class ItemInfo_CollectDish extends AchievementItemInfo{
    constructor(){
        super(achievementIdEnum.collectDish, "菜谱收藏大师", "收集菜品总数", [1, 3, 10, 20, -1]);
    }

    initData(): void {
        this.count = 0;
        RuntimeData._ins.handbookItems.forEach(item=>{
            if(item.unlocked){
                this.count += 1;
            }
        });

        this.progressBarNumDescription[this.progressBarNum.length - 1] = "收藏全满";
    }
    checkLocked(): void {
        super.checkLocked();

        this.checkByCondition(this.progressBarNum.length - 1, RuntimeData._ins.handbookItems.every(item=>item.unlocked));
    }
}