import { GameModel } from "db://assets/script/model/GameModel";
import { AchievementItemInfo } from "./AchievementItemInfo";
import { achievementIdEnum } from "../AchievementItemsData";

export class ItemInfo_ShareGame extends AchievementItemInfo{
    constructor(){
        super(achievementIdEnum.shareGame, "分享之王", "分享游戏给好友的次数", [1, 5, 20, 50, 100]);
    }

    initData(): void {
        this.count = GameModel.instance.shareGameNum;
    }
}