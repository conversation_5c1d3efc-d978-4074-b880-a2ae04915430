import { achievementIdEnum } from "../AchievementItemsData";
import { RuntimeData } from "../../GameData";
import { AchievementItemInfo } from "./AchievementItemInfo";

export class ItemInfo_PassDailyLevel extends AchievementItemInfo{
    constructor(){
        super(achievementIdEnum.passDailyLevel, "大席菜的神", "完成大席菜任务的次数", [1, 5, 20, 50, 100]);
    }

    initData(): void {
        this.count = RuntimeData._ins.passDailyLevelCount;
    }
}