import { GameModel } from "db://assets/script/model/GameModel";
import { AchievementItemInfo } from "./AchievementItemInfo";
import { achievementIdEnum } from "../AchievementItemsData";

export class ItemInfo_WatchVideoAd extends AchievementItemInfo{
    constructor(){
        super(achievementIdEnum.watchVideoAd, "视频达人", "观看视频广告的次数", [1, 5, 20, 50, 100]);
    }
    
    initData(): void {
        this.count = GameModel.instance.watchAdNum;
    }
}