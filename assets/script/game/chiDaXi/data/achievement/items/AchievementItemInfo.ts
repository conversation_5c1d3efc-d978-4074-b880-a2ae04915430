import { RuntimeData } from '../../GameData';
import { achievementIdEnum, IAchievementItemInfo } from '../AchievementItemsData';

export abstract class AchievementItemInfo implements IAchievementItemInfo {
    id: achievementIdEnum;
    name: string;
    description: string;
    progressBarNum: number[];
    progressBarNumDescription: string[] = [];
    unlockedProgress: boolean[] = [];
    count: number = 0;

    constructor(id: achievementIdEnum, name: string, description: string, progressBarNum: number[]) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.progressBarNum = progressBarNum;
    }

    abstract initData(): void;

    checkLocked(){
        this.initData();
        
        this.progressBarNum.forEach((num, index)=>{
            if(num == -1) return;
            // 更新对这个荣誉进度的描述
            this.progressBarNumDescription[index] = num.toString();
            // 判断这个荣誉进度是否解锁了
            if(num <= this.count){
                // console.log("解锁了");
                this.unlockedProgress[index] = true;
                RuntimeData._ins.saveAchievementItem(this.id, index);
            }
        });
    }

    /** 对于使用非通用数值判定解锁条件的，传入判断条件，判定指定索引位置的进度解锁情况 */
    protected checkByCondition(index: number, condition: boolean){
        let flag = false;
        // 如果已经解锁了最后一个进度条，就不需要再检查了
        if(RuntimeData._ins.getAchievementItem(this.id, index)){
            console.log("本身就已经解锁了");
            flag = true;
        }else{
            // 判断所有菜都收集完了
            if(condition){
                console.log("达成，解锁成就");
                flag = true;
                RuntimeData._ins.saveAchievementItem(this.id, index);
            }else{
                console.log("未达成，成就未解锁");
            }
        }
        if(flag){
            this.unlockedProgress[index] = true;
        }
    }

    public getUnlockedIndex(){
        // 获取当前解锁的进度
        let index = this.unlockedProgress.findIndex(val=>!val);
        if(index == -1) index = this.unlockedProgress.length - 1;
        return index;
    }
    public getFirstLockedIndex(){
        let count = this.progressBarNumDescription.length;

        let index = this.getUnlockedIndex();
        index += 1;
        if(index > count - 1) index = count - 1;
        
        return index;
    }
}