import Singleton from "../../../../base/Singleton";
import { RuntimeData } from "../GameData";
import { ItemInfo_CollectDish } from "./items/ItemInfo_CollectDish";
import { ItemInfo_CollectSpecialDish } from "./items/ItemInfo_CollectSpecialDish";
import { ItemInfo_PassDailyLevel } from "./items/ItemInfo_PassDailyLevel";
import { ItemInfo_ShareGame } from "./items/ItemInfo_ShareGame";
import { ItemInfo_WatchVideoAd } from "./items/ItemInfo_WatchVideoAd";

/** 成就item数据类（每个不同的成就都有自己独特的计算解锁方式） */
export class AchievementItemsData extends Singleton{
    public static get _ins(){
        return this.getInstance<AchievementItemsData>();
    }

    constructor(){
        super();
    }

    public readonly achievementItems: IAchievementItemInfo[] = [
        new ItemInfo_ShareGame(),
        new ItemInfo_WatchVideoAd(),
        new ItemInfo_CollectDish(),
        // new ItemInfo_CollectSpecialDish(),
        // new ItemInfo_PassDailyLevel()
    ]

    /** 
     * 最后一次打开荣誉UI时，解锁情况记录 
     * 存储样本，实际存储在RuntimeData本地缓存，
     */
    public readonly lastEnterAchievementUIUnlockedData: Record<achievementIdEnum, number> = {
        [achievementIdEnum.shareGame]: -1,
        [achievementIdEnum.watchVideoAd]: -1,
        [achievementIdEnum.collectDish]: -1,
        [achievementIdEnum.collectSpecialDish]: -1,
        [achievementIdEnum.passDailyLevel]: -1
    };

    /** 获取最新解锁的成就itemid */
    public getLastestUnlockedAchievement(){
        let targetItemId: achievementIdEnum = null;

        let lastEnterAchievementUIUnlockedData = RuntimeData._ins.lastEnterAchievementUIUnlockedData;

        for(let i = 0; i < this.achievementItems.length; i++){
            let itemInfo = this.achievementItems[i];
            let itemId = itemInfo.id;
            let index = itemInfo.getUnlockedIndex();
            let lastIndex = lastEnterAchievementUIUnlockedData[itemId];
            if (index > lastIndex){
                targetItemId = itemId;
                break;
            }
        }

        return targetItemId;
    }

    /** 同步到最新的数据 */
    public syncToLastestData(){
        let lastEnterAchievementUIUnlockedData = RuntimeData._ins.lastEnterAchievementUIUnlockedData;
        for(let i = 0; i < this.achievementItems.length; i++){
            lastEnterAchievementUIUnlockedData[this.achievementItems[i].id] = this.achievementItems[i].getUnlockedIndex();
        }
        RuntimeData._ins.lastEnterAchievementUIUnlockedData = lastEnterAchievementUIUnlockedData;
    }
}

export interface IAchievementItemInfo {
    id: achievementIdEnum;
    name: string;
    description: string;
    progressBarNum: number[]; // 进度条
    progressBarNumDescription: string[]; // 进度条数值描述
    unlockedProgress: boolean[]; // 解锁进度情况
    count?: number;

    checkLocked(): void;
    getUnlockedIndex(): number;
    getFirstLockedIndex(): number;
}

export enum achievementIdEnum {
    shareGame = 1,
    watchVideoAd,
    /** 收藏大席菜 */
    collectDish,
    /** 收藏特色菜 */
    collectSpecialDish,
    /** 第n次完成大席菜任务（通过每日5道菜任务） */
    passDailyLevel
}
