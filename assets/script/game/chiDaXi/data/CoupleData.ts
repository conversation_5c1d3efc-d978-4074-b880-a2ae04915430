import Singleton from "../../../base/Singleton";
import { RuntimeData } from "./GameData";

/** 新郎新娘信息类（随机名字等） */
export class CoupleData extends Singleton{
    static get instance(){
        return this.getInstance<CoupleData>();
    }

    public readonly maleNames: string[] = [
        "建国",
        "广场",
        "阿光",
        "阿开",
        "阿发",
        "阿大",
        "阿旺",
        "阿宗",
        "阿吉",
        "阿喜",
        "阿祖",
        "阿耀",
        "小虎",
        "小球",
        "小毛",
        "小胜",
        "小刚",
        "阿平",
        "小强",
        "小钢",
        "小壮",
        "小力",
        "小标",
        "小彪",
        "小波",
        "小潮",
        "小松",
        "小展",
        "小忠",
        "小富",
        "小超",
        "小霸",
        "小根",
        "小劲",
        "小炳",
        "小堂",
        "小和",
        "小友",
        "小成",
        "小开",
        "小发",
        "小吉",
        "小旺",
        "小光",
        "小宗",
        "小耀",
        "小祖",
        "小平",
        "小喜",
        "来富",
        "大根",
        "大劲",
        "铁蛋",
        "长根",
        "狗发",
        "铁根",
        "狗蛋",
        "狗富",
        "大力",
        "大强",
        "大胜",
        "大钢",
        "大刚",
        "大展",
        "大彪",
        "大波",
        "大忠",
        "大松",
        "大潮",
        "大壮",
        "大超",
        "大霸",
        "大堂",
        "大富",
        "大炳",
        "大和",
        "大友",
        "大成",
        "大旺",
        "大发",
        "大光",
        "大宗",
        "大祖",
        "大开",
        "大耀",
        "大吉",
        "大平",
        "大喜",
        "大财",
        "大毛",
        "大狗",
        "大锤",
        "大剩",
        "大娃",
        "大头",
        "大虎",
        "大球",
        "大猫",
        "二娃",
        "二狗",
        "二剩",
        "二蛋",
        "二毛",
        "二锤",
        "二虎",
        "二钢",
        "二刚",
        "二力",
        "二强",
        "二彪",
        "二友",
        "二成",
        "二旺",
        "二发",
        "二光",
        "二祖",
        "二平",
        "二喜",
        "来堂",
        "来发",
        "来光",
        "来旺",
        "来根",
        "来宗",
        "来耀",
        "来吉",
        "来平",
        "来喜",
        "傻毛",
        "傻头",
        "傻虎",
        "傻球",
        "傻猫",
        "傻蛋",
        "傻强",
        "傻钢",
        "傻刚",
        "傻壮",
        "傻力",
        "傻富",
        "傻根",
        "傻旺",
        "傻发",
        "傻喜",
        "傻光",
        "傻开",
        "傻平",
        "傻吉",
        "傻耀",
        "胖娃",
        "胖狗",
        "胖虎",
        "胖球",
        "胖蛋",
        "胖猫",
        "胖头",
        "胖彪",
        "胖富",
        "胖喜",
        "臭猫",
        "胖发",
        "胖壮",
        "胖根",
        "臭狗",
        "臭娃",
        "臭球",
        "臭松",
        "臭发",
        "臭蛋",
        "臭刚",
        "臭光",
        "臭喜",
        "发胜",
        "臭钢",
        "发财",
        "发狗",
        "发虎",
        "发强",
        "发钢",
        "发力",
        "发彪",
        "发展",
        "发标",
        "发刚",
        "发波",
        "发潮",
        "发忠",
        "发松",
        "发壮",
        "发富",
        "发霸",
        "发堂",
        "发根",
        "发超",
        "发光",
        "发和",
        "发劲",
        "发宗",
        "发炳",
        "发友",
        "发成",
        "发旺",
        "发开",
        "发耀",
        "发吉",
        "发平",
        "发喜",
        "阿猫",
        "发祖",
        "阿剩",
        "阿胜",
        "阿毛",
        "阿财",
        "阿狗",
        "阿虎",
        "阿球",
        "阿壮",
        "阿强",
        "阿钢",
        "阿彪",
        "阿标",
        "阿力",
        "阿刚",
        "阿松",
        "阿壮",
        "阿波",
        "阿潮",
        "阿忠",
        "阿富",
        "阿超",
        "阿堂",
        "阿根",
        "阿成",
        "阿霸",
        "阿炳",
        "阿和",
        "阿劲",
        "阿友",
        "长钢",
        "长胜",
        "长剩",
        "长刚",
        "长力",
        "长松",
        "长",
        "长喜",
        "长吉",
        "长耀",
        "长祖",
        "长发",
        "长光",
        "长富",
        "长平",
        "长开",
        "长财",
        "铁娃",
        "铁头",
        "铁锤",
        "铁强",
        "铁力",
        "铁钢",
        "铁球",
        "铁刚",
        "铁胜",
        "铁波",
        "铁松",
        "铁壮",
        "铁富",
        "铁友",
        "铁发",
        "铁旺",
        "铁光",
        "铁柱",
        "铁耀",
        "铁喜",
        "狗娃",
        "狗剩",
        "狗毛",
        "狗旺",
        "狗壮",
        "狗耀",
        "狗平",
        "狗喜",
        "狗吉",
        "来胜",
        "来虎",
        "来球",
        "来钢",
        "来财",
        "来力",
        "来彪",
        "来刚",
        "来强",
        "来波",
        "来忠",
        "来松",
        "来劲",
        "来成"
    ]
    public readonly femaleNames: string[] = [
        "翠花",

        "凤霞",

        "春梅",

        "大妞",

        "秀兰",

        "根生",

        "腊梅",

        "杏花",

        "小芳",

        "玉兰",

        "红梅",

        "桂花",

        "小翠",

        "香玉",

        "玉环",

        "红霞",

        "荷花",

        "宝凤",

        "小英",

        "秋香",

        "艳芳",

        "丫蛋",

        "兰兰",

        "秀英",

        "喜妞",

        "春花",

        "小莲",

        "喜凤",

        "二妮"
    ]
    public lastNames: string[] = [
        "张",
        "王",
        "李",
        "赵",
        "孙",
        "周",
        "吴",
        "徐",
        "马",
        "胡",
        "诸葛",
        "司马",
        "欧阳",
        "夏侯",
        "皇甫",
        "慕容",
        "西门",
        "上官"
    ]

    /** 当前新郎名字 */
    private _curMaleName: string;
    /** 当前新娘名字 */
    private _curFemaleName: string;

    public get curMaleName(): string {
        if(RuntimeData._ins.curInvitationInfo){
            return RuntimeData._ins.curInvitationInfo.inviterName[0];
        }
        return this._curMaleName;
    }
    public get curFemaleName(): string {
        if (RuntimeData._ins.curInvitationInfo) {
            return RuntimeData._ins.curInvitationInfo.inviterName[1];
        }
        return this._curFemaleName;
    }

    public genRandomMaleName(){
        let lastName = this.lastNames[Math.floor(Math.random() * this.lastNames.length)];
        let firstName = this.maleNames[Math.floor(Math.random() * this.maleNames.length)]
        let maleName = lastName + firstName;
        this._curMaleName = maleName;
        return maleName;
    }
    public genRandomFemaleName(){
        let lastName = this.lastNames[Math.floor(Math.random() * this.lastNames.length)];
        let firstName = this.femaleNames[Math.floor(Math.random() * this.femaleNames.length)]
        let femaleName = lastName + firstName;
        this._curFemaleName = femaleName;
        return femaleName;
    }
}