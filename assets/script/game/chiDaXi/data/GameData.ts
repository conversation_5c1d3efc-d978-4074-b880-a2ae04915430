import { director, Node } from "cc";
import { AdManager } from "../../../ads/AdManager";
import Singleton from "../../../base/Singleton";
import { DailyRefresh, DailyRefreshItems } from "../../../common/DailyRefresh";
import { GameModel } from "../../../model/GameModel";
import { bundleNamesEnum, colorIdEnum, gameStateEnum, IInvitationInfo, IProp, propIdEnum, sceneNamesEnum } from "../Index";
import { MainGame_CDX } from "../MainGame_CDX";
import { achievementIdEnum, AchievementItemsData } from "./achievement/AchievementItemsData";
import { IHandbookItemInfo } from "./HandbookItemData";
import { OldObjectData } from "./OldObjectData";
import { AutoGame } from "../../../common/AutoGame";
import { IServerUserInfo, RemoteServerData } from "./RemoteServerData";
import { releaseType } from "../../../Enum/Enum";
import { CoupleData } from "./CoupleData";

/** 游戏运行时数据类 */
export class RuntimeData extends Singleton {
    public static get _ins(): RuntimeData {
        return super.getInstance<RuntimeData>();
    }

    private _playerData: PlayerData;

    public gameState: gameStateEnum;
    public curSelectPropId: propIdEnum;
    public gameDifficulty: number = 0;

    /** 是否允许进入特殊菜关卡 */
    private _allowEnterSpecialLevel = false;
    /** 不允许进入特殊菜关卡的前提下，累积观看广告的次数 */
    public watchSpecialLevelVideoAdCount = 0;
    /** 当前是否是特殊菜关卡 */
    public isSpecialDishLevel = false;
    /** 是否一次挑战成功特殊菜关卡(只有 -1 的时候可以赋值， 0代表失败，1代表成功) */
    private _successSpecialLevelOneTime = -1;

    /** 本次复活次数 */
    public reliveCount = 0;

    /** 本次对局道具使用情况 */
    public propUsedInfoInCurFight: Partial<Record<propIdEnum, number>>;

    /** 当前正在显示的剩余桌子数量 */
    public curShowingSurplusTableCount: number = 0;

    /** 当前大席目标物品 */

    /** 当前主页场景的动画 */
    public homeSceneAnimId: number = -1;
    /** 首页同一动画连续出现次数
     * index == 0; 动画id
     * index == 1; 出现次数
     */
    private _continuousHomeSceneAnimCount: number[] = [];

    init() {
        // console.log("初始化数据");
        this._playerData = new PlayerData();
        this._playerData.restore();
        this.gameState = gameStateEnum.prepare;
        this.curSelectPropId = null;
        this.gameDifficulty = 0;
        this.isSpecialDishLevel = false;
        if (this._successSpecialLevelOneTime == 0)
            this._successSpecialLevelOneTime = -1;
        this.reliveCount = 0;
        this.propUsedInfoInCurFight = {
            [propIdEnum.resetTableWithMostColorProp]: 0,
            [propIdEnum.resortDishesProp]: 0,
            [propIdEnum.autoServingProp]: 0
        }

        if (this.targetLevel == 0) {
            this.targetLevel = this.getRandomTargetLevel();
            this.passLevel = 0;
        }
    }

    /** 获取服务器数据进行比对 */
    public async downloadServerData() {
        if (GameModel.instance.releaseType !== releaseType.test_TEST) {
            // 加载运行时数据
            let openid = await AdManager.getOpenid();
            let res = await RemoteServerData.instance.getUserInfo(openid);
            let code = res.code;
            let serverUserInfo: IServerUserInfo = res.user;
            if (code != 1) {
                // console.log("用户数据不存在，初始化用户数据", serverUserInfo);
                RemoteServerData.instance.initUserInfoData(
                    openid,
                    this.passTotalLevelForRankingList,
                    this.curLevel,
                    this.targetLevel,
                    RemoteServerData.instance.handbookItemList(),
                    GameModel.instance.shareGameNum,
                    GameModel.instance.watchAdNum,
                    CoupleData.instance.curMaleName,
                    CoupleData.instance.curFemaleName
                );
            } else {
                // console.log("用户数据：", serverUserInfo);
                let passTotalLevelForRankingList = parseInt(serverUserInfo.blevel);
                let targetLevel = parseInt(serverUserInfo.dlevel);
                let curLevel = parseInt(serverUserInfo.slevel);
                let handbookItemUnlockedList = RemoteServerData.instance.parseServerHandbookItemList(serverUserInfo.books);
                let shareGameNum = parseInt(serverUserInfo.sharenum);
                let watchAdNum = parseInt(serverUserInfo.watchadnum);
                let maleName = serverUserInfo.male;
                let femaleName = serverUserInfo.female;

                this.passTotalLevelForRankingList = Math.max(this.passTotalLevelForRankingList, passTotalLevelForRankingList);
                this.targetLevel = Math.max(this.targetLevel, targetLevel);
                this.passLevel = Math.max(this.passLevel, curLevel - 1);
                this.setHandbookWithServerData(handbookItemUnlockedList);
                let localShareGameNum = GameModel.instance.shareGameNum;
                let localWatchAdNum = GameModel.instance.watchAdNum;
                let remoteShareGameNum = Number.isNaN(shareGameNum) ? 0 : shareGameNum;
                let remoteWatchAdNum = Number.isNaN(watchAdNum) ? 0 : watchAdNum;
                GameModel.instance.shareGameNum = Math.max(localShareGameNum, remoteShareGameNum);
                GameModel.instance.watchAdNum = Math.max(localWatchAdNum, remoteWatchAdNum);
            }
        }
    }

    /** 取消当前选中正在使用的道具 */
    public cancelCurSelectProp() {
        this.curSelectPropId = null;
    }

    public get todayPassLevel(): number {
        return this._playerData.data.todayPassLevel;
    }
    public set todayPassLevel(val: number) {
        this._playerData.data.todayPassLevel = val;
        this._playerData.saveData();
    }
    /** 当前所在关卡 （1-3-。。。。。。。） */
    public get curLevel(): number {
        // return this.todayPassLevel + 1;
        return this._playerData.data.passLevel + 1;
    }
    /** 获取通过数学函数影响后的当前游戏关卡值（给生成关卡的时候使用） */
    public get curLevelForLevelConfig(): number {
        let curLevel = this.curLevel * 2 - 1;

        return curLevel;
    }
    public set passLevel(val: number) {
        this._playerData.data.passLevel = val;
        this._playerData.saveData();
    }
    public get passLevel(): number {
        return this._playerData.data.passLevel;
    }
    public get targetLevel(): number {
        return this._playerData.data.targetLevel;
    }
    public set targetLevel(val: number) {
        this._playerData.data.targetLevel = val;
        this._playerData.saveData();
    }

    public getRandomTargetLevel() {
        let completeCount = this.passTotalLevelForRankingList;

        let maxLevel = 3;
        return Math.min(completeCount + 2, maxLevel);
    }

    public getAllColorId() {
        let colors = Object.getOwnPropertyNames(colorIdEnum);
        // 因为值是数字，所以需要去除，只留下数字
        colors.length /= 2;
        return colors.map(colorId => parseInt(colorId));
    }

    //#region 道具存取相关
    /** 添加获取的道具 */
    public addProp(prop: IProp) {
        let targetProp = this._playerData.data.inventory.props.find(val => val.propId == prop.propId);
        if (targetProp)
            targetProp.count += prop.count;
        else
            this._playerData.data.inventory.props.push(prop);
        this._playerData.saveData();
        if (director.getScene().name == sceneNamesEnum.吃大席场景)
            MainGame_CDX._ins.updateRenderAdPropBtn();
    }
    public getPropById(propId: propIdEnum): IProp {
        return this._playerData.data.inventory.props.find(val => val.propId == propId);
    }

    public removeProp(propId: propIdEnum) {
        let index = this._playerData.data.inventory.props.findIndex(val => val.propId == propId);
        if (index != -1) {
            if (this._playerData.data.inventory.props[index].count > 1)
                this._playerData.data.inventory.props[index].count--;
            else
                this._playerData.data.inventory.props.splice(index, 1);
        }
        this._playerData.saveData();
    }
    public useProp(propId: propIdEnum): boolean {
        if (this.getPropById(propId)) {
            this.removeProp(propId);
            MainGame_CDX._ins.updateRenderAdPropBtn();
            return true;
        }
        return false;
    }
    /** 获取随机道具，并自动放入背包中 */
    public getRandomProp(): IProp {
        let allPropNames = Object.keys(propIdEnum);
        allPropNames = allPropNames.splice(allPropNames.length / 2);
        let randomIndex = Math.ceil(Math.random() * (allPropNames.length - 1));
        if (randomIndex >= allPropNames.length) return;
        let randomProp = allPropNames[randomIndex];
        let prop = {
            propId: propIdEnum[randomProp],
            propName: randomProp,
            count: 1
        }

        // console.log("获取随机道具：", prop, allPropNames, randomIndex);

        RuntimeData._ins.addProp(prop);

        return prop;
    }

    public getRandomColorId() {
        let colors = Object.getOwnPropertyNames(colorIdEnum);
        // 因为值是数字，所以需要去除，只留下数字
        colors.length /= 2;
        let selectLength = colors.length;
        if (this.gameDifficulty == 0) selectLength /= 2;
        let randomColorId = parseInt(colors[Math.floor(Math.random() * selectLength)]);
        return randomColorId;
    }
    //#endregion
    //#region 图鉴存取相关
    public get handbookItems() {
        return this._playerData.data.inventory.handbookItems;
    }
    public unlockNewHandbookItem() {
        let handbookItem = this.handbookItems.find(val => !val.unlocked);
        if (!handbookItem) return console.log("没有新的，全部解锁了");
        handbookItem.unlocked = true;
        this._playerData.saveData();
        return handbookItem;
    }
    //#endregion
    public get passDailyLevelCount() {
        return this._playerData.data.passDailyLevelCount;
    }
    public set passDailyLevelCount(val: number) {
        this._playerData.data.passDailyLevelCount = val;
        this._playerData.saveData();
    }
    /** 获取剩余开启特殊菜关卡免费次数 */
    public get allowEnterSpecialLevel(): boolean {
        if (this._allowEnterSpecialLevel) return true;

        if (DailyRefresh.isNewDay(DailyRefreshItems.specialLevelFreeCount)) {
            this._allowEnterSpecialLevel = true;
        } else {
            // 判断看广告次数
            if (this.watchSpecialLevelVideoAdCount >= 2) {
                this.watchSpecialLevelVideoAdCount = 0;
                this._allowEnterSpecialLevel = true;
            }
        }
        return this._allowEnterSpecialLevel;
    }
    public set allowEnterSpecialLevel(val: boolean) {
        this._allowEnterSpecialLevel = val;
    }

    /** 存储成就系统的item（徽章等）收集进度 */
    public saveAchievementItem(achievementItemId: achievementIdEnum, index: number) {
        let achievementItem = this._playerData.data.inventory.achievementItems[achievementItemId];
        achievementItem[index] = true;
        this._playerData.saveData();
    }
    /** 获取指定成就系统的item */
    public getAchievementItem(achievementItemId: achievementIdEnum, index: number) {
        return this._playerData.data.inventory.achievementItems[achievementItemId][index];
    }

    public get lastEnterAchievementUIUnlockedData() {
        return this._playerData.data.lastEnterAchievementUIUnlockedData;
    }
    public set lastEnterAchievementUIUnlockedData(val) {
        this._playerData.saveData();
    }

    public set successSpecialLevelOneTime(val: number) {
        console.log("设置特殊关卡一次性通过", this._successSpecialLevelOneTime, val);
        if (this.successSpecialLevelOneTime !== -1) return;
        this._successSpecialLevelOneTime = val;
    }
    public get successSpecialLevelOneTime(): number {
        return this._successSpecialLevelOneTime;
    }

    // #region 邀请函相关
    public get curInvitationInfo(): IInvitationInfo {
        return this._playerData.data.invitationInfo;
    }
    public set curInvitationInfo(val: IInvitationInfo) {
        this._playerData.data.invitationInfo = val;
        this._playerData.saveData();
    }
    public get curBgId(): number {
        let curBgId = this._playerData.data.curBgId;
        if (curBgId == -1) {
            curBgId = Math.ceil(Math.random() * 1);
            this.curBgId = curBgId;
        }
        return curBgId;
    }
    public set curBgId(val: number) {
        this._playerData.data.curBgId = val;
        this._playerData.saveData();
    }
    public get curMainDishId(): number {
        let curMainDishId = this._playerData.data.curMainDishId;
        if (curMainDishId == -1) {
            curMainDishId = Math.ceil(Math.random() * 4);
            this.curMainDishId = curMainDishId;
        }
        return curMainDishId;
    }
    public set curMainDishId(val: number) {
        this._playerData.data.curMainDishId = val;
        this._playerData.saveData();
    }
    // #endregion
    public get is1stRunGame() {
        // return this._playerData.data.is1stRunGame && this.passTotalLevelForRankingList <= 0;
        return this.curLevel == 1 && this.passTotalLevelForRankingList <= 0;
    }
    public set is1stRunGame(val: boolean) {
        this._playerData.data.is1stRunGame = val;
        this._playerData.saveData();
    }
    /** 是否展示引导关卡 */
    public get isShowGuideLevel() {
        return this.is1stRunGame;
    }

    /** 排行榜使用的总关卡数信息 */
    public get passTotalLevelForRankingList() {
        let val = this._playerData.data.passTotalLevelForRankingList;
        if (val !== 0 && !val) {
            val = 0;
            this.passTotalLevelForRankingList = val;
        }
        return val;
    }
    public set passTotalLevelForRankingList(val) {
        this._playerData.data.passTotalLevelForRankingList = val;
        this._playerData.saveData();
        AdManager.setRankData(val.toString());
    }

    public get targetHandbookItemInfo() {
        let targetItemInfo = this._playerData.data.inventory.handbookItems.find(val => !val.unlocked);
        if (!targetItemInfo) {
            let allItmes = this._playerData.data.inventory.handbookItems;
            targetItemInfo = allItmes[allItmes.length - 1];

            if (AutoGame.instance.autoFlag) {
                targetItemInfo = allItmes[Math.max(0, AutoGame.instance.curOldObjectIndex - allItmes.length - 1)];
            }
        }
        return targetItemInfo;
    }

    /** 首页同一动画连续出现次数
     * index == 0; 动画id
     * index == 1; 出现次数
     */
    public get continuousHomeSceneAnimCount(): number[] {
        if (this._continuousHomeSceneAnimCount.length === 0) {
            let val = GameModel.instance.loadFromLocal<number[]>("continuousHomeSceneAnimCount", null);
            if (!val) {
                val = [-1, 0];
            }
            this._continuousHomeSceneAnimCount = val;
        }
        return this._continuousHomeSceneAnimCount;
    }
    public set continuousHomeSceneAnimCount(value: number[]) {
        this._continuousHomeSceneAnimCount = value;
        GameModel.instance.saveToLocal("continuousHomeSceneAnimCount", this._continuousHomeSceneAnimCount);
    }
    public getRandomHomeSceneAnimId() {
        let animId = Math.random() > 0.5 ? 1 : 2;
        if (this.continuousHomeSceneAnimCount[0] == animId) {
            this.continuousHomeSceneAnimCount[1] += 1;
        } else {
            this.continuousHomeSceneAnimCount[0] = animId;
            this.continuousHomeSceneAnimCount[1] = 1;
        }
        if (this.continuousHomeSceneAnimCount[1] > 3) {
            // console.log("展示次数太多了，换一个吧");
            animId = animId == 1 ? 2 : 1;
            this.continuousHomeSceneAnimCount[0] = animId;
            this.continuousHomeSceneAnimCount[1] = 1;
        }
        this.continuousHomeSceneAnimCount = this._continuousHomeSceneAnimCount;
        RuntimeData._ins.homeSceneAnimId = animId;
        return animId;
    }

    // #region 服务器数据存取相关
    public get uploadDataFlag() {
        return this._playerData.data.uploadDataFlag;
    }
    public set uploadDataFlag(val: boolean) {
        // 只有第一次拉取服务器成功后才设置为true，否则为false（或0）
        this._playerData.data.uploadDataFlag = true;
        this._playerData.saveData();
    }
    public get openid() {
        return this._playerData.data.openid;
    }
    public set openid(val: string) {
        this._playerData.data.openid = val;
        this._playerData.saveData();
    }
    /** 把从服务器获取的数据，保存到本地 */
    public setHandbookWithServerData(serverData: number[]) {
        console.log(serverData);
        if (!serverData) return;

        let serverUnlockedCount = 0;
        serverData.forEach(val => {
            if (val == 1) serverUnlockedCount += 1;
        });
        let localUnlockedCount = 0;
        let localData = this.handbookItems;
        localData.forEach(val => {
            if (val.unlocked) localUnlockedCount += 1;
        });
        if (serverUnlockedCount > localUnlockedCount) {
            localData.forEach((val, index) => {
                if (serverData[index] == 1 && !val.unlocked) {
                    val.unlocked = true;
                }
            });
            this._playerData.saveData();
        }
    }
    // endregion

    public get eatCakeLevel() {
        return this._playerData.data.eatCakeLevel;
    }
    public set eatCakeLevel(val: number) {
        this._playerData.data.eatCakeLevel = val;
        this._playerData.saveData();
    }
}

interface IInventory {
    /** 拥有的道具 */
    props: IProp[];
    /** 解锁的图鉴 */
    handbookItems: IHandbookItemInfo[];
    /** 成就系统(徽章等)的解锁进度 */
    achievementItems: Partial<Record<achievementIdEnum, boolean[]>>;
}

interface IPlayerData {
    /** 第一次打开游戏 */
    is1stRunGame: boolean;
    /** 今日关卡数 */
    todayPassLevel: number;
    /** 背包 */
    inventory: IInventory;
    /** 最后一次打开成就界面时的解锁情况 */
    lastEnterAchievementUIUnlockedData: Record<achievementIdEnum, number>;
    /** 通关次数 */
    passDailyLevelCount: number;
    /** 目标关卡数（小关卡） */
    targetLevel: number;
    /** 当前已经通过关卡数（小关卡） */
    passLevel: number;
    invitationInfo: IInvitationInfo;
    /** 当前大席场景背景id */
    curBgId: number;
    /** 当前主菜id */
    curMainDishId: number;
    /** 通过总关卡数（大关卡） */
    passTotalLevelForRankingList: number;
    /** 是否上传过服务器（flag值判断是否数据丢失了，如果flag为false（或0）则代表数据为初始值，
     * 上传数据到服务器前需要先从服务器获取，获取之后再重置flag值为true（或1）
     * 之后就可以上传数据到服务器了
     */
    uploadDataFlag: boolean;
    openid: string;
    eatCakeLevel: number;
}
class PlayerData {
    data: IPlayerData = {
        is1stRunGame: true,
        todayPassLevel: 0,
        inventory: {
            props: [],
            handbookItems: [...OldObjectData.instacne.oldObjects], // ...DishesData.instacne.dishes, 
            achievementItems: {},
        },
        lastEnterAchievementUIUnlockedData: AchievementItemsData._ins.lastEnterAchievementUIUnlockedData,
        passDailyLevelCount: 0,
        targetLevel: 0,
        passLevel: 0,
        invitationInfo: null,
        curBgId: -1,
        curMainDishId: -1,
        passTotalLevelForRankingList: 0,
        uploadDataFlag: false,
        openid: null,
        eatCakeLevel: 1,
    }

    constructor() {
        // 初始化成就徽章进度数据
        let achievementItems = this.data.inventory.achievementItems;
        let keys = Object.keys(achievementIdEnum);
        for (let i = keys.length / 2; i < keys.length; i++) {
            let key = keys[i];
            achievementItems[achievementIdEnum[key]] = [];
        }
    }

    saveData() {
        GameModel.instance.saveToLocal("STORAGE_KEY_PLAYER_DATA_2", this.data);
    }

    restore() {
        let data: IPlayerData = GameModel.instance.loadFromLocal<IPlayerData>("STORAGE_KEY_PLAYER_DATA_2", this.data);
        if (data) {
            // 防止更新把玩家数据覆盖了，所以这里只更新不存在的字段
            let keys = Object.keys(this.data);
            let curKeys = Object.keys(data);
            for (let key of keys) {
                if (curKeys.indexOf(key) == -1) {
                    data[key] = this.data[key];
                }
            }
            this.data = data;

            // 判定新增老物件
            let oldObjects = OldObjectData.instacne.oldObjects;
            for (let oldObject of oldObjects) {
                if (!this.data.inventory.handbookItems.find(val => val.id == oldObject.id)) {
                    this.data.inventory.handbookItems.push(oldObject);
                    this.saveData();
                }
            }
        }
    }
}