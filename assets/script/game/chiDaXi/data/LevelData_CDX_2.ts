import { clamp, log } from "cc";
import Singleton from "../../../base/Singleton";
import { colorIdEnum } from "../Index";
import { RuntimeData } from "./GameData";
import { LevelData_CDX } from "./LevelData_CDX";
import { AutoGame } from "../../../common/AutoGame";

/** 
 * levelData_CDX_2,3,4,5,6...... 
 * 代表如果有新的关卡配置需求可以通过新增其他类，生成新的数据赋值到初始的LevelData_CDX
 * 从而做到不影响初始类和游戏内调用初始类变量方法的效果
 */
export class LevelData_CDX_2 extends Singleton {
    public static get _ins() {
        return super.getInstance<LevelData_CDX_2>();
    }

    // 桌子数组
    levelConfig: number[][] = [];
    // 菜品数组
    dishColorIds: colorIdEnum[] = [];
    /** 已经使用桌子数量 */
    private _usedTableCount: number = 0;

    /** 指定大关卡中对应小关卡的桌子数量 */
    public tableCountConfig: number[][] = [
        [1, 20],
        [50, 80, 100],
        [65, 90, 120],
        [80, 100, 130],
        [90, 120, 150]
    ]

    init() {
        LevelData_CDX._ins.onload();

        this.levelConfig.length = 0;
        this.dishColorIds.length = 0;
        this._usedTableCount = 0;

        // 创建关卡
        // if(RuntimeData._ins.isSpecialDishLevel)
        //     this._createSpecialLevel();
        // else
        this._createNormalLevel();

        let result: colorIdEnum[][] = [];
        let tempResult: colorIdEnum[] = [];
        do {
            tempResult.length = 0;
            LevelData_CDX._ins.getDishColorUntilDiff(this.dishColorIds, tempResult);
            if (tempResult.length > 0) {
                result.push([...tempResult]);
            }
        }
        while (tempResult.length > 0);

        // 干预自动挡 自动模式情况下，不刷新
        if (!AutoGame.instance.autoFlag) {
            // 再刷新一遍关卡，调整游戏难度
            LevelData_CDX._ins.shuffleDishColor(result);
            // console.log("关卡配置：", result, this.dishColorIds);
        }
        
        // 刷新后的结果伸展开，重新赋到关卡配置中
        this.dishColorIds = result.flat(2);

        // 关卡初始化
        LevelData_CDX._ins.init(this.levelConfig, this.dishColorIds, this._usedTableCount);
        // console.log(this._getColorCountByLevel());
    }

    /** 创建普通关卡配置 */
    private _createNormalLevel() {
        // for (let i = 1; i <= 5; i++) {
        //     log("第" + i, "关颜色数量：","  :", this.getColorCountByLevel(i));
        //     let dishCount = i * 200;
        //     for (let j = 0; j < dishCount; j++) {
        //         log("level ", j, " 关卡每次获取的颜色总数: ", this.getColorIdByCurCreateCount(j, dishCount, i));
        //     }
        // }
        let createTableCount = this._getTableCountByLevel();
        for (let i = 0; i < createTableCount; i++) {
            let seatCount = this._getRandomSeatCount(i, createTableCount);

            let temp = [];
            temp.length = seatCount;
            temp.fill(1, 0, seatCount);
            this.levelConfig.push(temp);
        }

        let seatCount = 0;
        this.levelConfig.forEach(val => {
            seatCount += val.length;
        });
        LevelData_CDX._ins.dishCount = seatCount;
        // let index = 0;
        // for(let i = 0; i < this.levelConfig.length; i++){
        //     for(let j = 0; j < this.levelConfig[i].length; j++){
        //         this.levelConfig[i][j] = this.getColorIdByCurCreateCount(index, seatCount);
        //         index += 1;
        //     }
        // }
        // 是否展示双拼桌子小玩法模式
        // let moreColorModeFlag = true;
        let index = 0;
        let preColor: colorIdEnum;
        for (let i = 0; i < this.levelConfig.length; i++) {
            let tableConfig = this.levelConfig[i];

            let randomColor = this.getColorIdByCurCreateCount(index, seatCount);
            if (preColor) {
                while (preColor == randomColor) {
                    randomColor = this.getColorIdByCurCreateCount(index, seatCount);
                }
            }
            preColor = randomColor;

            // // 填充颜色
            // let randomP = 0.23;
            // // 在这个概率范围内，则代表是双拼桌子
            let moreColorModeFlag = LevelData_CDX._ins.showMoreColorTableFlag;
            let flag = moreColorModeFlag;
            if (!flag) {
                // 普通模式 flag = false;
                for (let j = 0; j < tableConfig.length; j++) {
                    tableConfig[j] = randomColor;
                }
            }else{
                // 双拼色模式 flag = true;
                // 第一个颜色填充数量
                let fillCount = Math.ceil(tableConfig.length * (Math.random() * 0.6 + 0.2));
                for (let j = 0; j < fillCount; j++) {
                    tableConfig[j] = randomColor;
                }
                // 获取第二个颜色
                let secondColor = this.getColorIdByCurCreateCount(index, seatCount);
                while (secondColor == randomColor) {
                    secondColor = this.getColorIdByCurCreateCount(index, seatCount);
                }
                for (let j = fillCount; j < tableConfig.length; j++) {
                    tableConfig[j] = secondColor;
                }
                preColor = secondColor;
            }

            index += tableConfig.length;
        }

        // 干预自动挡 自动模式情况下，不刷新
        if (!AutoGame.instance.autoFlag) {
            // 刷新等级配置
            LevelData_CDX._ins.shuffleDishColor(this.levelConfig, 5, 10, false);
        }

        this.levelConfig.forEach(val => {
            this.dishColorIds.push(...val);
        });
    }

    /** 通过关卡数，获取本关卡包含的颜色种类 */
    public getColorCountByLevel(level?: number): number {
        if (level == undefined) level = RuntimeData._ins.curLevelForLevelConfig;

        // 当玩家不是第一次玩，即玩家已经成功吃完一场大席（通关一次）
        // 则下一次第一关的难度，以第4关为起点
        if (RuntimeData._ins.passTotalLevelForRankingList >= 1) {
            level = Math.max(4, level);
        }  
        
        // 再次手动影响关卡难度
        // 1. 第一大关（即吃第一场大席）第一关低难度，
        if (RuntimeData._ins.passTotalLevelForRankingList == 0
            || RuntimeData._ins.passTotalLevelForRankingList == 1 && RuntimeData._ins.curLevel == 1
        ) {
            level = 1;
        }

        /**
         * temp info 当前临时数据信息
         * 1. 关卡数量 5关
         * 2. 颜色种类 9种 
         */

        // 基础颜色种类数量
        let baseColorCount = 5;
        let levelCount = 5;
        let a = 2.5 / levelCount;
        // 随着关卡增加，颜色数量呈现曲线增长
        let colorCount = Math.ceil(baseColorCount + Math.pow(a * level, 2.56));
        // 颜色种类上限
        colorCount = clamp(colorCount, baseColorCount, 9);

        if(AutoGame.instance.autoFlag) colorCount = Math.min(colorCount, 4);
        return colorCount;
    }

    /** 通过已经生成的数量，获取当前的颜色id */
    public getColorIdByCurCreateCount(curCreateCount: number, createCount: number, curLevel?: number) {
        let curLevelColorCount = this.getColorCountByLevel(curLevel);
        // console.log("当前关卡颜色种类数量：", curLevelColorCount);
        // 基础颜色种类数量
        let baseColorCount = Math.min(6, curLevelColorCount);
        let a = 2.5 / createCount;
        // 随着关卡增加，颜色数量呈现曲线增长
        let colorCount = Math.floor(baseColorCount + Math.pow(a * curCreateCount, 2.56));
        // 颜色种类上限
        colorCount = clamp(colorCount, baseColorCount, curLevelColorCount);
        let randomColor = Math.ceil(Math.random() * colorCount);
        // 干预自动挡 自动模式情况下，在9种颜色中随机取4种颜色盘子
        if (AutoGame.instance.autoFlag) {
            // 随机数在AutoGame中获取
            return AutoGame.instance.colorIdsInCurLevel[randomColor - 1];
        }
        return randomColor;
    }

    /** 根据关卡数，获取每关卡对应生成盘子数量 */
    private _getTableCountByLevel(level?: number) {
        let bigLevelNum = RuntimeData._ins.passTotalLevelForRankingList;
        let smallLevelNum = RuntimeData._ins.curLevel;
        bigLevelNum = Math.min(bigLevelNum, this.tableCountConfig.length - 1);
        let count = this.tableCountConfig[bigLevelNum][smallLevelNum - 1];

        // 干预 自动挡
        if(AutoGame.instance.autoFlag)
            count = Math.min(count, 123);
        return count;
    }

    /**
     * 给桌子随机分配座椅数量
     */
    private _getRandomSeatCount(curIndex: number, createCount: number) {
        // 每张桌子座椅数量
        let seatCountP: number[];
        if (curIndex < createCount / 6) {
            seatCountP = [0.7, 0.3];
        } else if (curIndex < createCount / 6 * 2) {
            seatCountP = [0.6, 0.4];
        } else if (curIndex < createCount / 6 * 3) {
            seatCountP = [0.5, 0.5];
        } else if (curIndex < createCount / 6 * 4) {
            seatCountP = [0.4, 0.6];
        } else {
            seatCountP = [0.3, 0.7];
        }
        let randomNum = Math.random();
        let randomSeatCoutn = 2;
        let tempNum = 0;
        for (let i = 0; i < seatCountP.length; i++) {
            tempNum += seatCountP[i];
            if (randomNum <= tempNum) {
                randomSeatCoutn = i == 0 ? 4 : 6;
                break;
            }
        }

        randomNum = Math.ceil(Math.random() * 4);
        switch (randomNum) {
            case 1:
                randomSeatCoutn = 4;
                break;
            case 2:
                randomSeatCoutn = 6;
                break;
            case 3:
                randomSeatCoutn = 8;
                break;
            case 4:
                randomSeatCoutn = 10;
                break;
        }
        return randomSeatCoutn;
    }
}