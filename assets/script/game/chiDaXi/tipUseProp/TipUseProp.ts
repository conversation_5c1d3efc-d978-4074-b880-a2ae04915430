import { _decorator, Component, easing, Node, tween, UIOpacity, UITransform, v3, view } from 'cc';
import { Tools } from '../../../common/Tools';
import { Constants } from '../data/Constants';
const { ccclass, property } = _decorator;

/** 提示使用道具 文字提醒 */
@ccclass('TipUseProp')
export class TipUseProp extends Component {
    @property(Node)
    private bgNode: Node = null;
    @property([Node])
    private textNodes: Node[] = [];

    start() {
        this.setRandomTipText();
        this.showAnim();
    }

    setRandomTipText(){
        let randomPropIndex = Math.ceil(Math.random() * 3);
        let propTextImgName = `text_tipUseProp_${200 + randomPropIndex}`;
        let propText = this.textNodes.find(node => node.name === "Text" + 2);
        Tools.setSpriteFrame(propText, propTextImgName);

        let widthCount = 0;
        this.textNodes.forEach(textNode => widthCount += textNode.getComponent(UITransform).width);
        let bgNodeTransform = this.bgNode.getComponent(UITransform);
        bgNodeTransform.setContentSize(widthCount + 80, bgNodeTransform.height);
    }

    showAnim(){
        let scale = this.node.scale.x;
        let initScale = scale * 0.7;
        this.node.setScale(initScale, initScale, 1);
        let opacityCom = this.node.getComponent(UIOpacity);
        opacityCom.opacity = 80;
        this.node.setPosition(0, -view.getVisibleSize().height / 2 * 3 / 4);
        let showTime = 0.56;
        let displayTime = 3;
        let hideTime = 0.34;
        tween(this.node)
            .to(showTime, {scale: v3(scale, scale, 1), position: v3(0, Constants._ins.waitingAreaY + 200)}, {easing: easing.backOut})
            .delay(displayTime)
            .to(hideTime, {scale: v3(0, 0, 0)}, {easing: easing.backIn})
            .call(()=>{
                this.node.destroy();
            })
            .start();
        tween(opacityCom)
            .to(showTime / 2, {opacity: 255}, {easing: easing.sineIn})
            .delay(showTime / 2 + displayTime)
            .to(hideTime, {opacity: 0}, {easing: easing.sineOut})
            .start();
    }

    update(deltaTime: number) {
        
    }
}


