import { _decorator, Component, easing, isValid, Node, sp, tween, v3, Vec3, view } from 'cc';
import { Constants } from '../data/Constants';
import { colorIdEnum, gameStateEnum, prefabNameEnums } from '../Index';
import { Tools } from '../../../common/Tools';
import { PeopleManager } from '../people/PeopleManager';
import { Table } from './Table';
import { WaitingManager } from '../waitingArea/WaitingManager';
import { LevelData_CDX } from '../data/LevelData_CDX';
import { RuntimeData } from '../data/GameData';
import { MainGame_CDX } from '../MainGame_CDX';
import { GameModel } from '../../../model/GameModel';
import { releaseType } from '../../../Enum/Enum';
import { Particle } from '../../particle/Particle';
import { AudioManager } from '../../../manager/AudioManager';
import { VoicePackageManager } from '../../../common/VoicePackageManager';
import { TaskProgressVer2 } from '../taskProgress/TaskProgressVer2';
import { DishProductGenerator } from '../dish/DishProductGenerator';
const { ccclass, property } = _decorator;

@ccclass('TableManager')
export class TableManager extends Component {
    static _ins: TableManager;

    private tablePosArr: ITablePos[] = [];
    public curOpt: Node;
    
    public topSpace = 500;

    private _removingFullTableEnters = 0;
    public addRemovingFullTableEntersOnce(){
        this._removingFullTableEnters += 1;
    }
    public get removingFlag(){
        return this._removingFullTableEnters > 0;
    }

    protected onLoad(): void {
        TableManager._ins = this;
    }

    protected start(): void {
    }

    /** 开始游戏回调 */
    startGame() {
        this.initTablePos();
        this.createInitTable();
        // this.createAdTable();
    }

    /** 新手引导的开始游戏回调 */
    startGuideGame() {
        let tablePos = {
            row: 0,
            col: 0,
            pos: v3(0, 0),
            table: null
        };

        this.tablePosArr.push(tablePos);

        let tableConfig = LevelData_CDX._ins.getTableConfig();
        let table = this.createTable(tablePos);
        this.initTableByLevelConfig(table, tableConfig);

        let tableScale = Constants._ins.tableScale;
        let tableSize = Constants._ins.tableSize;
        WaitingManager.instance.setMaxY(this.tablePosArr[this.tablePosArr.length - 1].pos.y - tableSize.height / 2);
    }

    // 初始化桌子位置
    private initTablePos() {
        let tableScale = Constants._ins.tableScale;
        let tableSize = Constants._ins.tableSize;

        let spaceY = 150;
        let spaceX = 200;

        let rowCount = 2;
        let colCount = 2;

        let startX = -((tableSize.width + spaceX) * colCount - spaceX) / 2 + tableSize.width / 2;
        let startY = view.getVisibleSize().height / 2 - this.topSpace;
        for (let i = 0; i < rowCount; i++) {
            let y = startY - (tableSize.height + spaceY) * i;
            for (let j = 0; j < colCount; j++) {
                let x = startX + (tableSize.width + spaceX) * j;
                this.tablePosArr.push({
                    row: i,
                    col: j,
                    pos: v3(x, y),
                    table: null
                });
            }
        }

        WaitingManager.instance.setMaxY(this.tablePosArr[this.tablePosArr.length - 1].pos.y - tableSize.height / 2);
    }

    // 创建初始所有桌子
    private createInitTable() {
        this.tablePosArr.forEach((tablePos, index)=>{
            // if(index < 2) return;
            let tableConfig = LevelData_CDX._ins.getTableConfig();
            let table = this.createTable(tablePos);
            this.initTableByLevelConfig(table, tableConfig, false);
        });
        // // 决定是否开局锁定一半桌子
        // if(LevelData_CDX._ins.lockTableFlag){
        //     // 确保场内有对应颜色可操作的盘子
        //     for(let i = 0; i < 1; i++){
        //         let tablePos = this.tablePosArr[i + 2];
        //         let table = tablePos.table;
        //         if(table){
        //             table.getComponent(Table).setStepCountToUnlock();
        //         }
        //     }
        // }
    }

    /** 生成需要看广告才能获得的桌子 */
    createAdTable(){
        this.tablePosArr.forEach(tablePos=>{
            if(!tablePos.table){
                let table = Tools.newPrefab(prefabNameEnums.桌子, this.node, tablePos.pos);
                tablePos.table = table;
                let tableScript = table.getComponent(Table);

                tableScript.setAdToUnlock();
            }
        })
    }

    // 生成桌子
    createTable(tablePos: ITablePos){
        let table = Tools.newPrefab(prefabNameEnums.桌子, this.node, tablePos.pos);
        tablePos.table = table;
        let tableScale = Constants._ins.tableScale;
        table.setScale(tableScale, tableScale);

        return table;
    }

    // 根据关卡配置，初始化指定桌子
    public initTableByLevelConfig(table: Node, tableConfig: number[], autoShowCustomerFlag = true){
        if(!tableConfig) return null;

        let colorId = tableConfig[0];
        let seatCount = tableConfig.length;
        
        let tableScript = table.getComponent(Table);
        tableScript.initSeatPos(seatCount);
        tableScript.initTabel(tableConfig);
        tableScript.createCustomerOnSeat();
        tableScript.hideAllCustomer();
        if(autoShowCustomerFlag){
            this.scheduleOnce(()=>{
                tableScript.showAllCustomer();
            }, 0.34);
        }

        // 改变关卡有关
        // 是否生成加急餐桌
        // if(LevelData_CDX._ins.createVipTableFlag){
        //     let isVipFlag = Math.random() > 0.8;
        //     if (isVipFlag) {
        //         let curVipCount = 0;
        //         this.tablePosArr.forEach(tablePos=>{
        //             let table = tablePos.table;
        //             if(table && table.getComponent(Table).isVipFlag){
        //                 curVipCount++;
        //             }
        //         });
        //         if(curVipCount < 2)
        //             table.getComponent(Table).surplusStepCount = 10;
        //     }
        // }
        // 临时将生成VIP桌子逻辑，替换成锁桌子
        if (LevelData_CDX._ins.lockTableFlag && LevelData_CDX._ins.levelConfig.length > 13) {
            let flag = Math.random() > 0.8;
            if (flag) {
                let curLockedCount = 0;
                this.tablePosArr.forEach(tablePos => {
                    let table = tablePos.table;
                    if (table && table.getComponent(Table).isLockedFLag) {
                        curLockedCount++;
                    }
                });
                if (curLockedCount == 0)
                    table.getComponent(Table).setStepCountToUnlock();
            }
        }

        this.scheduleOnce(() => {
            if(isValid(table))
                tableScript?.registLabelToLabelManager();
        }, 1);
    }

    /** 展示所有顾客 */
    showAllCustomer(){
        this.tablePosArr.forEach(tablePos=>{
            let table = tablePos.table;
            if(table){
                table.getComponent(Table).showAllCustomer();
            }
        });
    }

    removeFullTable(table: Node, delayTime: number, nextTableConfig: number[]) {
        // 立即执行的
        // 消除 闪光
        let tablePos = this.tablePosArr.find(val => val.table == table);
        tablePos.table = null;
        // 生成新的
        let newTable: Node = null;
        if(nextTableConfig)
            newTable = this.createTable(tablePos);
        // 防止出现游戏结束不刷新剩余盘子数的问题
        MainGame_CDX._ins.renderSurplusCount();
        let targetScale = 0;
        if (newTable) {
            targetScale = newTable.scale.x;
            newTable.scale = v3(0, 0, 0);
        }
        // 延时执行的
        this.scheduleOnce(() => {
            AudioManager.playSound("cleanTableEff", 1);
            AudioManager.playSound("put", 1);
            let voiceCount = 1;
            // 增加催促语音
            if (RuntimeData._ins.gameState == gameStateEnum.start) {
                // console.log("播放吃饱辣语音");
                let voiceCount = 1;
                let playP = 0.7;
                let voiceName = "chiBaoLa";
                let volume = 2.5;
                VoicePackageManager.instance.playVoicePackage(voiceName, voiceCount, playP, volume);
            }
            
            // 消除旧的桌子
            let colors = table.getComponent(Table).colors;
            for(let i = 0; i < colors.length; i++){
                this.scheduleOnce(() => {
                    let colorId = colors[i];
                    let cleanTableEff = Tools.newPrefab("CleanTabelEff", this.node, table.position);
                    let spineCom = cleanTableEff.getComponentInChildren(sp.Skeleton);
                    let animName = colorId.toString();
                    let trackEntry = spineCom.setAnimation(0, animName, false);
                    spineCom.setTrackCompleteListener(trackEntry, () => {
                        cleanTableEff.destroy();
                    });
                }, 0.12 * i);
            }
            // 消除 粒子效果
            Particle.bombRibbon(this.node, table.position);
            // 桌子缩小消除
            tween(table)
                .to(0.34, { scale: v3(0, 0, 0) }, { easing: easing.backIn })
                .call(() => {
                    table.getComponent(Table).removeAllDishes();
                    table.destroy();
                })
                .start();

            // 等待一个桌子消除动画的时间
            this.scheduleOnce(() => {
                // 展示新的桌子
                // 检测待餐区
                if (newTable) {
                    // 先初始化桌子位置和盘子位置，然后立即恢复到缩放0
                    newTable.setScale(targetScale, targetScale);
                    this.initTableByLevelConfig(newTable, nextTableConfig);
                    newTable.setScale(0, 0);
                        tween(newTable)
                            .to(1, { scale: v3(targetScale, targetScale, targetScale) }, { easing: easing.backOut })
                            .call(() => {
                                Tools.callFuncUntilSuccess(
                                    () => {
                                        // 新桌子生成完毕，判断等待区上菜
                                        WaitingManager.instance.addServingEntersOnce();
                                        WaitingManager.instance.checkServing();
                                    },
                                    () => {
                                        return newTable.getComponent(Table).isReady;
                                    }
                                )
                            })
                            .start();
                }else{
                    // 没有新的桌子了，游戏成功
                    // 并且每个位置都是空的
                    if (this.tablePosArr.every(val => !val.table)) {
                        MainGame_CDX._ins.success();
                    } else {
                        // 出现了没有盘子，但是桌子还在的情况
                        // 暂时不处理这种情况，直接游戏成功

                        // 不是每个位置都是空的
                        // 1. 盘子生成区域 没有可以操作的盘子了,或者盘子没有对应的桌子(合并在一起就是没有可以直接上菜到桌子了)
                        // 2. 等待区没有盘子了,或者盘子没有对应的桌子(合并在一起就是不能服务了)
                        // 3. 其他桌子还有空位的时候
                        if (!DishProductGenerator.instance.checkOptItemHaveSameTable()
                            && !WaitingManager.instance.checkCanServe()
                            && this.tablePosArr.some(val => val.table && !val.table.getComponent(Table).isFull)
                        ) {
                            this.tablePosArr.forEach(tablePos => {
                                let table = tablePos.table;
                                if (table) {
                                    let tableScript = table.getComponent(Table);
                                    if(!tableScript.isFull)
                                        tableScript.tableFull(undefined);
                                }
                            });
                            // MainGame_CDX._ins.success();
                        }
                    }
                }
                // 完成消除，减去一个进入的
                this._removingFullTableEnters--;
            }, 0.34);
        }, delayTime);
    }

    public reduceEmptySeat(targetTable: Node){
        // let tables = this.getTables();
        // tables.forEach(table=>{
            targetTable.getComponent(Table).reduceEmptySeat();
        // });
    }

    /** 检测是否有超过3张桌子，为同一颜色 */
    public checkTableExceedNum(limitNum: number = 3): colorIdEnum[]{
        let colorIds: colorIdEnum[] = [];
        let filterColors: colorIdEnum[] = [];
        for(let i = 0; i < this.tablePosArr.length; i++){
            let table = this.tablePosArr[i].table;
            if(!table) continue;
            let tableScript = table.getComponent(Table);
            if(tableScript.isFull) continue;
            let colorId = tableScript.colorId;
            if(colorIds[colorId] === undefined){
                colorIds[colorId] = 1;
            } else {
                colorIds[colorId] += 1;
                if (colorIds[colorId] >= limitNum){
                    if(filterColors.indexOf(colorId) === -1)
                        filterColors.push(colorId);
                }
            }
        }

        if(filterColors.length > 0) return filterColors;
        else return null;
    }

    /**
     * 是否还有空位（传入颜色值，则返回包含判定颜色的结果，不传入颜色值则有空位就行）
     */
    hasEmptyTable(colorId?: colorIdEnum){
        return this.tablePosArr.some(val=>{
            return true;
        });
    }

    /** 
     * 获取所有的桌子
     * @param isSort 是否返回排序后的结果
     */
    getTables(isSort = false){
        let tables = this.tablePosArr.map(tablePos=>{
            return tablePos.table;
        }).filter(val=>{
            if(!val) return false;
            let tableScript = val.getComponent(Table);
            return !tableScript.isLockedFLag && tableScript.surplusNeedDishCount > 0;
        });

        if(isSort){
            tables.sort((a, b)=>{
                let aScript = a.getComponent(Table);
                let bScript = b.getComponent(Table);
                return aScript.surplusNeedDishCount - bScript.surplusNeedDishCount;
            });
        }

        // 把加急桌子前移
        for(let i = 1; i < tables.length; i++){
            let table = tables[i];
            let tableScript = table.getComponent(Table);
            if(tableScript.surplusStepCount > 0){
                for(let j = i; j >= 1; j--){
                    tables[j] = tables[j - 1];
                }
                tables[0] = table;
            }
        }
        let vipTables = tables.filter(table=>table.getComponent(Table).isVipFlag);
        let vipTableCount = vipTables.length;
        vipTables.sort((a, b)=>{
            let aScript = a.getComponent(Table);
            let bScript = b.getComponent(Table);

            let aNum = aScript.surplusStepCount * 5 + aScript.surplusNeedDishCount;
            let bNum = bScript.surplusStepCount * 5 + bScript.surplusNeedDishCount;

            return aNum - bNum;
        })
        for(let i = 0; i < vipTableCount; i++){
            tables[i] = vipTables[i];
        }

        return tables;
    }

    getAllShowingTableCount(){
        let count = 0;
        this.tablePosArr.forEach(tablePos=>{
            if(tablePos.table){
                count ++;
            }
        });
        return count;
    }

    /**
     * 检测所有桌子都可以放置盘子（即所有桌子都在，并且桌子没满）
     * @returns 
     */
    checkAllTableCanPlaceItem(){
        for(let i = 0; i < this.tablePosArr.length; i++){
            let table = this.tablePosArr[i].table;
            if(!table){
                return false;
            }
            let tableScript = table.getComponent(Table);
            if(tableScript.isLockedFLag) continue;
            if(tableScript.surplusNeedDishCount <= 0) return false;
        }
        return  true;
    }

    public reduceStepCount(){
        this.tablePosArr.forEach(tablePos=>{
            let table = tablePos.table;
            if(!table) return;
            let tableScript = table.getComponent(Table);
            // 减少Vip餐桌要求的剩余步数
            if(tableScript.surplusStepCount > 0){
                tableScript.surplusStepCount -= 1;
    
                if(tableScript.surplusStepCount <= 0 && !tableScript.isFull){
                    // 游戏失败
                    // if(GameModel.instance.releaseType == releaseType.test_TEST)
                        // alert("TableManager, VIP餐桌没有在规定步数内把菜上齐，游戏失败！");

                    RuntimeData._ins.gameState = gameStateEnum.pause;
                    console.log("TableManager, VIP餐桌没有在规定步数内把菜上齐，游戏失败！");
                    tableScript.flashRedLight();
                    this.scheduleOnce(() => {
                        MainGame_CDX._ins.gameOver();
                    }, 1);
                }
            }

            // 减少解锁所需步数
            if(tableScript.surplusStepCountToUnlock > 0){
                tableScript.surplusStepCountToUnlock -= 1;
            }
        });
    }

    /** 清除剩余时间（步数）为0的VIP餐桌 */
    public clearZeroSurplusStepCountTable(){
        this.tablePosArr.forEach(tablePos=>{
            let table = tablePos.table;
            if(!table) return;
            let tableScript = table.getComponent(Table);
            if(tableScript.surplusStepCount <= 0){
                tableScript.clearVipFlag();
            }
        });
    }

    /** 重置桌子颜色 */
    public resetTableColor(colorId: colorIdEnum){
        let table = this.getTables()[0];
        if(!table) return;
        let tableScript = table.getComponent(Table);
        let beforeColor = tableScript.colorId;
        tableScript.changeTableColorAnim(colorId);
        return beforeColor;
    }

    // 指定桌子上菜（效果扇形等）
    public addTableDishCount(table: Node, dish?: Node){
        let tableScript = table.getComponent(Table);
        let isFull = tableScript.addDishCount(dish);
        return isFull;
    }
    // 获取指定桌子上可以放盘子的位置
    public getDishPos(table: Node, colorId: colorIdEnum){
        return table.getComponent(Table).getDishPos(colorId);
    }

    update(deltaTime: number) {
        
    }
}

export interface ITablePos{
    row: number;
    col: number;
    pos: Vec3;
    table: Node;
}

