import { _decorator, color, Component, easing, instantiate, isValid, Label, misc, Node, Sprite, tween, UIOpacity, UITransform, v3, Vec3 } from 'cc';
import { Tools } from '../../../common/Tools';
import { PeopleManager } from '../people/PeopleManager';
import { colorIdEnum, prefabNameEnums, propIdEnum, textureNamesEnum } from '../Index';
import { MainGame_CDX } from '../MainGame_CDX';
import { TableManager } from './TableManager';
import { RuntimeData } from '../data/GameData';
import { LevelData_CDX } from '../data/LevelData_CDX';
import { AdManager } from '../../../ads/AdManager';
import { Customer } from '../people/Customer';
import { ShadowManager } from '../ShadowManager';
import { Dish } from '../dish/Dish';
import { DelayRes } from '../../../common/DelayRes';
import { Constants } from '../data/Constants';
import { GlobalLabelNodesManager } from '../../../common/GlobalLabelNodesManager';
import { Hourglass } from '../hourglass/Hourglass';
import { WaitingManager } from '../waitingArea/WaitingManager';
const { ccclass, property } = _decorator;

enum TabelState {
    prepare = 0,
    ready
}

@ccclass('Table')
export class Table extends Component {
    private _colorId: number;
    private sprNode: Node;
    private _showColorSprNode: Node;
    private _allCustomer: Node[] = [];
    private _dishes: Node[] = [];

    /** 规定步数内上齐 -1表示不是VIP，不做规定 */
    private _surplusStepCount: number = -1;
    /** 是否是VIP桌 */
    public isVipFlag: boolean = false;
    /** 是否是锁定的桌子 */
    public isLockedFLag: boolean = false;
    /** 解锁剩余所需步数 */
    private _surplusStepCountToUnlock = -1;
    /** 是否是广告位桌子 */
    private _isAdTable: boolean = false;
    /** 沙漏引用 */
    private _hourglass: Node;
    /** 座位数组 */
    seatPosArr: Vec3[] = [];
    /** 桌子配置信息 */
    private _tableConfig: number[];
    /** 座位状态数组，0表示上菜，1表示顾客开吃了 */
    private _seatStateArr: number[] = [];
    /** 颜色配置信息（决定桌子的各座位颜色） */
    private _colors: colorIdEnum[] = [];
    /** 剩余需要上菜数 */
    public surplusNeedDishCount: number;
    /** 剩余步数的label组件 */
    private surplusStepCountLabelCom: Label;

    private _state: TabelState = TabelState.prepare;

    protected onLoad(): void {
        this.sprNode = this.node.getChildByName("SprNode");
        this._showColorSprNode = this.node.getChildByName("ShowColorType");

        // 注册点击事件
        this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this);

        // 添加上素材后，添加影子
        // ShadowManager._ins.registItem(this.node.getChildByName("Colorless"));

        this.surplusStepCountLabelCom = this.node.getChildByName("SurplusStepCount").getComponent(Label);
    }

    protected start(): void {
    }

    // 初始化随机数量的座位
    public initSeatPos(seatCount: number) {
        this.surplusNeedDishCount = seatCount;

        let averageRad = Math.PI * 2 / seatCount;
        let startRad = averageRad / 2;

        // 设置好位置数组
        let r = (this.getComponent(UITransform).width / 2 - 10) * this.node.scale.x;
        for (let i = 0; i < seatCount; i++) {
            let rad = startRad + averageRad * i;
            this.seatPosArr.push(new Vec3(Math.cos(rad) * r, Math.sin(rad) * r, 0));
        }

        this._seatStateArr.length = seatCount;
        this._seatStateArr.fill(0);
    }

    public registLabelToLabelManager() {
        GlobalLabelNodesManager.instance.addLabelNode(this.surplusStepCountLabelCom.node);
    }

    // 每个桌子上安排上人
    createCustomerOnSeat() {
        this.seatPosArr.forEach(seatPos => {
            let customer = PeopleManager._ins.createCustomer(this.node.position);
            let angle = Math.atan2(seatPos.y, seatPos.x) * 180 / Math.PI;
            customer.angle = angle - 90;

            this._allCustomer.push(customer);
        });

        this.scheduleOnce(() => {
            this._state = TabelState.ready;
        }, 0.1);
    }

    /** 伴随动作隐藏所有顾客 */
    hideAllCustomerWithAnim() {
        let delayTime = 0;
        let hideTime = 0.12;
        this._allCustomer.forEach((customer, index) => {
            tween(customer)
                .delay(delayTime)
                .to(hideTime, { position: this.node.position, scale: v3(0.5, 0.5, 0.5) }, { easing: easing.sineOut })
                .call(() => {
                    customer.destroy();
                })
                .start();
            delayTime += 0; // hideTime * 3 / 4;
        });
        delayTime = hideTime;
        return delayTime;
    }
    /** 无动作隐藏所有顾客 */
    hideAllCustomer() {
        this._allCustomer.forEach(customer => {
            customer.active = false;
            customer.setPosition(this.node.position);
        });
    }

    /** 有动作展示所有顾客 */
    showAllCustomer() {
        this.seatPosArr.forEach((seatPos, index) => {
            let targetPos = this.node.getPosition();
            Vec3.add(targetPos, targetPos, seatPos);
            let customer = this._allCustomer[index];
            tween(customer)
                .delay(Math.random() * 0.5)
                .parallel(
                    tween()
                        .delay(0.5 * 3 / 4)
                        .call(() => {
                            customer.active = true;
                        }),
                    tween<Node>()
                        .to(0.5, { position: targetPos }, { easing: easing.sineInOut })
                )
                .start();
        });
    }

    public initTabel(tableConfig: number[]) {
        this._tableConfig = tableConfig;
        this.initTabelColors();
        this.sprNode.getComponent(Sprite).fillRange = 0;
        this.sprNode.children.forEach(child => child.getComponent(Sprite).fillRange = 0);

        let color1 = tableConfig[0];
        let color2StartIndex = tableConfig.findIndex(colorId => colorId != color1);
        if (color2StartIndex == -1) color2StartIndex = tableConfig.length;
        let color2 = tableConfig[color2StartIndex];
        let color1Count = color2StartIndex;
        let color2Count = tableConfig.length - color2StartIndex;

        let seatCount = this.surplusNeedDishCount;
        let averageRad = Math.PI * 2 / seatCount;
        let startRad = averageRad / 2;

        // 确定桌面扇形填充fillRange 的起始范围
        let fillStart = 1 * ((startRad - averageRad / 2) / (Math.PI * 2));
        let colors = [color1, color2];
        let colorCounts = [color1Count, color2Count];
        for (let i = 0; i < colors.length; i++) {
            let colorId = colors[i];
            if (colorId == undefined) break;

            let sprNode = this.sprNode;
            if (i > 0) sprNode = sprNode.children[i - 1];

            let sprCom = sprNode.getComponent(Sprite);
            sprCom.fillStart = fillStart;
            let fillRange = colorCounts[i] / seatCount;
            sprCom.fillRange = fillRange;

            fillStart += fillRange;

            this.setTableSpr(sprNode, colorId);
        }
    }

    setTableSpr(sprNode: Node, colorId: colorIdEnum) {
        this._colorId = colorId;

        let tableId = 100 + this._colorId;
        let imgName = textureNamesEnum.桌子 + tableId;
        Tools.setSpriteFrame(sprNode, imgName);
    }
    setTableId(colorId: colorIdEnum) {
        this._colorId = colorId;

        let tableId = 100 + this._colorId;
        let imgName = textureNamesEnum.桌子 + tableId;
        Tools.setSpriteFrame(this.sprNode, imgName);

        // let tableCenterImgName = textureNamesEnum.桌子中心 + (100 + this._colorId);
        // Tools.setSpriteFrame(this._showColorSprNode, tableCenterImgName);

        // 桌子上的菜也变颜色
        this._dishes.forEach(dish => {
            let dishScript = dish.getComponent(Dish);
            dishScript.initDish(colorId);
        });
    }
    get colorId(): colorIdEnum {
        return this._colorId;
    }
    initTabelColors() {
        let tempColorId: colorIdEnum;
        this.colors.length = 0;
        for (let i = 0; i < this._tableConfig.length; i++) {
            let colorId = this._tableConfig[i];
            if (colorId != tempColorId) {
                this._colors.push(colorId);
                tempColorId = colorId;
            }
        }
    }
    get colors(): colorIdEnum[] {
        return this._colors;
    }
    /** 根据颜色id找对应座位的索引， -1代表没有座位了*/
    getSeatIndex(colorId: colorIdEnum) {
        let startIndex = this._getStartIndex(colorId);
        if (startIndex == -1) return -1;
        let endIndex = this._getEndIndex(colorId);

        let count = endIndex + 1 - startIndex;

        for (let i = startIndex; i <= endIndex; i++) {
            let seatState = this._seatStateArr[i];
            if (seatState == 0) {
                return i;
            }
        }
        return -1;
    }
    /**
     * 返回指定颜色剩余需要上菜的座位数量
     * @param colorId 指定颜色
     * @returns 
     */
    public surplusNeedDishCountByColorId(colorId: colorIdEnum) {
        let startIndex = this._getStartIndex(colorId);
        if (startIndex == -1) return 0;
        let endIndex = this._getEndIndex(colorId);

        // let curIndex = 0;
        let surplusCount = 0;
        for (let i = startIndex; i <= endIndex; i++) {
            let seatState = this._seatStateArr[i];
            if (seatState == 0) {
                // curIndex = i;
                surplusCount++;
            }
        }
        // console.log("startIndex:", startIndex, "endIndex:", endIndex, "curIndex:", curIndex ,endIndex + 1 - curIndex);
        return surplusCount;
    }
    /** 获取颜色开始的座位索引 */
    private _getStartIndex(colorId: colorIdEnum) {
        let startIndex = this._tableConfig.findIndex(val => val == colorId);
        return startIndex;
    }
    /** 获取颜色结束的座位索引 */
    private _getEndIndex(colorId: colorIdEnum) {
        let startIndex = this._getStartIndex(colorId);
        let endIndex = this._tableConfig.findIndex((val, index) => index > startIndex && val != colorId) - 1;
        if (endIndex < 0) endIndex = this._tableConfig.length - 1;
        return endIndex;
    }

    setRandomColorId() {
        let randomColorId = RuntimeData._ins.getRandomColorId();
        this._colorId = randomColorId;

        this.setTableId(randomColorId);
    }

    /** 使用改变颜色道具改变颜色，动作 */
    changeTableColorAnim(colorId: colorIdEnum) {
        // this._colorId = colorId
        this.initTabel(this._tableConfig.fill(colorId));

        // 桌子上的菜也变颜色
        this._dishes.forEach(dish => {
            let dishScript = dish.getComponent(Dish);
            if (dishScript) {
                dishScript.initDish(colorId);
            } else {
                let dishId = 100 + colorId;
                let imgName = textureNamesEnum.盘子 + dishId;
                Tools.setSpriteFrame(dish, imgName);
            }
            dish.active = false;
        });

        let sprCom = this.sprNode.getComponent(Sprite);
        let curFillRange = sprCom.fillRange;
        sprCom.fillRange = 0;
        tween(this._showColorSprNode)
            .to(0.23, { scale: v3(0, 0, 0) }, { easing: easing.backIn })
            .call(() => {
                let tableId = 100 + this._colorId;
                let imgName = textureNamesEnum.桌子 + tableId;
                Tools.setSpriteFrame(this.sprNode, imgName);

                Tools.setSpriteFrame(this._showColorSprNode, imgName);
                this._dishes.forEach((dish, index) => {
                    this.scheduleOnce(() => {
                        let pos = dish.getPosition();
                        dish.setPosition(pos.x, pos.y + 80);
                        let targetScale = dish.scale.x;
                        dish.setScale(targetScale * 1.3, targetScale * 1.3);
                        dish.active = true;
                        tween(dish)
                            .to(0.12, { position: pos, scale: v3(targetScale, targetScale) }, { easing: easing.backIn })
                            .call(() => {
                                // sprCom.fillRange = curFillRange / this._dishes.length * (index + 1);
                            })
                            .start();
                    }, index * 0.08);
                })
            })
            .delay(0.12)
            .to(0.33, { scale: v3(0.5, 0.5, 0.5) }, { easing: easing.backOut })
            .start();

        tween(sprCom)
            .to(0.23, { fillRange: 1 })
            .start();
    }

    removeAllDishes() {
        if (this._dishes.length) {
            this._dishes.forEach(dish => dish.destroy());
            this._dishes.length = 0;
        }
    }

    /** 增加餐盘 */
    addDishCount(dishNode?: Node): boolean {
        if (this.surplusNeedDishCount <= 0) return true;

        if (dishNode) this._dishes.push(dishNode);

        this.surplusNeedDishCount--;
        if (this.surplusNeedDishCount <= 0) {
            this.node.off(Node.EventType.TOUCH_START, this.onTouchStart, this);
        }

        // 等待一个盘子飞过来的时间
        let dishMoveTime = 0.67;
        this.scheduleOnce(() => {
            let count = this.seatPosArr.length;
            // let fillRange = (count - this.surplusNeedDishCount) / count;
            // tween(this.sprNode.getComponent(Sprite))
            //     .to(0.12, { fillRange }, { easing: easing.sineInOut })
            //     .start();
            Tools.callFuncUntilSuccess(
                () => {
                    // 把之前的顾客都遍历一遍，确定所有顾客都开始吃饭了
                    for (let i = 0; i < this._seatStateArr.length; i++) {
                        let seatState = this._seatStateArr[i];
                        if (seatState == 1) {
                            let customer = this._allCustomer[i];
                            let customerScript = customer?.getComponent(Customer);
                            customerScript?.eatAnim();
                        }
                    }
                },
                () => this.isReady
            )
        }, dishMoveTime);

        // 桌子菜上齐了，移走，生成下一桌
        if (this.surplusNeedDishCount <= 0) {
            this.scheduleOnce(() => {
                // 生成主菜前，清除沙漏和倒计时数字
                this.surplusStepCountLabelCom.node.active = false;
                if (this._hourglass && isValid(this._hourglass)) {
                    // console.log("清除沙漏");
                    this._hourglass.destroy();
                }
                // 移动完成，在桌子上生成主菜
                this.createMainDish();
            }, dishMoveTime / 3);

            // 更改桌子管理器状态
            TableManager._ins.addRemovingFullTableEntersOnce();
            this.scheduleOnce(() => {
                let nextTableConfig: number[];
                let filterColor = TableManager._ins.checkTableExceedNum(2);
                // console.log("要排除的颜色:", filterColor);
                if (filterColor !== null) {
                    nextTableConfig = LevelData_CDX._ins.getTableConfigFilterColor(filterColor);
                } else {
                    nextTableConfig = LevelData_CDX._ins.getTableConfig();
                }
                this.tableFull(nextTableConfig);
            }, 0.18 + dishMoveTime);
        }

        return this.surplusNeedDishCount <= 0;
    }

    // 桌子菜上齐，满桌了
    tableFull(nextTableConfig: number[]) {
        this.surplusNeedDishCount = 0;
        let hideCustomerTime = this.hideAllCustomerWithAnim();
        TableManager._ins.removeFullTable(this.node, hideCustomerTime, nextTableConfig);
    }

    /**
     * 获取当前上菜，餐盘放置的位置
     */
    public getDishPos(colorId: colorIdEnum) {
        // let count = this.seatPosArr.length;
        // let customerIndex = count - this.surplusNeedDishCount - 1;
        let customerIndex = this.getSeatIndex(colorId);
        if (customerIndex < 0) return null;
        this._seatStateArr[customerIndex] = 1;
        let seatPos = this.seatPosArr[customerIndex];
        let pos = seatPos.clone().normalize();
        let r = (this.getComponent(UITransform).width / 2 - 23) * Constants._ins.tableScale;
        pos.multiplyScalar(r);
        pos.add(this.node.position);
        return pos;
    }

    public reduceEmptySeat() {
        // 撤掉空椅子
        let count = this.seatPosArr.length;
        let curIndex = count - this.surplusNeedDishCount;
        for (let i = curIndex; i < count; i++) {
            let seat = this._allCustomer[i];
            let dir = Tools.getPosForAngleLen(seat.angle + 90, 1000);
            let moveVec3 = v3(dir.x, dir.y);
            tween(seat)
                .by(0.23, { position: moveVec3 }, { easing: easing.sineIn })
                .call(() => {
                    seat.destroy();
                })
                .start();
        }

        // 直接填满
        let surplusNeedDishCount = this.surplusNeedDishCount;
        for (let i = 0; i < surplusNeedDishCount; i++) {
            this.addDishCount();
        }
    }


    // 在桌子上生成主菜
    public createMainDish() {
        let curMainDishId = RuntimeData._ins.curMainDishId;
        let mainDish = Tools.newPrefab(prefabNameEnums.singleSpr, this.node);
        mainDish.getComponent(Sprite).spriteFrame = DelayRes.instance.getSpriteFrameByName(`mainDish_10${curMainDishId}`);
        if (curMainDishId == 4) {
            mainDish.setPosition(5, 8);
        }
        mainDish.setScale(0, 0);
        let targetScale = this.node.getChildByName("ShowColorType").scale;
        tween(mainDish)
            .to(0.34, { scale: targetScale }, { easing: easing.backOut })
            .start();

        // console.log(DelayRes.instance.getSpriteFrameByName(`mainDish_10${curMainDishId}`), this.node.getChildByName("ShowColorType"), mainDish.getComponent(Sprite));
        // console.log("当前主菜ID", curMainDishId);
    }

    get isFull(): boolean {
        return this.surplusNeedDishCount <= 0;
    }
    get isEmpty(): boolean {
        return this.surplusNeedDishCount == this.seatPosArr.length;
    }

    get surplusStepCount(): number {
        return this._surplusStepCount;
    }
    set surplusStepCount(val: number) {
        // 初始化VIP桌子时，标记其为VIP桌子
        if (this._surplusStepCount < 0 && val > 0) {
            // console.log("VIP 桌子标记");
            this.isVipFlag = true;
            this._hourglass = Tools.newPrefab("Hourglass", this.node.getChildByName("Hourglass"));
            // this._hourglass.setScale(0.23, 0.23);
            this._hourglass.getComponent(Hourglass).initData(0, val);
        }
        this._surplusStepCount = val;
        this.surplusStepCountLabelCom.string = this._surplusStepCount.toString();
        if (isValid(this._hourglass))
            this._hourglass?.getComponent(Hourglass)?.setSurplus(val);
        // else
        //     console.log("销毁了");
    }
    /** 清除VIP标记 */
    public clearVipFlag() {
        if (!this.isVipFlag) return;

        this.isVipFlag = false;
        // console.log(this._hourglass);
        if (this._hourglass) {
            this._hourglass.destroy();
            this._hourglass = null;
        }
        this.surplusStepCount = -1;
        // this.surplusStepCountLabelCom.node.destroy();
        this.surplusStepCountLabelCom.node.active = false;
    }

    /** 锁定桌子 */
    public locked() {
        let lockedIcon = this.node.getChildByName("LockedIcon");
        lockedIcon.active = true;

        this.isLockedFLag = true;

        this.clearVipFlag();
    }
    public unlocked() {
        let lockedIcon = this.node.getChildByName("LockedIcon");
        lockedIcon.active = false;

        this.isLockedFLag = false;

        WaitingManager.instance.addServingEntersOnce();
        WaitingManager.instance.checkServing();
    }
    /** 设置桌子指定步数内是锁定状态 */
    public setStepCountToUnlock() {
        this.locked();
        this.surplusStepCountToUnlock = 8;
    }
    public get surplusStepCountToUnlock() {
        return this._surplusStepCountToUnlock;
    }
    public set surplusStepCountToUnlock(val: number) {
        this._surplusStepCountToUnlock = val;
        if (this._surplusStepCountToUnlock <= 0) {
            this.unlocked();
            this._surplusStepCountToUnlock = -1;
        }

        let lockedIcon = this.node.getChildByName("LockedIcon");
        let countLabel = lockedIcon.getComponentInChildren(Label);
        // countLabel.string = `已锁定，${val} 步后解锁`;
        countLabel.string = val.toString();
    }

    /** 设置为需要看广告解锁的桌子 */
    public setAdToUnlock() {
        this.locked();
        let lockedIcon = this.node.getChildByName("LockedIcon");
        // let countLabel = lockedIcon.getChildByName("Count").getComponent(Label);
        // countLabel.string = `看广告解锁`;
        lockedIcon.getChildByName("LockedIcon").active = false;
        Tools.setSpriteFrame(lockedIcon.getChildByName("AdIcon"), "videoAdIcon_common");

        this._isAdTable = true;
    }

    onTouchStart() {
        if (this._isAdTable) {
            RuntimeData._ins.cancelCurSelectProp();
            AdManager.showVideoAd(
                () => {
                    console.log("加载广告成功");
                    this._isAdTable = false;
                    this.unlocked();
                    RuntimeData
                    let tableConfig = LevelData_CDX._ins.getTableConfig();
                    TableManager._ins.initTableByLevelConfig(this.node, tableConfig);
                },
                () => {
                    console.error("加载广告失败");
                }
            )
            return;
        }

        // 玩法2 解开
        return;
        if (!MainGame_CDX._ins.touchFlag) return;

        // console.log("点击桌子，上菜", this._colorId);
        MainGame_CDX._ins.serveDish(this._colorId, this.node);
        TableManager._ins.curOpt = this.node;
    }

    /** 闪红 */
    flashRedLight() {
        let flashNode = instantiate(this.sprNode);
        flashNode.setParent(this.node);
        flashNode.getComponent(Sprite).color = color(255, 0, 0);
        let opacityCom = flashNode.addComponent(UIOpacity);
        opacityCom.opacity = 0;
        tween(opacityCom)
            .to(0.12, { opacity: 255 }, { easing: easing.sineOut })
            .to(0.23, { opacity: 0 }, { easing: easing.sineIn })
            .union()
            .repeat(5)
            .start();
    }

    public get isUsable(): boolean {
        return !this.isFull && !this.isLockedFLag && this._surplusStepCountToUnlock <= 0 && !this._isAdTable;
    }

    public get isReady() {
        return this._state == TabelState.ready;
    }

    protected onDestroy(): void {
        // this._allCustomer.forEach(customer=>{
        //     customer.destroy();
        // });
        if (TableManager._ins.curOpt == this.node)
            TableManager._ins.curOpt = null;

        if (this._hourglass && isValid(this._hourglass)) {
            this._hourglass.destroy();
            this._hourglass = null;
        }
    }
}


