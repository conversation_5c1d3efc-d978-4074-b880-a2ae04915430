import { UITransform, v3, Vec3 } from "cc";
import { IComponent } from "./IComponent";

export class MovementComponent implements IComponent{
    private _speed: number = 300;
    private _moveDir: Vec3;
    private _ctrlTransform: UITransform;

    constructor(ctrlTransform: UITransform, moveSpeed: number){
        this._ctrlTransform = ctrlTransform;
        this._speed = moveSpeed;
    }

    public move(dt: number): void{
        if(!this._moveDir) return;
    }

    public update(deltaTime: number): void {
        this.move(deltaTime);
    }
}