import { _decorator, As<PERSON>, But<PERSON>, Component, director, easing, error, find, game, instantiate, js, Label, Layout, Node, Prefab, size, sp, Sprite, sys, tween, UITransform, v3, view, Widget } from 'cc';
import { TableManager } from './table/TableManager';
import { ResManager } from '../../manager/ResManager';
import { bundleNamesEnum, colorIdEnum, gameStateEnum, propIdEnum, sceneNamesEnum } from './Index';
import { WaitingManager } from './waitingArea/WaitingManager';
import { UIManager } from '../../manager/UIManager';
import { RuntimeData } from './data/GameData';
import { UINamesEnum } from '../../uiPanel/UIConfig';
import { AdManager } from '../../ads/AdManager';
import { LevelData_CDX } from './data/LevelData_CDX';
import { Tools } from '../../common/Tools';
import { DishManager } from './dish/DishManager';
import { Dish } from './dish/Dish';
import { AudioManager } from '../../manager/AudioManager';
import { LoadingUI } from '../ui/loadingUI/LoadingUI';
import { LevelData_CDX_2 } from './data/LevelData_CDX_2';
import { DishProductGenerator } from './dish/DishProductGenerator';
import { Constants } from './data/Constants';
import { Table } from './table/Table';
import { GlobalLabelNodesManager } from '../../common/GlobalLabelNodesManager';
import { VoicePackageManager } from '../../common/VoicePackageManager';
import { GameModel } from '../../model/GameModel';
import { DelayRes } from '../../common/DelayRes';
import { IHandbookItemInfo } from './data/HandbookItemData';
import { Particle } from '../particle/Particle';
import { DisplayOldObjectProgress } from './taskProgress/DisplayOldObjectProgress';
import { TaskProgress } from './taskProgress/TaskProgress';
import { GuideGame } from '../../common/GuideGame';
import { AutoGame } from '../../common/AutoGame';
import { gameModeName, propNameEnum, RemoteServerData } from './data/RemoteServerData';
const { ccclass, property } = _decorator;

@ccclass('MainGame_CDX')
export class MainGame_CDX extends Component {
    public static _ins: MainGame_CDX;

    @property(Node)
    reduceEmptySeatBtn: Node = null;
    @property(Node)
    resortDishesPropBtn: Node = null;
    @property(Node)
    resetTablePropBtn: Node = null;
    @property(Node)
    autoServePropBtn: Node = null;
    @property(Node)
    taskProgressNode: Node = null;
    @property(Node)
    propBgNode: Node = null;
    @property(Prefab)
    delayResPrefab: Prefab = null;

    public usingAdPropFlag: boolean = false;

    private dishPlateSurplusCount: Node;
    private dishPlateSurplusCountLabelCom: Label;

    protected onLoad(): void {
        // 生成加载页面
        // CommonLoadingUIManager.addLoadingUI();
        RuntimeData._ins.init();

        MainGame_CDX._ins = this;

        // 玩法2 切换
        // LevelData_CDX._ins.selfInit();
        LevelData_CDX_2._ins.init();
        RuntimeData._ins.reliveCount = 0;

        this.adaptScreen();

        game.frameRate = 60;

        director.preloadScene(sceneNamesEnum.主页场景);

        let delayRes = instantiate(this.delayResPrefab);
        delayRes.setParent(this.node.getChildByName("DelayRes"));
    }

    start() {
        this.node.getComponentInChildren(LoadingUI).node.active = true;
        ResManager.instance.loadBundle(bundleNamesEnum.吃大席场景).then(
            bundle => {
                ResManager.instance.bundelLoadDir(bundleNamesEnum.吃大席场景, "",
                    this.onProgress.bind(this),
                    this.onComplete.bind(this)
                );
            }
        ).catch(err => {
            console.error(`[LoadScene] ${bundleNamesEnum.吃大席场景} 加载失败:`, err);
        });
    }

    startGame() {
        let dishPlateSurplusCount = Tools.newPrefab("DishPlateSurplusCount", this.node.getChildByName("GameLayer"));
        this.dishPlateSurplusCount = dishPlateSurplusCount;
        this.dishPlateSurplusCountLabelCom = dishPlateSurplusCount.getComponentInChildren(Label);
        GlobalLabelNodesManager.instance.addLabelNode(this.dishPlateSurplusCountLabelCom.node);
        this.renderSurplusCount();

        this.taskProgressNode.getComponent(TaskProgress).startGame();

        let delayTime = 0;
        delayTime = this.showTipGameMode();

        GameModel.instance.curBgmName = "gameBGM";
        this.scheduleOnce(() => {
            AudioManager.playMusic(GameModel.instance.curBgmName, 0.23);
        }, 0)
        this.initBg();
        // 设置道具背景板图片
        let sf = DelayRes.instance.getSpriteFrameByName(`propBg_202`);
        this.propBgNode.getComponent(Sprite).spriteFrame = sf;
        // 开启录屏
        AdManager.createGameVideo();
        this.showAllPropBtn();
        // 延迟开始游戏状态，防止过早点击
        this.scheduleOnce(() => {
            RuntimeData._ins.gameState = gameStateEnum.start;
        }, 1.2);

        WaitingManager.instance.startGame();
        if (RuntimeData._ins.isShowGuideLevel) {
            LevelData_CDX._ins.initGuideLevel();
            TableManager._ins.startGuideGame();
            GuideGame.instance.initGuide();
        } else {
            // test
            // LevelData_CDX._ins.initGuideLevel();
            TableManager._ins.startGame();
        }
        // 玩法3 
        DishProductGenerator.instance.startGame();

        this.updateRenderAdPropBtn();
        // 适配剩余餐桌数位置
        dishPlateSurplusCount.setPosition(dishPlateSurplusCount.position.x, WaitingManager.instance.waitingArea.position.y);

        this.scheduleOnce(() => {
            if (AutoGame.instance.autoFlag) {
                AutoGame.instance.init(this.node);
                AutoGame.instance.startGame(
                    this.resortDishes.bind(this),
                    this.resetColor.bind(this),
                    this.autoServing.bind(this)
                );
            }
        }, Math.random() * 2 + 1);

        // 等待一个展示条幅的时间
        this.scheduleOnce(() => {
            this.scheduleOnce(() => {
                TableManager._ins.showAllCustomer();
            }, 0.34);
        }, delayTime);
    }

    private showTipGameMode() {
        let showTime = 0.34;
        let displayTime = 0.23;

        if (LevelData_CDX._ins.isCoverByLidMode) {
            // 展示提示游戏模式的条幅
            this.scheduleOnce(() => {
                console.log("展示菜品盲盒模式条幅");
                let tip = Tools.newPrefab("TipGameMode_CoverLid", this.node, v3(-1000, 123));
                tip.setScale(1.2, 0, 1);
                tween(tip)
                    .to(showTime, { position: v3(0, tip.position.y), scale: v3(1, 1, 1) }, { easing: easing.backOut })
                    .start();

                this.scheduleOnce(() => {
                    AudioManager.playSound("ticketCheck", 1);
                    let animCom = tip.getComponentInChildren(sp.Skeleton);
                    let trackEntry = animCom.setAnimation(0, "animation", false);
                    animCom.setTrackCompleteListener(trackEntry, () => {
                        tween(tip)
                            .delay(displayTime)
                            .by(0.34, { position: v3(0, -1000), scale: v3(-1, -1, 1) }, { easing: easing.backIn })
                            .call(() => {
                                tip.destroy();
                            })
                            .start();
                    });
                }, showTime);
            }, 0.56);

            return 0.56 + showTime + 0.34;
        }
        return 0;
    }

    adaptScreen() {
        let normalHeight = 1920;
        let winHeight = view.getVisibleSize().height;
        let scale = winHeight / normalHeight;
        // 适配底部
        let waitingAreaY = Constants._ins.waitingAreaY;

        let propY = (waitingAreaY - winHeight / 2) * 4 / 6;
        if (scale > 1.2) {
            this.scheduleOnce(() => {
                let props = find("Canvas/UILayer/Props").children;
                props.forEach(prop => {
                    if (prop.name == "Bg") {
                        let bg = prop;
                        let bgTransformCom = bg.getComponent(UITransform);
                        let bgHeight = propY + 115 - (-winHeight / 2);
                        bgTransformCom.setContentSize(bgTransformCom.contentSize.width, bgHeight);
                        // bg.setScale(scale, scale);
                        bg.getComponent(Widget).updateAlignment();
                        bg.setPosition(0, -winHeight / 2 + bgHeight / 2);
                        return;
                    }
                    prop.setPosition(prop.position.x, propY);
                })
                DishProductGenerator.instance.bottom = (propY + 189) - (-winHeight / 2);

                let offsetY = -130;
                this.taskProgressNode.setPosition(this.taskProgressNode.position.x, this.taskProgressNode.position.y + offsetY * 2 / 3);
                TableManager._ins.topSpace += Math.abs(offsetY);
            }, 0);
        }
    }

    initBg() {
        let bg_top = find("Canvas/BgLayer/TopScene");
        let bg = find("Canvas/BgLayer/Bg");
        Tools.setSpriteFrame(bg, `bg_101`);
        Tools.setSpriteFrame(bg_top, `bg_${RuntimeData._ins.curBgId}00`);
    }

    /** 操作一次后回调（立即执行） */
    public optOnce() {
        TableManager._ins.reduceStepCount();

        // temp test 任何点击都会取消道具的选中。或许可能会随着逻辑改变而取消这个逻辑。
        RuntimeData._ins.curSelectPropId = null;

        // this.success();
        // this.checkSuccess();
    }

    /** 检测点击 */
    public get touchFlag(): boolean {
        let touchFlag = !(WaitingManager.instance.placingFlag
            || WaitingManager.instance.servingFlag
            || this.usingAdPropFlag);

        // console.log("检测点击");
        // 因为每次游戏逻辑都要走这里，所以放这里更新状态，减少新增数组对象
        this._hasOptDish = DishProductGenerator.instance.checkOptItemHaveSameTable();
        this._preShowTipUsePropTime = Date.now();

        return touchFlag;
    }

    checkSuccess() {
        // 判定游戏是否成功
        let generatorEmpty = DishProductGenerator.instance.checkDishProductGeneratorEmpty();
        let isSuccess = generatorEmpty && !LevelData_CDX._ins.isHaveDish();
        if (isSuccess) {
            RuntimeData._ins.gameState = gameStateEnum.preOver;
            this.scheduleOnce(() => {
                this.success();
            }, 0.56);
            return;
        }
    }

    //#region 道具按钮回调
    public resortDishes() {
        if (!this.resortDishesPropBtn.getComponent(Button).interactable) return;
        if (!this.touchFlag) return;
        if (RuntimeData._ins.gameState !== gameStateEnum.start) return;
        if (DishProductGenerator.instance.fillingFlag) return;
        AudioManager.playSound("placeDang", 1);

        if (AutoGame.instance.autoFlag)
            AutoGame.instance.registerBtnClickAnim(this.resortDishesPropBtn);


        let success = () => {
            RuntimeData._ins.propUsedInfoInCurFight[propIdEnum.resortDishesProp] += 1;
            if (RuntimeData._ins.propUsedInfoInCurFight[propIdEnum.resortDishesProp] == 3) {
                this.resortDishesPropBtn.getComponent(Button).interactable = false;
                this.resortDishesPropBtn.getComponent(Sprite).grayscale = true;
                this.checkAllBtnDisable();
            }

            console.log("重新排列菜品");
            // 获取场上的菜品
            this.usingAdPropFlag = true;
            let allShowingDishes = DishProductGenerator.instance.getAllItems();
            let tempShowingDishes = [...allShowingDishes];
            // 打乱排列顺序
            Tools.aginSortArr(tempShowingDishes);

            // 把和场内桌子同色的盘子往前提
            let tables = TableManager._ins.getTables().map(table => table.getComponent(Table));
            for (let i = 0; i < tempShowingDishes.length; i++) {
                let dish = tempShowingDishes[i];
                let dishScript = dish.getComponent(Dish);
                let dishColor: colorIdEnum = dishScript.colorId;
                let index = tables.findIndex(table => table.getComponent(Table).getSeatIndex(dishColor) !== -1);
                if (index !== -1 && i !== 0) {
                    tempShowingDishes.splice(i, 1);
                    tempShowingDishes.unshift(dish);
                }
            }

            let tempDishColorIds = tempShowingDishes.map(dish => dish.getComponent(Dish).colorId);
            let tempDishFloorCountes = tempShowingDishes.map(dish => dish.getComponent(Dish).floorCount);
            let tempPoses = tempShowingDishes.map(dish => dish.getWorldPosition());
            let poses = allShowingDishes.map(dish => dish.getWorldPosition());
            tempShowingDishes.forEach((dish, index) => {
                let dishScript = dish.getComponent(Dish);
                let hideFlag = dishScript.hideLightFrame();
                let obj = {
                    x: dish.worldPosition.x,
                    y: dish.worldPosition.y
                }
                let fallTime = 0.1 + Math.random() * 0.13;
                tween(obj)
                    .by(0.5, { x: 0, y: 200 + Math.random() * 300 }, {
                        onUpdate(target, ratio) {
                            dish.setWorldPosition(v3(target.x, target.y));
                        },
                        easing: easing.sineOut
                    })
                    .parallel(
                        tween()
                            .to(fallTime, { x: poses[index].x }, {
                                onUpdate(target, ratio) {
                                    dish.setWorldPosition(v3(target.x, target.y));
                                },
                                easing: easing.sineOut
                            }),
                        tween()
                            .to(fallTime, { y: poses[index].y }, { easing: easing.sineIn })
                    )
                    .start();

                // 落下后，重置回归到自己原本为止，然后重新渲染为调换后的颜色、层数等
                tween(dish)
                    .delay(0.5 + 0.23)
                    .call(() => {
                        allShowingDishes[index].getComponent(Dish).initDish(tempDishColorIds[index], tempDishFloorCountes[index]);
                        dish.setWorldPosition(tempPoses[index]);
                        if (hideFlag) {
                            this.scheduleOnce(() => {
                                dishScript.hideLightFrame();
                                dishScript.showLightFrame();
                            }, 1);
                        }
                    })
                    .start();
            })
            this.scheduleOnce(() => {
                this.usingAdPropFlag = false;
            }, 0.5 + 0.23 + 1);
            this.scheduleOnce(() => {
                DishProductGenerator.instance.updateRender();
            }, 0.78);
        }

        if (RuntimeData._ins.getPropById(propIdEnum.resortDishesProp)) {
            success();
            RuntimeData._ins.useProp(propIdEnum.resortDishesProp);
        } else {
            AdManager.showVideoAd(
                () => {
                    success();
                    RemoteServerData.instance.insertAppletToolsData(gameModeName.吃大席, propNameEnum.resortProp, true);
                },
                () => {
                    console.error("奖励领取失败");
                    RemoteServerData.instance.insertAppletToolsData(gameModeName.吃大席, propNameEnum.resortProp, false);
                }
            )
        }
    }
    public resetColor() {
        if (!this.resetTablePropBtn.getComponent(Button).interactable) return;
        if (!this.touchFlag) return;
        if (RuntimeData._ins.gameState !== gameStateEnum.start) return;
        AudioManager.playSound("placeDang", 1);

        if (AutoGame.instance.autoFlag)
            AutoGame.instance.registerBtnClickAnim(this.resetTablePropBtn);

        let success = () => {
            RuntimeData._ins.propUsedInfoInCurFight[propIdEnum.resetTableWithMostColorProp] += 1;
            if (RuntimeData._ins.propUsedInfoInCurFight[propIdEnum.resetTableWithMostColorProp] == 3) {
                this.resetTablePropBtn.getComponent(Button).interactable = false;
                this.resetTablePropBtn.getComponent(Sprite).grayscale = true;
                this.checkAllBtnDisable();
            }
            console.log("重置桌子颜色变为场地中数量最多菜品的颜色");
            this.usingAdPropFlag = true;
            // 获取场上的菜品
            let allShowingDishes = DishProductGenerator.instance.getAllOptItems();
            let tempArr: number[] = [];
            allShowingDishes.forEach(dish => {
                let dishScript = dish.getComponent(Dish);
                let colorId = dishScript.colorId;
                let floorCount = dishScript.floorCount;
                if (tempArr[colorId] == undefined) tempArr[colorId] = 0;
                tempArr[colorId] += floorCount;
            });

            // 再加上等待区的菜品
            let waitingDishes = WaitingManager.instance.getAllItems<Node>();
            waitingDishes.forEach(dish => {
                let dishScript = dish.getComponent(Dish);
                let colorId = dishScript.colorId;
                let floorCount = dishScript.floorCount;
                if (tempArr[colorId] == undefined) tempArr[colorId] = 0;
                tempArr[colorId] += floorCount;
            });
            // 确定场景内正在显示的菜品数量最多的颜色id
            let mostNumberColorId: colorIdEnum;
            let maxNumber = 0;
            for (let i = 0; i < tempArr.length; i++) {
                let colorId = i;
                let number = tempArr[colorId];
                if (number && number > maxNumber) {
                    mostNumberColorId = colorId;
                    maxNumber = number;
                }
            }

            let usedFlag = false;
            let table = TableManager._ins.getTables().find(table => {
                let tableScript = table.getComponent(Table);
                return !(tableScript.colors.includes(mostNumberColorId) && tableScript.colors.length <= 1);
            });
            if (table) {
                usedFlag = true;
                // 初始化场景内盘子颜色数组
                LevelData_CDX._ins.updateColorIdsInSceneArr();
                let preLength = LevelData_CDX._ins.colorIdsInScene.length;

                let tableScript = table.getComponent(Table);
                let beforeColors = [...tableScript.colors];
                // let surplusDishCount = tableScript.surplusNeedDishCount;

                // 使用道具后，将关卡配置中，
                // 从尾部开始，
                // 1. 增加一张原颜色桌子
                // 2. 改变新颜色的盘子为原颜色，数量为桌子上已经摆放的盘子数量

                // 执行1. 步骤
                // LevelData_CDX._ins.addTableConfig(beforeColor, seatCount);

                // 执行2.步骤
                // 获取最多颜色的菜品索引数组
                let colorIdsInScene = LevelData_CDX._ins.colorIdsInScene;
                let dishColorIds = LevelData_CDX._ins.dishColorIds;
                let allColorIds = [...LevelData_CDX._ins.colorIdsInScene, ...LevelData_CDX._ins.dishColorIds];
                for (let i = 0; i < beforeColors.length; i++) {
                    let beforeColor = beforeColors[i];
                    let colorIdIndexArr = LevelData_CDX._ins.getIndexArrByColorId(beforeColor);
                    let surplusDishCount = tableScript.surplusNeedDishCountByColorId(beforeColor);
                    for (let i = 0; i < surplusDishCount; i++) {
                        let index = colorIdIndexArr[colorIdIndexArr.length - 1 - i];
                        // console.log("改变盘子颜色为最多颜色：", allColorIds[index], beforeColor, mostNumberColorId, surplusDishCount);
                        allColorIds[index] = mostNumberColorId;
                        // console.log(allColorIds[index], "改变盘子颜色为最多颜色：", mostNumberColorId);
                    }
                }
                // 替换后的配置，赋值回去
                for (let i = 0; i < allColorIds.length; i++) {
                    if (i < colorIdsInScene.length) colorIdsInScene[i] = allColorIds[i];
                    else dishColorIds[i - colorIdsInScene.length] = allColorIds[i];
                }

                DishProductGenerator.instance.resetCurDishes();
                tableScript.changeTableColorAnim(mostNumberColorId);
            }

            if (!usedFlag) {
                RuntimeData._ins.addProp({
                    propId: propIdEnum.resetTableWithMostColorProp,
                    propName: Object.keys(propIdEnum)[propIdEnum.resetTableWithMostColorProp],
                    count: 1
                })
            }

            // 延迟使用道具的时间
            this.scheduleOnce(() => {
                this.usingAdPropFlag = false;
                WaitingManager.instance.addServingEntersOnce();
                WaitingManager.instance.checkServing();
            }, 0.78);
        }

        if (RuntimeData._ins.getPropById(propIdEnum.resetTableWithMostColorProp)) {
            success();
            RuntimeData._ins.useProp(propIdEnum.resetTableWithMostColorProp);
        } else {
            AdManager.showVideoAd(
                () => {
                    success();
                    RemoteServerData.instance.insertAppletToolsData(gameModeName.吃大席, propNameEnum.resetTabelColorProp, true);
                },
                () => {
                    console.error("奖励领取失败");
                    RemoteServerData.instance.insertAppletToolsData(gameModeName.吃大席, propNameEnum.resetTabelColorProp, false);
                }
            )
        }
    }
    public reduceEmptySeat() {
        if (!this.touchFlag) return;
        if (RuntimeData._ins.gameState !== gameStateEnum.start) return;
        AudioManager.playSound("placeDang", 1);

        // let useProp = ()=>{
        //     console.log("减少场内空椅子的数量");
        //     // TableManager._ins.reduceEmptySeat();
        //     RuntimeData._ins.curSelectPropId = propIdEnum.reduceEmptySeatProp;
        // }

        // let showAdSuccess = ()=>{
        //     // 增加道具数量
        //     RuntimeData._ins.addProp({
        //         propId: propIdEnum.reduceEmptySeatProp,
        //         propName: Object.keys(propIdEnum)[propIdEnum.reduceEmptySeatProp],
        //         count: 1
        //     });
        //     useProp();
        // }

        // if(RuntimeData._ins.getPropById(propIdEnum.reduceEmptySeatProp)){
        //     useProp();
        // }else{
        //     AdManager.showVideoAd(
        //         showAdSuccess.bind(this),
        //         ()=>{
        //             console.error("奖励领取失败");
        //         }
        //     );
        // }
    }

    public autoServing() {
        if (!this.autoServePropBtn.getComponent(Button).interactable) return;
        if (!this.touchFlag) return;
        if (RuntimeData._ins.gameState !== gameStateEnum.start) return;
        if (DishProductGenerator.instance.fillingFlag) return;
        AudioManager.playSound("placeDang", 1);

        if (AutoGame.instance.autoFlag)
            AutoGame.instance.registerBtnClickAnim(this.autoServePropBtn);

        let success = () => {
            // 本次对局使用道具次数（超过 2 次隐藏道具）
            RuntimeData._ins.propUsedInfoInCurFight[propIdEnum.autoServingProp] += 1;
            if (RuntimeData._ins.propUsedInfoInCurFight[propIdEnum.autoServingProp] == 3) {
                this.autoServePropBtn.getComponent(Button).interactable = false;
                this.autoServePropBtn.getComponent(Sprite).grayscale = true;
                this.checkAllBtnDisable();
            }
            console.log("自动上菜");
            let allTable = TableManager._ins.getTables(true);
            let allShowingDishes = DishProductGenerator.instance.getAllItems();
            // 分别列举所有桌子的颜色（桌子按照顺序） 和 所有盘子的颜色
            // let allTableColors = allTable.map(table=>table.getComponent(Table).colors).flat(2);
            let allTabelScripts = allTable.map(table => table.getComponent(Table));
            let allDishColors = allShowingDishes.map(dish => dish.getComponent(Dish).colorId);
            let targetTable: Node;
            for (let i = 0; i < allTabelScripts.length; i++) {
                let tableScript = allTabelScripts[i];
                allDishColors.some(colorId => {
                    if (tableScript.getSeatIndex(colorId)) {
                        targetTable = tableScript.node;
                        return true;
                    }
                    return false;
                });
            }

            let tableScript = targetTable.getComponent(Table);
            let colorIds = tableScript.colors;
            allShowingDishes.sort((a, b) => {
                return a.getComponent(Dish).floorCount - b.getComponent(Dish).floorCount;
            });
            let usedFlag = false;
            for (let i = allShowingDishes.length - 1; i >= 0; i--) {
                let item = allShowingDishes[i];
                let dish = item;
                let dishScript = dish.getComponent(Dish);
                if (colorIds.includes(dishScript.colorId)) {
                    usedFlag = true;
                    let targetGrid = DishProductGenerator.instance.getGirdByItem(dish);

                    let hideFlag = dishScript.hideLightFrame();
                    dishScript.serveTabel(
                        targetTable,
                        () => {
                            targetGrid.item = null;
                            DishProductGenerator.instance.fillTargetCol(targetGrid.cellPos[1], targetGrid.cellPos[0]);
                        },
                        () => {
                            if (hideFlag) {
                                dishScript.showLightFrame();
                            }
                        }
                    )
                    break;
                }
            }

            if (!usedFlag) {
                RuntimeData._ins.addProp({
                    propId: propIdEnum.autoServingProp,
                    propName: Object.keys(propIdEnum)[propIdEnum.autoServingProp],
                    count: 1
                })
            }
        }

        if (RuntimeData._ins.getPropById(propIdEnum.autoServingProp)) {
            success();
            RuntimeData._ins.useProp(propIdEnum.autoServingProp);
        } else {
            AdManager.showVideoAd(
                () => {
                    success();
                    RemoteServerData.instance.insertAppletToolsData(gameModeName.吃大席, propNameEnum.autoServeProp, true);
                },
                () => {
                    console.error("奖励领取失败");
                    RemoteServerData.instance.insertAppletToolsData(gameModeName.吃大席, propNameEnum.autoServeProp, false);
                }
            );
        }
    }
    updateRenderAdPropBtn() {
        if (director.getScene().name !== sceneNamesEnum.吃大席场景) return;

        let btns = [this.resetTablePropBtn, this.resortDishesPropBtn, this.autoServePropBtn];
        let propIds = [propIdEnum.resetTableWithMostColorProp, propIdEnum.resortDishesProp, propIdEnum.autoServingProp];
        btns.forEach((btnNode, index) => {
            let val = RuntimeData._ins.getPropById(propIds[index])?.count;
            if (val && val >= 1) {
                // 展示角标1
                Tools.setSpriteFrame(btnNode.getChildByName("videoAdIcon"), "haveOneIcon");
            } else {
                // 展示角标广告
                Tools.setSpriteFrame(btnNode.getChildByName("videoAdIcon"), "videoAdIcon_common");
            }
        })
    }
    /** 检测是否道具按钮全部不可用 */
    checkAllBtnDisable() {
        if (RuntimeData._ins.propUsedInfoInCurFight) {
            let allHideFlag = true;
            let keys = Object.keys(RuntimeData._ins.propUsedInfoInCurFight);
            for (let i = 0; i < keys.length; i++) {
                let key = keys[i];
                let val = RuntimeData._ins.propUsedInfoInCurFight[key];
                if (val < 2) {
                    allHideFlag = false;
                    break;
                }
            }
            if (allHideFlag) {
                RuntimeData._ins.propUsedInfoInCurFight = null;
                // let propParent = this.resetTablePropBtn.parent;
                // tween(propParent)
                //     .delay(1)
                //     .by(0.34, { position: v3(0, -780) }, { easing: easing.sineInOut })
                //     .call(() => {
                //         propParent.destroy();
                //     })
                //     .start();
            }
        }
    }
    showAllPropBtn() {
        let propParent = this.resetTablePropBtn.parent;
        let y = propParent.position.y;
        propParent.setPosition(0, y - 780);
        tween(propParent)
            .delay(1)
            .by(0.34, { position: v3(0, 780) }, { easing: easing.sineInOut })
            .start();
    }
    // #endregion 道具按钮回调结束

    placeDishToTable(dish: Node, targetTable: Node, dishColor: colorIdEnum) {
        // console.log("开始上菜", colorId);
        TableManager._ins.addTableDishCount(targetTable, dish);
        let dishMoveTime = DishManager.instance.placeDishToTable(dish, targetTable, dishColor);
        this.scheduleOnce(() => {
            // 放到桌子上的音效
            AudioManager.playSound("placeDang", 0.67);
        }, dishMoveTime / 2);
    }

    private onProgress(finished: number, total: number): void {
        // console.log(`加载进度：${finished}/${total}`);
        // CommonLoadingUIManager.updateProgress(finished, total);
        this.node.getComponentInChildren(LoadingUI).updateProgress(finished, total);
    }

    private onComplete(err: Error, assets: Asset[]): void {
        if (err) {
            error('[CDX_Scene] 加载bundle资源失败:', err);
            return;
        }

        if (assets?.length > 0) {
            console.log(sceneNamesEnum.吃大席场景 + '资源加载完成');
            assets.forEach(asset => {
                if (asset?.name) {
                    ResManager.instance.setAsset(asset.name, asset);
                }
            });
        }

        this.scheduleOnce(() => {
            this.node.getComponentInChildren(LoadingUI).node.active = false;
        }, 0);

        this.startGame();
    }

    success() {
        if (RuntimeData._ins.gameState == gameStateEnum.over) return;
        RuntimeData._ins.gameState = gameStateEnum.over;
        console.log("游戏成功");

        // let isShowSuccessUI = true;
        // 特殊菜关卡逻辑
        if (RuntimeData._ins.isSpecialDishLevel) {
            // 改变特殊菜关卡过关数
            RuntimeData._ins.successSpecialLevelOneTime = 1;
        } else {
            // 关闭新手引导开关
            if (RuntimeData._ins.isShowGuideLevel) RuntimeData._ins.is1stRunGame = false;
            RuntimeData._ins.passLevel += 1;

            // 更新进度条
            TaskProgress._ins.updateRenderWithLevelNum(RuntimeData._ins.passLevel);
            // 单独更新物品遮罩解锁进度
            TaskProgress._ins.更新老物件遮罩填充量(RuntimeData._ins.passLevel);

            // 更新闯关进度
            // 解锁新物品之前，展示当前目标物品
            let targetItemInfo = RuntimeData._ins.targetHandbookItemInfo;
            let recordCurLevel = RuntimeData._ins.passLevel;
            let recordTargetLevel = RuntimeData._ins.targetLevel;
            // this.node.getComponentInChildren(TaskProgressVer2).updateRenderWithLevelNum();
            if (RuntimeData._ins.passLevel == RuntimeData._ins.targetLevel) {
                RuntimeData._ins.passTotalLevelForRankingList += 1;
                console.log("解锁一道大席菜");
                // 解锁一道大席菜
                RuntimeData._ins.unlockNewHandbookItem();
                RuntimeData._ins.passLevel = 0;
                RuntimeData._ins.targetLevel = 0;
                // 清空邀请函信息
                RuntimeData._ins.curInvitationInfo = null;
                RuntimeData._ins.curBgId = -1;
                RuntimeData._ins.curMainDishId = -1;
                // isShowSuccessUI = false;

                // this.scheduleOnce(() => {
                //     director.preloadScene(sceneNamesEnum.主页场景);
                //     this.displayTargetItem(targetItemInfo, () => {
                //         director.loadScene(sceneNamesEnum.主页场景);
                //     }, recordCurLevel, recordTargetLevel)
                // }, 2);
                // return;
            }

            RemoteServerData.instance.saveDataToServer();
            this.scheduleOnce(() => {
                director.preloadScene(sceneNamesEnum.吃大席场景);
                this.displayTargetItem(targetItemInfo, () => {
                    director.loadScene(sceneNamesEnum.吃大席场景);
                }, recordCurLevel, recordTargetLevel);
            }, 2);
        }

        this.scheduleOnce(() => {
            // if(isShowSuccessUI)
            //     UIManager.instance.showUI(UINamesEnum.吃大席成功页面);

            // 增加开吃语音
            let voiceCount = 1;
            let playP = 1;
            let voiceName = "kaiChi";
            VoicePackageManager.instance.playVoicePackage(voiceName, voiceCount, playP, 2.5);
            AdManager.stopGameVideo();
        }, 2);
    }

    displayTargetItem(itemInfo: IHandbookItemInfo, finishCallback?: Function, passLevel = RuntimeData._ins.passLevel, targetLevel = RuntimeData._ins.targetLevel) {
        // 展示大席菜
        let displayItem = Tools.newPrefab("DisplayOldObject", this.node, v3(0, 150));
        let itemId = itemInfo.id;
        let oldObjectId = 100 + itemId;
        let delayTime = displayItem.getComponent(DisplayOldObjectProgress).initData("oldObject_" + oldObjectId, undefined, true, passLevel, targetLevel, finishCallback);

        this.scheduleOnce(() => {
            // 彩带音效提前出
            AudioManager.playLongSound("peng", 3);
        }, delayTime * 2 / 3);
        this.scheduleOnce(() => {
            Particle.successRibbon(this.node, v3(0, 251), 50);
        }, delayTime);

        // 干预自动挡 自动挡模式下自动过关
        if (AutoGame.instance.autoFlag) {
            this.scheduleOnce(() => {
                finishCallback && finishCallback();
            }, delayTime + 1.6);
        }
    }

    // checkGameOver(){
    //     if(TableManager._ins.removingFlag || WaitingManager.instance.servingFlag) return;


    // }
    gameOver() {
        if (RuntimeData._ins.gameState == gameStateEnum.over) return;
        if (RuntimeData._ins.gameState == gameStateEnum.preOver) return;
        RuntimeData._ins.gameState = gameStateEnum.preOver;

        let gameOverCb = () => {
            RuntimeData._ins.gameState = gameStateEnum.over;
            console.log("游戏结束");
            AudioManager.pauseMusic();
            AudioManager.playSound("gameOver_3", 1);
            RuntimeData._ins.reliveCount = 0;
            // 特殊菜关卡逻辑
            if (RuntimeData._ins.isSpecialDishLevel) {
                // 改变特殊菜关卡过关数
                RuntimeData._ins.successSpecialLevelOneTime = 0;
            }
            UIManager.instance.showUI(UINamesEnum.吃大席结束页面);
            AdManager.stopGameVideo();
            RemoteServerData.instance.saveDataToServer();
        }

        AutoGame.instance.setIdleStage();
        if (RuntimeData._ins.reliveCount == 0) {
            // 增加开吃语音
            let voiceCount = 1;
            let playP = 1;
            let voiceName = "cuiCu";
            let volume = 2.5;
            VoicePackageManager.instance.playVoicePackage(voiceName, voiceCount, playP, volume);

            // 询问复活
            UIManager.instance.showUI(UINamesEnum.吃大席复活页面, undefined, gameOverCb);

        } else {
            // 直接结束游戏
            console.log("直接游戏失败");
            gameOverCb();
        }
    }

    relive() {
        // 复活前初始化数据
        WaitingManager.instance.placingFlag = false;
        // WaitingManager.instance.servingFlag = false;
        RuntimeData._ins.reliveCount += 1;
        WaitingManager.instance.removeAllItemsToTempArea();

        RuntimeData._ins.gameState = gameStateEnum.start;

        TableManager._ins.clearZeroSurplusStepCountTable();
    }

    private showReliveUI() {
        UIManager.instance.showUI(UINamesEnum.吃大席复活页面);
    }

    update(deltaTime: number) {
        this._checkTipUseProp();
    }

    private _hasOptDish: boolean;
    private _preShowTipUsePropTime: number = 0;
    /** 检测是否展示提示使用道具文案 */
    private _checkTipUseProp() {
        if (RuntimeData._ins.gameState !== gameStateEnum.start) return;
        if (this._hasOptDish === false) {
            let curTime = Date.now();
            if (curTime - this._preShowTipUsePropTime > 10000) {
                let tipUseProp = Tools.newPrefab("TipUseProp", this.node);
                this._preShowTipUsePropTime = curTime;
            }
        }
    }

    public renderSurplusCount() {
        // let unShowingDishCount = LevelData_CDX._ins.surplusDishCount;
        // let showingDishCountInGenerator = DishProductGenerator.instance.getShowingDishCount();
        // let showingDishCountInWaitingArea = 0;
        // let allItemsInWaitingArea = WaitingManager.instance.getAllItems<Node>();
        // allItemsInWaitingArea.forEach(dish => {
        //     showingDishCountInWaitingArea += dish.getComponent(Dish).floorCount;
        // });
        // let surplusCount = unShowingDishCount + showingDishCountInGenerator + showingDishCountInWaitingArea;
        let surplusCount = LevelData_CDX._ins.levelConfig.length + TableManager._ins.getAllShowingTableCount();
        RuntimeData._ins.curShowingSurplusTableCount = surplusCount;
        this.dishPlateSurplusCountLabelCom.string = surplusCount.toString();
    }
}


