import { _decorator, Component, easing, Node, tween, Tween, UITransform, v3, Vec3 } from 'cc';
import { Tools } from '../../../common/Tools';
import { gameStateEnum, prefabNameEnums } from '../Index';
import { WaitingSlot } from './WaitingSlot';
import { Constants } from '../data/Constants';
import { TableManager } from '../table/TableManager';
import { Table } from '../table/Table';
import { MainGame_CDX } from '../MainGame_CDX';
import { Dish } from '../dish/Dish';
import { RuntimeData } from '../data/GameData';
import { DishManager } from '../dish/DishManager';
import { LevelData_CDX } from '../data/LevelData_CDX';
import { VoicePackageManager } from '../../../common/VoicePackageManager';
import { AudioManager } from '../../../manager/AudioManager';
import { AutoGame } from '../../../common/AutoGame';
const { ccclass, property } = _decorator;

@ccclass('WaitingManager')
export class WaitingManager extends Component {
    public static instance: WaitingManager;

    @property(Node)
    public waitingArea: Node;
    /** 临时存放区，游戏复活的时候把场内等待区的盘子放到这 */
    @property(Node)
    public tempWaitingArea: Node;
    // 待餐区 盘子层级
    @property(Node)
    public waitingAreaDishPlace: Node;
    @property(Node)
    public tempWaitingAreaDishPlace: Node;

    private _waitingSlots: Node[] = [];
    private _tempWaitingSlots: Node[] = [];
    private _allSlots: Node[] = [];

    /** 多余的盘子是否正在放置到等待区 */
    public placingFlag = false;
    /** 是否正在把等待区的盘子分发到桌子上 */
    public get servingFlag(): boolean{
        // console.log("剩余未完成数量：", this.servingEnters);
        return this.servingEnters > 0;
    };

    private _servingEnters: number = 0;
    public get servingEnters(): number{
        return this._servingEnters;
    }
    private set servingEnters(val: number){
        this._servingEnters = val;
    }
    public addServingEntersOnce(){
        this._servingEnters++;
    }

    protected onLoad(): void {
        WaitingManager.instance = this;
    }

    maxY: number;
    /**容错位置初始化 */
    startGame(){
        this.createWaitingSlot();
        this.createWaitingSlot(this.tempWaitingArea);
    }

    setMaxY(val: number){
        this.maxY = val;

        let minY = Constants._ins.waitingAreaY + 35;
        let y = (this.maxY + minY) * 3 / 4;
        let offsetY = y - this.waitingArea.position.y;
        this.node.children.forEach(child => {
            child.setPosition(child.position.x, child.position.y + offsetY);
        });
        // console.log(y, offsetY);
    }

    createWaitingSlot(parentNode: Node = this.waitingArea){
        let slotArr: Node[];
        let isCreateAdSlot = true;
        if(parentNode == this.waitingArea){
            slotArr = this._waitingSlots;
            isCreateAdSlot = true;
        }else if(parentNode == this.tempWaitingArea){
            slotArr = this._tempWaitingSlots;
            isCreateAdSlot = false;
        }

        let waitingAreaUITransform = parentNode.getComponent(UITransform);
        let waitingAreaSize = waitingAreaUITransform.contentSize;
        let waitingAreaWidth = waitingAreaSize.width;
        let waitingAreaHeight = waitingAreaSize.height;
        let left = 15, right = 5, spaceX = 1;
        let slotCount = 6;
        let targetSlotWidth = (waitingAreaWidth - left - right - spaceX * (slotCount - 1)) / slotCount;
        let minX = -waitingAreaWidth / 2 + left + targetSlotWidth / 2;
        let y = waitingAreaHeight / 2 - 45;

        let adSlotCount = 2;
        // 如果是自动挡，则不创建广告位
        if(AutoGame.instance.autoFlag){
            adSlotCount = 0;
        }

        for(let i = 0; i < slotCount; i++){
            let x = minX + (targetSlotWidth + spaceX) * i;
            let slot = Tools.newPrefab(prefabNameEnums.等待区盘子槽位, parentNode, v3(x, y, 0));
            slotArr.push(slot);

            let slotWidth = slot.getComponent(UITransform).width;
            let slotScale = targetSlotWidth / slotWidth;
            slot.setScale(slotScale, slotScale);
            slot.children.forEach(child=>{
                child.setScale(1.5, 1.5);
            })

            if(isCreateAdSlot && i >= slotCount - adSlotCount){
                slot.getComponent(WaitingSlot).setAdToUnlock();
            }
        }

        this._allSlots.push(...slotArr);
    }

            // item.setScale(itemScale, itemScale, 1);
    public placeItemToWaitingSlot(item: Node, waitingSlots = this._waitingSlots, parentNode = this.waitingAreaDishPlace, finishedCallback?: Function): boolean{
        let targetSlot = waitingSlots.find((slot, index)=>{
            let slotScript = slot.getComponent(WaitingSlot);
            return !slotScript.isAdFlag && slotScript.item == null;
        });
        if(targetSlot){
            AudioManager.playSound("placeDang", 1);

            let slotScript = targetSlot.getComponent(WaitingSlot);
            slotScript.item = item;
            Tween.stopAllByTarget(item);
            let nodePos = Tools.getToNodePosForNode(item, parentNode);
            item.setParent(parentNode);
            item.setPosition(nodePos.x, nodePos.y);
            let targetWidth = targetSlot.getComponent(UITransform).width * targetSlot.scale.x;
            let itemScale = targetWidth / item.getComponent(UITransform).width;

            let targetPos = targetSlot.getPosition();
            DishManager.instance.palceDishAnim(item, targetPos, itemScale, finishedCallback);

            if(this.tempWaitingArea.active && parentNode == this.waitingAreaDishPlace){
                item.getComponent(Dish).hideRealFloor();
            }

            // 增加开吃语音
            let voiceCount = 2;
            let playP = 0.23;
            let voiceName = "kaiChi";
            let volume = 1;
            VoicePackageManager.instance.playVoicePackage(voiceName, voiceCount, playP, volume);

            return true;
        }else{
            console.log("等待区槽位已经满了");
            return false;
        }
    }
    private placeItemToTempWaitingSlot(item:Node){
        this.placeItemToWaitingSlot(item, this._tempWaitingSlots, this.tempWaitingAreaDishPlace);

        // this.servingEnters += 1;
        this.addServingEntersOnce();
        this.scheduleOnce(()=>{
            this.checkServing();
        }, 0.5);
    }
    public removeAllItemsToTempArea(){
        this._waitingSlots.forEach(slot=>{
            let slotScript = slot.getComponent(WaitingSlot);
            if(slotScript.item){
                let item = slotScript.item;
                slotScript.item = null;
                this.placeItemToTempWaitingSlot(item);
            }
        });

        this.tempWaitingArea.active = true;

        // 居中处理
        let offsetY = -130 / 2;
        this.node.children.forEach(child=>{
            child.setPosition(child.position.x, child.position.y + offsetY);
        });
    }
    private endPlaceItemToSlot(){
        this.placingFlag = false;
        this.checkServing();
        MainGame_CDX._ins.touchFlag;
    }

    public hasEmptyPos(){
        return this._waitingSlots.some(slot=>{
            let slotScript = slot.getComponent(WaitingSlot);
            return !slotScript.isAdFlag && slotScript.item == null;
        });
    }

    /** 检测分发到桌子 */
    public checkServing(...args: any[]){
        if (RuntimeData._ins.gameState !== gameStateEnum.start) return;
        let [finishedCallback] = args;

        // this.servingFlag = true;
        let allSlots = this._allSlots;
        // if(args.length <= 0){
            // 不是因为玩家操作，引起的上菜行为，不全部遍历
            // 按颜色分类，并去数量层数最少的，舍去层数多的
            // 这样可以优先空出槽位


            // 按颜色分组
            let group: Node[] = [];
            for(let i = 0; i < allSlots.length; i++){
                let slot = allSlots[i];
                let item = slot.getComponent(WaitingSlot).item;
                if(!item) continue;

                let dishScript = item.getComponent(Dish);
                let colorId = dishScript.colorId;
                let floorCountNum = dishScript.floorCount;
                if(!group[colorId]){
                    group[colorId] = item;
                    continue;
                }

                let comtrastDish = group[colorId];
                if (floorCountNum < comtrastDish.getComponent(Dish).floorCount)
                    group[colorId] = item;
            }
            if(group.length > 0){
                let selectSlots: Node[] = [];
                for(let i = 0; i < group.length; i++){
                    let slot = allSlots.find(slot=>slot.getComponent(WaitingSlot).item == group[i]);
                    selectSlots.push(slot);
                }
                allSlots = selectSlots;
            }
        // }


        for(let i = 0; i < allSlots.length; i++){
            let slot = allSlots[i];
            let item = slot.getComponent(WaitingSlot).item;
            if(item){
                if(!item.position.equals(slot.position)){
                    continue;
                }
                if(item.getComponent(Dish).servingFlag){
                    continue;
                }

                let colorId = item.getComponent(Dish).colorId;
                // 餐车上的盘子是否有对应的桌子
                let targetTable: Node = null;
                let tables = TableManager._ins.getTables(true);
                tables.some(table => {
                    let tableScript = table.getComponent(Table);
                    let seatIndex = tableScript.getSeatIndex(colorId);
                    if (seatIndex !== -1) {
                        targetTable = table;
                        return true;
                    }
                });

                if (targetTable) { //  && !targetTable.getComponent(Table).isFull
                    // 放到桌子上
                    this.serveTable(targetTable, slot, ...args);
                    return;
                }
            }
        }

        // this.servingFlag = false;
        this.servingEnters -= 1;
        // 不需要自动上菜了
        // 判断是否还有空位，没有空位了则表示游戏结束了
        if(this.isFull() && !this.servingFlag){
            // 判断没有正在消除空桌子
            // 如果有桌子正在执行消除动作，则不进行判断游戏失败
            // 因为新桌子生成后，会再次检测等待区，所以不需要单独再做其他判定逻辑
            // 判断桌子是否都在
            if (!TableManager._ins.removingFlag && LevelData_CDX._ins.isHaveTable() && TableManager._ins.checkAllTableCanPlaceItem()) {
                // 判断所有可操作槽位都处于空闲阶段
                for (let i = 0; i < this._waitingSlots.length; i++){
                    let slot = this._waitingSlots[i].getComponent(WaitingSlot);
                    if(slot.isAdFlag) continue;
                    let item = slot.item;
                    if(!item) return;
                    if(item.getComponent(Dish).servingFlag) return;
                }
                // 判断复活等待区，所有槽位都处于空闲状态
                // 等待区的槽位不能操作，所以只判断是否有正在上菜的
                for (let i = 0; i < this._tempWaitingSlots.length; i++) {
                    let slot = this._tempWaitingSlots[i].getComponent(WaitingSlot);
                    let item = slot.item;
                    if (!item) continue;
                    if (item.getComponent(Dish).servingFlag) return;
                }
                console.log("等待区没有空位了，游戏结束");

                for(let i = 0; i < this._waitingSlots.length; i++){
                    let slot = this._waitingSlots[i].getComponent(WaitingSlot);
                    if(!slot.item) continue;
                    let dish = slot.item;
                    let dishScript = dish.getComponent(Dish);
                    dishScript.flashRedLight();
                }
                RuntimeData._ins.gameState = gameStateEnum.pause;
                this.scheduleOnce(() => {
                    MainGame_CDX._ins.gameOver();
                }, 2.3);
                return;
            }
        }

        // 判定结束
        finishedCallback && finishedCallback();

        // 有空位则继续操作
        this.refill();
        MainGame_CDX._ins.touchFlag;
        if(this.tempWaitingArea.active 
            && this._tempWaitingSlots.every(slot=>slot.getComponent(WaitingSlot).item == null)){
                this.tempWaitingArea.active = false;

                // 居中处理
                let offsetY = 130 / 2;
                this.node.children.forEach(child=>{
                    child.setPosition(child.position.x, child.position.y + offsetY);
                });

                this._waitingSlots.forEach(slot=>{
                    let dish = slot.getComponent(WaitingSlot).item;
                    dish?.getComponent(Dish).resumeShowRealFloor();
                });
                // this.node.children.forEach(child => {
                //     child.setPosition(child.position.x, child.position.y + 130 / 2 * child.scale.y);
                //     child.setScale(child.scale.y * 2, child.scale.y * 2);
                // });
        }
    }
    
    // 服务指定桌子
    serveTable(targetTable: Node, slot: Node, ...args: any[]){
        let slotScript = slot.getComponent(WaitingSlot);
        let item = slotScript.item;

        let deltaTime = 0.07;

        let tableScript = targetTable.getComponent(Table);
        let dish = item;
        let dishScript = dish.getComponent(Dish);

        dishScript.serveTabel(targetTable,
            ()=> {
                slotScript.item = null;
            },
            () => {
                this.checkServing(...args);
                if (!tableScript.isFull) {
                } else {
                    this.refill();
                    // this.servingFlag = false;
                }
            }
        );
    }

    /** 等待区向前补齐位置 */
    public refill(){
        return;
        // console.log("重新摆放位置");
        let items: Node[] = [];
        for(let i = 0; i < this._waitingSlots.length; i++){
            let slot = this._waitingSlots[i].getComponent(WaitingSlot);
            if(slot.item){
                let item = slot.item;
                slot.item = null;
                items.push(item);
            }
        }
        for(let i = 0; i < items.length; i++){
            let item = items[i];
            item.setPosition(this._waitingSlots[i].getPosition());
            this._waitingSlots[i].getComponent(WaitingSlot).item = item;
        }
    }

    /** 获取所有item */
    public getAllItems<T>(): T[]{
        return this._waitingSlots.map(slot=>slot.getComponent(WaitingSlot).item).filter(val=>!!val) as T[];
    }

    public isFull(){
        return !this._waitingSlots.some(slot=>{
            let slotScript = slot.getComponent(WaitingSlot);
            return !slotScript.isAdFlag && slotScript.item == null;
        })
    }

    /** 检测是否还有对应颜色的桌子 */
    public checkCanServe(): boolean {
        let items = this.getAllItems<Node>();
        if (items.length <= 0) return false;

        let allTables = TableManager._ins.getTables();

        let flag = false;
        for(let i = 0; i < items.length; i++){
            let item = items[i];
            let itemScript = item.getComponent(Dish);
            if(allTables.some(table=>table.getComponent(Table).getSeatIndex(itemScript.colorId))){
                flag = true;
                break;
            }
        }

        return flag;
    }

    update(deltaTime: number) {
        
    }
}