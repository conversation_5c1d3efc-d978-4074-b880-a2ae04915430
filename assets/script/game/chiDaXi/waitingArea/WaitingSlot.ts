import { _decorator, Component, Node } from 'cc';
import { AdManager } from '../../../ads/AdManager';
import { Tools } from '../../../common/Tools';
import { RuntimeData } from '../data/GameData';
import { gameStateEnum } from '../Index';
const { ccclass, property } = _decorator;

@ccclass('WaitingSlot')
export class WaitingSlot extends Component {
    item: Node;

    isAdFlag: boolean = false;
    
    start() {
        this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this);
    }

    onTouchStart(event: Event) {
        if (this.isAdFlag) {
            if (RuntimeData._ins.gameState !== gameStateEnum.start) return;
            AdManager.showVideoAd(
                ()=>{
                    console.log("播放视频完成回调");
                    this.isAdFlag = false;
                    this.unlocked();
                },
                ()=>{
                    console.log("播放视频失败回调");
                }
            )
        }
    }

    setAdToUnlock(){
        this.isAdFlag = true;

        this.lockedByAd();
    }

    lockedByAd(){
        let lockedIcon = this.node.getChildByName("LockedIcon");
        lockedIcon.active = true;
        // Tools.setSpriteFrame(lockedIcon.getChildByName("AdIcon"), "videoAdIcon");
    }
    unlocked(){
        this.node.getChildByName("LockedIcon").active = false;
    }
}


