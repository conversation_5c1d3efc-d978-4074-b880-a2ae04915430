export enum sceneNamesEnum{
    加载页场景 = "LoadScene",
    主页场景 = "HomeScene",
    吃大席场景 = "gameScene_chiDaXi",
    吃甜品 = "MainGame_MaiBing"
}

export enum prefabNameEnums{
    singleSpr = "SingleSprite",
    全局UI = "GlobalUI",
    顾客 = "Customer",
    服务员 = "Waiter",
    桌子 = "Table",
    盘子 = "DishPlate",
    等待区盘子槽位 = "WaitingSlot",
    餐车 = "Cart",
    图鉴界面物品 = "HandbookItem",
    // #region 图鉴界面相关
    书签 = "Bookmark",
    // #endregion
    // #region 成就系统相关
    achievementItem = "AchievementItem",
    /** 成就徽章详情页面 */
    achievementItemDetail = "AchievementItemDetail",
    /** 成就详情页进度条上的 图标/物品/item */
    itemOnCollectionProgress = "ItemOnCollectionProgress",
    // #endregion

    // #region主页
    请帖 = "Invitation",
    请帖内容 = "InvitationContent",
    // #endregion

    // #region 游戏界面
    // 任务进度
    /** 任务进度条上的星星 */
    starInTaskProgress = "Star_TaskProgress",
    oldObject = "OldObject",
    // #endregion


    // 接亲场景相关
    /** 亲戚朋友 */
    couplesFriend_JQ = "Friend_JQ_",
}

export enum bundleNamesEnum{
    吃大席场景 = "mainGameBundle",
    主页场景 = "homeBundle",
    接亲场景 = "jieQinBundle"
}

export enum textureNamesEnum{
    顾客 = "customer_",
    桌子 = "table_",
    桌子中心 = "tableCenter_",
    服务员 = "waiter_",
    盘子 = "dishPlate_",
    菜品 = "dish_",
    菜品名字 = "dishName_",
    // #region 图鉴相关
    书签背景 = "bookmark_",
    书签描述 = "dishTypeName_",
    // #endregion
    // #region 成就系统相关
    成就图标 = "achievementItemIcon_",
    成就名称 = "achievementItemName_"
    // #endregion
}

export enum colorIdEnum{
    红色 = 1,
    橙色,
    黄色,
    绿色,
    青色,
    蓝色,
    紫色,
    粉色,
    白色
}

export enum gameStateEnum{
    prepare = 0,
    start = 1,
    pause,
    /** 正式失败界面出现之前的状态 */
    preOver,
    over
}

export enum propIdEnum{
    /** 默认 */
    none,
    /** 清除指定桌子空位道具 */
    // reduceEmptySeatProp,
    /** 重置桌子颜色为最多菜品颜色 */
    resetTableWithMostColorProp,
    /** 重新排序正在展示的餐盘 */
    resortDishesProp,
    /** 自动上菜 */
    autoServingProp
}

export interface IProp{
    propName: string;
    propId: propIdEnum;
    count: number;
}

export interface IInvitationInfo{
    name?: string;
    content?: string;
    /** 邀请人 */
    inviterName: string[],
    startTime: number;
    dayCount: number;
}