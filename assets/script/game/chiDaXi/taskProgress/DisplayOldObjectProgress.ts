import { _decorator, easing, Label, Sprite, tween, UITransform, v3, view, Node } from "cc";
import { DisplayItemWithBgLightBase } from "../DisplayItemWithBgLightBase";
import { RuntimeData } from "../data/GameData";
import { AudioManager } from "../../../manager/AudioManager";
import { AdManager } from "../../../ads/AdManager";
import { DelayRes } from "../../../common/DelayRes";

const {ccclass, property} = _decorator;

@ccclass("DisplayOldObjectProgress")
export class DisplayOldObjectProgress extends DisplayItemWithBgLightBase {
    @property([Node])
    btnNodes: Node[] = [];

    private _finishCallback: Function;

    initData(...args: any[]) {
        let [itemSprName, itemNameSprName, animFlag, passLevel, targetLevel, finishCallback] = args;
        this._finishCallback = finishCallback;

        super.initData(itemSprName, itemNameSprName);

        let scale = 1;
        let maxWidth = view.getVisibleSize().width * 2 / 4;
        let maxHeight = view.getVisibleSize().width * 2 / 4;
        let uiTransform = this.itemSprNode.getComponent(UITransform);
        scale = Math.min(maxWidth / uiTransform.contentSize.width, maxHeight / uiTransform.contentSize.height);

        let delayTime = 0;

        this.maskSprNode.setParent(this.itemSprNode);
        this.maskSprNode.setScale(1, 1, 1);
        this.maskSprNode.setPosition(0, 0, 0);
        this.itemSprNode.setScale(scale, scale, 1);

        // 如果不是通关关卡，隐藏获得物品标题，展示百分比数字
        // if (RuntimeData._ins.passLevel !== RuntimeData._ins.targetLevel) {
            let rootNode = this.node.getChildByName("Root");
            rootNode.getChildByName("Title").active = false;

            // 生成老物件解锁进度百分比
            let percentLabelNode = new Node("PercentLabel");
            let percentLabel = percentLabelNode.addComponent(Label);
            percentLabel.lineHeight = 80;
            percentLabel.fontSize = 70;
            percentLabel.isBold = true;
            percentLabel.string = this.numberToPercentSign(0);
            this.scheduleOnce(() => {
                percentLabel.useSystemFont = false;
                percentLabel.font = DelayRes.instance.getFontByName("percentFont_TaskProgress");
                percentLabel.spacingX = -5;
            }, 0);
            percentLabelNode.setParent(rootNode);
            percentLabelNode.setPosition(0, -maxHeight / 2);

            let curNum = passLevel == undefined ? RuntimeData._ins.passLevel : passLevel;
            let targetNum = targetLevel == undefined ? RuntimeData._ins.targetLevel : targetLevel;
            if (animFlag) {
                let showMaskTime = 0.45;
                let fillRange = 1 - curNum / targetNum;
                let maskSprCom = this.maskSprNode.getComponent(Sprite);
                maskSprCom.fillRange = 1 - (curNum - 1) / targetNum;
                tween(maskSprCom)
                    .to(showMaskTime, { fillRange })
                    .start();

                let obj = {
                    percent: 0
                }
                tween(obj)
                    // .delay(0.34)
                    .to(showMaskTime,
                        { percent: Math.ceil(curNum / targetNum * 100) },
                        {
                            onUpdate: (target, ratio) => {
                                percentLabel.string = this.numberToPercentSign(Math.ceil(target.percent));
                            }
                        }
                    )
                    .start();

                let repeatCount = 8;
                tween(this.itemSprNode)
                    .repeat(
                        repeatCount,
                        tween()
                            .to(showMaskTime / repeatCount / 2, { scale: v3(scale * 0.98, scale * 0.98, 1) }, { easing: easing.sineOut })
                            .to(showMaskTime / repeatCount / 2, { scale: v3(scale, scale, 1) }, { easing: easing.sineIn })
                    )
                    .start();
                let playAudioCount = Math.min(10, Math.ceil(curNum / targetNum * 100));
                this.schedule(()=>{
                    AudioManager.playSound("placeDang", 0.5);
                    AdManager.shortVibrate();
                }, showMaskTime / playAudioCount, playAudioCount - 1);

                let showItemTime = 0.34;
                tween(this.itemSprNode)
                    .delay(showMaskTime)
                    .to(showItemTime * 2 / 3, { scale: v3(scale * 0.8, scale * 0.8, 1) }, { easing: easing.sineIn })
                    .to(showItemTime / 3, { scale: v3(scale, scale, 1) }, { easing: easing.backOut })
                    .call(() => {
                        this.showBgLight();
                    })
                    .start();
                delayTime = delayTime + showMaskTime + showItemTime;
            } else {
                this.maskSprNode.active = false;
                this.itemSprNode.setScale(scale, scale);
                percentLabel.string = this.numberToPercentSign(Math.ceil(curNum / targetNum * 100));
            }
        // }

        // 获取新物品
        this.scheduleOnce(() => {
            if (passLevel == targetLevel) {
                let title = rootNode.getChildByName("Title");
                title.setScale(3, 3, 3);
                title.active = true;
                tween(title)
                    .to(0.23, { scale: v3(1, 1, 1) }, { easing: easing.backOut })
                    .start();

                let descriptionNode = rootNode.getChildByName("Description");
                descriptionNode.setScale(0, 0, 0);
                descriptionNode.active = true;
                tween(descriptionNode)
                    .delay(0.185)
                    .to(0.23, { scale: v3(1, 1, 1) }, { easing: easing.backOut })
                    .start();
                this.btnNodes.find(btnNode=>btnNode.name == "ShareBtn").active = true;
            }

            this.btnNodes.forEach((btnNode, index)=>{
                tween(btnNode)
                    .delay(0.23 * (index + 1 + 0.67))
                    .to(0.23, { scale: v3(1, 1, 1) }, { easing: easing.backOut })
                    .start();
            });
        }, delayTime + 1);

        return delayTime;
    }

    nextBtnCallback(){
        this._finishCallback && this._finishCallback();
    }

    shareBtnCallback(){
        AdManager.shareGame("我在吃大席解锁了新的老物件！！！");
    }

    private numberToPercentSign(num: number): string {
        return `${num}:`;
    }
}