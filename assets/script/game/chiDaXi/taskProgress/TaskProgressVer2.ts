import { _decorator, Component, easing, Label, Layout, Node, Sprite, tween, UIOpacity, UITransform, v3, view } from 'cc';
import { RuntimeData } from '../data/GameData';
import { Tools } from '../../../common/Tools';
import { GlobalLabelNodesManager } from '../../../common/GlobalLabelNodesManager';
import { prefabNameEnums } from '../Index';
import { DelayRes } from '../../../common/DelayRes';
const { ccclass, property } = _decorator;

@ccclass('TaskProgressVer2')
export class TaskProgressVer2 extends Component {
    public static _ins: TaskProgressVer2;

    @property(Label)
    private curLevelLabel: Label = null;
    @property(Node)
    private curLevelNodeBro: Node = null;

    private _dayCount: number;
    private _curDayNum: number;

    private _starParent: Node;
    private _lineParent: Node;

    private _widthCount: number = 700;
    private _stars: Node[] = [];

    private _oldObjectMask: Sprite;

    private _percentLabel: Label;

    protected onLoad(): void {
        this.init();

        // this.updateRenderWithoutAnim();
        this.scheduleOnce(() => {
            this.curLevelLabel.node.parent.getComponent(Layout).enabled = false;
            this.updateRenderWithLevelNum();
            GlobalLabelNodesManager.instance.addLabelNode(this.curLevelLabel.node);
            // this.curLevelNodeBro.active = true;
        }, 0.23);
    }

    private init(){
        this._widthCount = this.node.getComponent(UITransform).contentSize.width / 5 * RuntimeData._ins.targetLevel;

        this._dayCount = RuntimeData._ins.targetLevel;
        this._curDayNum = RuntimeData._ins.passLevel;

        this._starParent = this.node.getChildByName("Stars");
        this._lineParent = this.node.getChildByName("Lines");
        
        // 生成星星
        for (let i = 0; i < this._dayCount; i++) {
            let starNode = Tools.newPrefab(prefabNameEnums.starInTaskProgress, this._starParent);
            Tools.setSpriteFrame(starNode, "star_taskProgressVer2");
            starNode.setPosition(-this._widthCount / 2 + this._widthCount / (this._dayCount - 1) * i, 0);
            this._stars.push(starNode);
            let 亮星星 = starNode.getChildByName("LightStar");
            Tools.setSpriteFrame(亮星星, "lightStar_taskProgressVer2");
            亮星星.active = false;

            let labelInStar = starNode.getComponentInChildren(Label);
            labelInStar.string = (i + 1).toString();
            this.scheduleOnce(() => {
                labelInStar.node.setParent(this.node, true);
                GlobalLabelNodesManager.instance.addLabelNode(labelInStar.node);
            }, 0);
        }
        // 生成虚线线条
        let spaceX = 5;
        let singleLineWidth = 26;
        let singleLineCount = (this._widthCount + spaceX) / (singleLineWidth + spaceX);
        let firstX = -this._widthCount / 2 + singleLineWidth / 2;
        for (let i = 0; i < singleLineCount; i++) {
            let lineNode = Tools.newPrefab(prefabNameEnums.singleSpr, this._lineParent)
            Tools.setSpriteFrame(lineNode, "line_taskProgressVer2");
            let posX = firstX + (singleLineWidth + spaceX) * i;
            lineNode.setPosition(posX, 0);
        }

        // 生成目标老物件
        let targetOldObjectParentNode = this.node.getChildByName("TargetOldObject");
        let oldObject = Tools.newPrefab(prefabNameEnums.oldObject, targetOldObjectParentNode);
        let targetItemInfo = RuntimeData._ins.targetHandbookItemInfo;
        let itemId = targetItemInfo.id;
        let oldObjectId = 100 + itemId;
        let oldObjectSprNode = oldObject.getChildByName("SprNode");
        Tools.setSpriteFrame(oldObjectSprNode, "oldObject_" + oldObjectId);
        let oldObjectMask = oldObject.getChildByName("Mask");
        Tools.setSpriteFrame(oldObjectMask, "oldObject_" + oldObjectId);
        this._oldObjectMask = oldObjectMask.getComponent(Sprite);
        let oldObjectWidth = oldObjectSprNode.getComponent(UITransform).contentSize.width;
        let spaceXWithTaskProgress = 75;
        let maxWidth = 180;
        let maxHeight = 130;
        let sprScale = Math.min(maxWidth / oldObjectWidth, maxHeight / oldObjectSprNode.getComponent(UITransform).contentSize.height);
        oldObjectSprNode.setScale(sprScale, sprScale, 1);
        oldObjectMask.setScale(sprScale, sprScale, 1);

        oldObject.setPosition(this._widthCount / 2 + spaceXWithTaskProgress + oldObjectWidth / 2 * sprScale, 0);
        this.node.setPosition(0, view.getVisibleSize().height / 2 - 251);

        // 生成老物件解锁进度百分比
        let percentLabelNode = new Node("PercentLabel");
        let percentLabel = percentLabelNode.addComponent(Label);
        percentLabel.lineHeight = 80;
        percentLabel.fontSize = 50;
        percentLabel.isBold = true;
        percentLabel.string = this.numberToPercentSign(0);
        percentLabelNode.setParent(targetOldObjectParentNode);
        percentLabelNode.setPosition(oldObject.position.x, oldObject.position.y - oldObject.getComponent(UITransform).contentSize.height / 2 - 12);
        this._percentLabel = percentLabel;
        this.scheduleOnce(() => {
            percentLabel.useSystemFont = false;
            percentLabel.font = DelayRes.instance.getFontByName("percentFont_TaskProgress");
            percentLabel.spacingX = -5;
            GlobalLabelNodesManager.instance.addLabelNode(percentLabelNode);
        }, 0);

        this.updateRenderCurLevel();
    }

    private updateRenderWithoutAnim() {
        // if (this._curDayNum < 1) return;
        // 根据关卡点亮星星
        // for(let i = 0; i < this._curDayNum; i++){
            this.点亮指定星星(this._stars[this._curDayNum - 1], false);
            this.更新老物件遮罩填充量(this._curDayNum, false);
    }

    public updateRenderWithLevelNum(curNum = RuntimeData._ins.passLevel) {
        // if(curNum < 1) return;

        // 根据关卡点亮星星
        for(let i = 0; i < this._stars.length; i++){
            let star = this._stars[i];
            star.getChildByName("LightStar").active = false;
        }
        this.点亮指定星星(this._stars[curNum], true); //  - 1
        this.更新老物件遮罩填充量(curNum);
    }

    private 点亮指定星星(指定星星: Node, 是否有缓动动画 = true){
        let 亮星星 = 指定星星.getChildByName("LightStar");
        亮星星.active = true;

        let 延迟销毁指定星星的时间 = 0;
        let 亮星星目标缩放值 = 指定星星.scale.x * 1.23;

        if (是否有缓动动画) {
            let uiOpacity = 亮星星.getComponent(UIOpacity);
            uiOpacity.opacity = 0;
            let 透明度缓入时间 = 0.23;
            延迟销毁指定星星的时间 = 透明度缓入时间;
            tween(uiOpacity)
                .to(透明度缓入时间, { opacity: 255 }, { easing: easing.linear })
                .start();
            亮星星.setScale(0, 0, 0);
            tween(亮星星)
                .to(透明度缓入时间 * 1.2, { scale: v3(亮星星目标缩放值, 亮星星目标缩放值, 亮星星目标缩放值) }, { easing: easing.backOut })
                .start();
        }

        this.scheduleOnce(() => {
            亮星星.setScale(亮星星目标缩放值, 亮星星目标缩放值, 亮星星目标缩放值);
        }, 延迟销毁指定星星的时间);
    }

    public 更新老物件遮罩填充量(curNum = RuntimeData._ins.passLevel, animFlag = true){
        let fillRange = 1 - curNum / this._dayCount;
        if (!animFlag) {
            this._oldObjectMask.fillRange = fillRange;
            this._percentLabel.string = this.numberToPercentSign(Math.round((1 - fillRange) * 100));
        }else {
            tween(this._oldObjectMask)
                .to(0.23, { fillRange }, { easing: easing.sineInOut })
                .start();

            let obj = {
                percent: Math.floor(this._curDayNum / this._dayCount * 100)
            }
            tween(obj)
                .to(0.23, 
                    { percent: Math.floor((1 - fillRange) * 100) },
                    {
                        onUpdate: (target, ratio)=>{
                            this._percentLabel.string = this.numberToPercentSign(Math.ceil(target.percent));
                        }
                    }
                )
                .start();
        }
    }

    public updateRenderCurLevel(){
        let curLevel = RuntimeData._ins.curLevel;
        let val = RuntimeData._ins.passTotalLevelForRankingList + 1;
        this.curLevelLabel.string = val.toString();
        // this.scheduleOnce(()=>{
        //     // 改变关卡label后，同步好兄弟的宽度
        //     this.curLevelNodeBro.getComponent(UITransform).setContentSize(this.curLevelLabel.node.getComponent(UITransform).contentSize);
        // }, 0);
    }

    private numberToPercentSign(num: number): string{
        return `${num} :`;
    }
}
