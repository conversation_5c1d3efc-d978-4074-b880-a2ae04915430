import { _decorator, Component, easing, Label, Layout, Node, size, Size, sp, Sprite, tween, UIOpacity, UITransform, v3, view, Widget } from 'cc';
import { RuntimeData } from '../data/GameData';
import { Tools } from '../../../common/Tools';
import { DishesData } from '../data/DishesData';
import { LevelData_CDX } from '../data/LevelData_CDX';
import { DishProductGenerator } from '../dish/DishProductGenerator';
import { WaitingManager } from '../waitingArea/WaitingManager';
import { Dish } from '../dish/Dish';
import { GlobalLabelNodesManager } from '../../../common/GlobalLabelNodesManager';
import { DelayRes } from '../../../common/DelayRes';
import { prefabNameEnums } from '../Index';
const { ccclass, property } = _decorator;

type itemType = "Bar" | "Frame" | "Bg";

@ccclass('TaskProgress')
export class TaskProgress extends Component {
    public static _ins: TaskProgress;

    @property(Label)
    private curLevelLabel: Label = null;

    private _bgNode: Node;
    private _barNode: Node;
    private _frameNode: Node;
    private _progressBar: Sprite;
    private _progressBallNodes: Node[] = [];

    private _dayCount: number;
    private _curDayNum: number;

    private _widthCount: number = 700;

    private _oldObjectMask: Sprite;

    private _percentLabel: Label;

    // private _male: Node;
    // private _female: Node;

    public startGame(): void {
        TaskProgress._ins = this;

        this.init();

        this.scheduleOnce(() => {
            this.curLevelLabel.node.parent.getComponent(Layout).enabled = false;
            this.updateRenderWithLevelNum();
            GlobalLabelNodesManager.instance.addLabelNode(this.curLevelLabel.node);
        }, 0.23);
    }

    private init(){
        this._widthCount = this.node.getComponent(UITransform).contentSize.width / 3 * RuntimeData._ins.targetLevel;
        let contentSize = this.node.getComponent(UITransform).contentSize;
        this.node.getComponent(UITransform).setContentSize(this._widthCount, contentSize.height);
        this.node.getComponentsInChildren(Widget).forEach(widgetCom=>{
            widgetCom.enabled = true;
            widgetCom.updateAlignment()
        });

        this._dayCount = RuntimeData._ins.targetLevel;
        this._curDayNum = RuntimeData._ins.passLevel;

        this._bgNode = this.node.getChildByName("Bg");
        this._barNode = this.node.getChildByName("Bar");
        this._frameNode = this.node.getChildByName("Frame");

        Tools.setSpriteFrame(this._bgNode, "bg_taskProgress_1");
        Tools.setSpriteFrame(this._bgNode.children[0], "bg_taskProgress_2");
        Tools.setSpriteFrame(this._barNode, "bar_taskProgress");
        
        // 总宽度
        let totalWidth = this._widthCount; //  this._barNode.getComponent(UITransform).contentSize.width;  // 
        this._progressBar = this._barNode.getComponent(Sprite);
        // 生成进度条星星们
        let lineCount = this._dayCount;
        let aveWidth = totalWidth / lineCount;
        let firstX = -totalWidth / 2;
        for (let i = 0; i < lineCount + 1; i++) {
            let imgName = "star_taskProgressVer2";
            let star = Tools.newPrefab("Star_TaskProgress", this._barNode);
            Tools.setSpriteFrame(star, imgName);
            let 亮星星 = star.getChildByName("LightStar");
            Tools.setSpriteFrame(亮星星, imgName);
            亮星星.active = false;
            star.setPosition(firstX + (i * aveWidth), 0);

            let labelInStar = star.getComponentInChildren(Label);
            labelInStar.string = i.toString();
            this._progressBallNodes.push(star);

            // 屏蔽第一颗星星
            if (i == 0) star.active = false;
            // 把label节点注册到统一管理
            if(i > 0){
                this.scheduleOnce(() => {
                    GlobalLabelNodesManager.instance.addLabelNode(labelInStar.node);
                }, 0);
            }
            // // 生成新郎
            // if(i == 0){
            //     this._male = Tools.newPrefab("Male_TaskProgress", this.node);
            //     this._male.setPosition(firstX + (i * aveWidth) - 15, 28);
            // }
            // // 生成新娘
            // if(i == lineCount){
            //     this._female = Tools.newPrefab("Female_TaskProgress", this.node);
            //     this._female.setPosition(firstX + (i * aveWidth) + 70, 28);
            // }
        }

        // 生成目标老物件
        let targetOldObjectParentNode = this.node.getChildByName("TargetOldObject");
        let oldObject = Tools.newPrefab(prefabNameEnums.oldObject, targetOldObjectParentNode);
        let targetItemInfo = RuntimeData._ins.targetHandbookItemInfo;
        let itemId = targetItemInfo.id;
        let oldObjectId = 100 + itemId;
        let oldObjectSprNode = oldObject.getChildByName("SprNode");
        Tools.setSpriteFrame(oldObjectSprNode, "oldObject_" + oldObjectId);
        let oldObjectMask = oldObject.getChildByName("Mask");
        Tools.setSpriteFrame(oldObjectMask, "oldObject_" + oldObjectId);
        this._oldObjectMask = oldObjectMask.getComponent(Sprite);
        let oldObjectWidth = oldObjectSprNode.getComponent(UITransform).contentSize.width;
        let spaceXWithTaskProgress = 75;
        let maxWidth = 180;
        let maxHeight = 130;
        let sprScale = Math.min(maxWidth / oldObjectWidth, maxHeight / oldObjectSprNode.getComponent(UITransform).contentSize.height);
        oldObjectSprNode.setScale(sprScale, sprScale, 1);
        oldObjectMask.setScale(sprScale, sprScale, 1);

        oldObject.setPosition(totalWidth / 2 + spaceXWithTaskProgress + oldObjectWidth / 2 * sprScale, 0);
        this.node.setPosition(0, view.getVisibleSize().height / 2 - 251);
        // oldObject.active = false;

        // 生成老物件解锁进度百分比
        let percentLabelNode = new Node("PercentLabel");
        let percentLabel = percentLabelNode.addComponent(Label);
        percentLabel.lineHeight = 80;
        percentLabel.fontSize = 50;
        percentLabel.isBold = true;
        percentLabel.string = this.numberToPercentSign(0);
        percentLabelNode.setParent(targetOldObjectParentNode);
        percentLabelNode.setPosition(oldObject.position.x, oldObject.position.y - oldObject.getComponent(UITransform).contentSize.height / 2 - 12);
        this._percentLabel = percentLabel;
        this.scheduleOnce(() => {
            percentLabel.useSystemFont = false;
            percentLabel.font = DelayRes.instance.getFontByName("percentFont_TaskProgress");
            percentLabel.spacingX = -5;
            percentLabel.cacheMode = Label.CacheMode.BITMAP;
            GlobalLabelNodesManager.instance.addLabelNode(percentLabelNode);
        }, 0);

        this.updateRenderWithoutAnim();
        this.updateRenderCurLevel();
    }

    // private progressComponentFactory(totalWidth: number, type: itemType, parentNode: Node){
    //     let typeStr = type.toString();
    //     if (type == "Bar") typeStr = "";
        
    //     let lineCount = this._dayCount;
    //     let aveWidth = totalWidth / lineCount;
    //     let firstX = -totalWidth / 2;

    //     // 生成进度条连线们
    //     let barImgName = "";
    //     barImgName += typeStr.toLowerCase();
    //     if (typeStr == "")
    //         barImgName += "bar_taskProgress";
    //     else
    //         barImgName += "Bar_taskProgress";
    //     if (type == "Frame") {
    //         for (let i = 0; i < lineCount; i++) {
    //             let line = Tools.newSprite(barImgName, parentNode);
    //             line.setPosition(firstX + aveWidth / 2 + (i * aveWidth), 0);
    //             let lineTransformCom = line.getComponent(UITransform);
    //             let contentSize = lineTransformCom.contentSize;
    //             lineTransformCom.setContentSize(aveWidth, contentSize.height);
    //             line.getComponent(Sprite).type = Sprite.Type.SLICED;
    //         }
    //     } else {
    //         let line = Tools.newSprite(barImgName, parentNode);
    //         line.setPosition(0, 0);
    //         let lineTransformCom = line.getComponent(UITransform);
    //         let contentSize = lineTransformCom.contentSize;
    //         lineTransformCom.setContentSize(totalWidth, contentSize.height);
    //         let lineSpriteCom = line.getComponent(Sprite);
    //         lineSpriteCom.type = Sprite.Type.SLICED;
    //         if (type == "Bar") {
    //             lineSpriteCom.type = Sprite.Type.FILLED;
    //             lineSpriteCom.fillType = Sprite.FillType.HORIZONTAL;
    //             lineSpriteCom.fillStart = 0;
    //             lineSpriteCom.fillRange = 0;
    //             this._progressBar = lineSpriteCom;
    //         }
    //     }
    //     // 生成进度条球球们
    //     for(let i = 0; i < lineCount + 1; i++){
    //         let imgName = "";
    //         if(i == 0) imgName += "left";
    //         else if(i == lineCount) imgName += "right";
    //         else imgName += "center";
    //         imgName += typeStr;
    //         imgName += "Ball_taskProgress";

    //         let ball = Tools.newSprite(imgName, parentNode);
    //         ball.setPosition(firstX + (i * aveWidth), 0);

    //         if(type == "Bar"){
    //             ball.active = false;
    //             this._progressBallNodes.push(ball);
    //         }
    //     }
    // }

    private updateRenderWithoutAnim() {
        let fillRange = this._curDayNum / this._dayCount;
        this._progressBar.fillRange = fillRange;
        for (let i = 0; i <= this._curDayNum; i++) {
            // this._progressBallNodes[i].active = true;
            let star = this._progressBallNodes[i];
            Tools.setSpriteFrame(star, "star_taskProgressVer2");
        }
        // let contentSize = this.node.getComponent(UITransform).contentSize;
        // let barWidth = contentSize.width;
        // let x = -barWidth / 2 + fillRange * barWidth - 15;
        // this._male.setPosition(x, this._male.position.y);
        this.更新老物件遮罩填充量(this._curDayNum, true);
    }

    public updateRenderWithLevelNum(curNum = RuntimeData._ins.passLevel) {
        let fillRange = curNum / this._dayCount;
        this._curDayNum = curNum;
        // console.log(fillRange, curNum, this._dayCount);
        tween(this._progressBar)
            .to(0.34, { fillRange }, { easing: easing.sineInOut })
            .call(() => {
                for (let i = 0; i <= curNum; i++) {
                    // this._progressBallNodes[i].active = true;
                    let star = this._progressBallNodes[i];
                    // Tools.setSpriteFrame(star, "lightStar_taskProgress");
                    this.点亮指定星星(star);
                }
            })
            .start();
        this.更新老物件遮罩填充量(curNum, true);
    }

    public updateRenderInCurLevel(curDishCount?: number, dishCount?: number) {
        if (dishCount == undefined) dishCount = LevelData_CDX._ins.dishCount;
        if(curDishCount == undefined) {
            let unShowingDishCount = LevelData_CDX._ins.surplusDishCount;
            let showingDishCountInGenerator = DishProductGenerator.instance.getShowingDishCount();
            let showingDishCountInWaitingArea = 0;
            let allItemsInWaitingArea = WaitingManager.instance.getAllItems<Node>();
            allItemsInWaitingArea.forEach(dish=>{
                showingDishCountInWaitingArea += dish.getComponent(Dish).floorCount;
            });
            curDishCount = dishCount - (unShowingDishCount + showingDishCountInGenerator + showingDishCountInWaitingArea);
        }

        let fillRange = this._curDayNum / this._dayCount;
        
        let detailFillRange = curDishCount / dishCount * (1 / this._dayCount);
        fillRange += detailFillRange;
        // console.log(fillRange, curDishCount, dishCount);
        tween(this._progressBar)
            .to(0.12, { fillRange }, { easing: easing.sineInOut })
            .start();

        let contentSize = this.node.getComponent(UITransform).contentSize;
        let barWidth = contentSize.width;
        // let x = -barWidth / 2 + fillRange * barWidth - 15;
        // this._male.setPosition(x, this._male.position.y);

        // if(fillRange >= 1){
        //     this._male.getComponent(sp.Skeleton).addAnimation(0, "qiuhun", false);
        // }
    }

    public updateRenderCurLevel() {
        let val = RuntimeData._ins.passTotalLevelForRankingList + 1;
        this.curLevelLabel.string = val.toString();
    }

    private 点亮指定星星(指定星星: Node, 是否有缓动动画 = true) {
        let 亮星星 = 指定星星.getChildByName("LightStar");
        if(亮星星.active) return;

        亮星星.active = true;

        let 延迟销毁指定星星的时间 = 0;
        let 亮星星目标缩放值 = 指定星星.scale.x * 1.23;

        if (是否有缓动动画) {
            let uiOpacity = 亮星星.getComponent(UIOpacity);
            uiOpacity.opacity = 0;
            let 透明度缓入时间 = 0.23;
            延迟销毁指定星星的时间 = 透明度缓入时间;
            tween(uiOpacity)
                .to(透明度缓入时间, { opacity: 255 }, { easing: easing.linear })
                .start();
            亮星星.setScale(0, 0, 0);
            tween(亮星星)
                .to(透明度缓入时间 * 1.2, { scale: v3(亮星星目标缩放值, 亮星星目标缩放值, 亮星星目标缩放值) }, { easing: easing.backOut })
                .start();
        }

        this.scheduleOnce(() => {
            亮星星.setScale(亮星星目标缩放值, 亮星星目标缩放值, 亮星星目标缩放值);
        }, 延迟销毁指定星星的时间);
    }

    public 更新老物件遮罩填充量(curNum = RuntimeData._ins.passLevel, animFlag = true) {
        let fillRange = 1 - curNum / this._dayCount;
        if (!animFlag) {
            this._oldObjectMask.fillRange = fillRange;
            this._percentLabel.string = this.numberToPercentSign(Math.round((1 - fillRange) * 100));
        } else {
            tween(this._oldObjectMask)
                .to(0.23, { fillRange }, { easing: easing.sineInOut })
                .start();

            let obj = {
                percent: Math.floor(this._curDayNum / this._dayCount * 100)
            }
            tween(obj)
                .to(0.23,
                    { percent: Math.floor((1 - fillRange) * 100) },
                    {
                        onUpdate: (target, ratio) => {
                            this._percentLabel.string = this.numberToPercentSign(Math.ceil(target.percent));
                        }
                    }
                )
                .start();
        }
    }

    private numberToPercentSign(num: number): string {
        return `${num}:`;
    }
}
