import { _decorator, Component, Node, Vec3, v3, view, UITransform, Label, color, tween, easing } from 'cc';
// import { colorIdEnum } from '../Index';
import { MainGame_CDX } from '../MainGame_CDX';
import { CartManager } from '../cart/CartManager';
import { Constants } from '../data/Constants';
const { ccclass, property } = _decorator;

@ccclass('QueueManager')
export class QueueManager extends Component {
    public static _ins: QueueManager;

    private _queue: IQueueGrid[] = [];
    public queueMovingFlag = false;
    /** 移动一个格子的时间 */
    public fillTime: number = 0.078;

    protected onLoad(): void {
        QueueManager._ins = this;
        this.initQueue();
    }

    protected start(): void {
    }

    /** 生成队列位置数组 */
    public initQueue(){
        let winWidth = view.getVisibleSize().width;
        let winHeight = view.getVisibleSize().height;

        // 队列间隔
        let itemSpace = 9;
        // 队列行间距
        let rowSpace = 100;

        let rowCount = 1;
        let colCount = 3;
        let left = 88;
        let itemWidth = (winWidth - left * 2 - (colCount - 1) * itemSpace) / colCount;

        // 生成长龙队形
        let posY = 0;
        let leftX = -winWidth / 2 + left + itemWidth / 2;
        let curQueueDir = -1;
        for(let row = 0; row < rowCount; row++){
            for(let col = 0; col < colCount; col++){
                let posX = (leftX + (itemWidth + itemSpace) * col) * curQueueDir;

                this._queue.push({
                    pos: v3(posX, posY),
                    item: null
                });
            }
            // if(row == rowCount - 1){
            //     break;
            // }

            posY += rowSpace; //  / 2;
            this._queue.push({
                pos: v3(this._queue[this._queue.length - 1].pos.x, posY),
                item: null
            });
            posY += rowSpace; //  / 2;

            curQueueDir *= -1;
        }

        // 生成龙头
        // let tempX = this._queue[0].pos.x;
        // if(rowCount > 1){
        //     for(let i = 0; i < colCount / 2; i++){
        //         this._queue.shift();
        //     }
        //     tempX = 0;
        // }
        // let headCount = 2;
        // let tempY = this._queue[0].pos.y;
        // for(let i = headCount; i > 0; i--){
        //     let x = tempX;
        //     let y = tempY - rowSpace * (headCount - i + 1);
        //     this._queue.unshift({
        //         pos: v3(x, y),
        //         item: null
        //     });
        // }

        // 展示位置
        // for(let i = 0; i < this._queue.length; i++){
        //     let singleSpr = Tools.newPrefab("SingleSprite", this.node, this._queue[i].pos);
        //     Tools.setSpriteFrame(singleSpr, "singleColor");
        //     singleSpr.getComponent(UITransform).setContentSize(50, 50);
        // }
    }

    public adaptPos(maxY?: number){
        // maxY = maxY || -view.getVisibleSize().height / 2 + 270 + 165;
        // 暂存区位置
        let waitingAreaY = Constants._ins.waitingAreaY;
        maxY = maxY || waitingAreaY - 100;

        let tempMaxY = -1000000;
        this._queue.forEach(val=>{
            if(val.pos.y > tempMaxY)
                tempMaxY = val.pos.y;
        });

        let offsetY = maxY - tempMaxY;
        this._queue.forEach(val=>{
            val.pos.y += offsetY;
        });
    }

    public isHaveEmptyPos(): boolean{
        let flag = false;
        flag = this._queue.some((item, index)=>item.item == null && index != this._queue.length - 1);

        return flag;
    }

    public addItem(item: Node): void {
        if(!this.isHaveEmptyPos()) return;

        let last = this._queue[this._queue.length - 1];
        item.setPosition(last.pos);

        // 获取指定的队列位置
        let targetGrid = this._queue.find(queueGrid=>!queueGrid.item);
        targetGrid.item = item;
        let targetIndex = this._queue.indexOf(targetGrid);

        // item 移动到指定位置
        let curIndex = this._queue.length - 1;
        let tw = tween(item);
        let moveTime = this.fillTime;

        while(curIndex > targetIndex){
            tw.to(moveTime, {position: this._queue[curIndex - 1].pos});
            curIndex--;
        }
        tw.start();

        item.setSiblingIndex(-targetIndex - 1);
    }

    /** 移除指定的餐车 */
    public removeItem(item: Node){
        let index = this._queue.findIndex(val=>val.item == item);
        if(index == -1) return;
        let queueGrid = this._queue[index];
        queueGrid.item = null;
        // item.destroy();
        // this.fillQueue(index + 1);
        tween(item)
            .by(0.12, {position: v3(0, -250)}, {easing: easing.sineInOut})
            .call(() => {
                // 玩法2 切换
                // 移除旧的后，生成新的
                let cart = CartManager.instance.createCartWithoutQueue();
                if (cart) {
                    queueGrid.item = cart;
                    cart.setPosition(item.position);
                    tween(cart)
                        .to(0.23, { position: queueGrid.pos }, { easing: easing.backOut })
                        .start();
                }
                // this.fillQueue(index + 1);

                this.scheduleOnce(() => {
                    this.queueMovingFlag = false;
                    MainGame_CDX._ins.touchFlag;
                }, this.fillTime);
                item.destroy();
            })
            .start();
    }

    /** 补充队列 */
    public fillQueue(index: number = 1){
        this.queueMovingFlag = true;
        // 所有item向前移动一个格子
        for(let i = index; i < this._queue.length; i++){
            let item = this._queue[i].item;
            if(!item) continue;
            this._queue[i - 1].item = item;
            this._queue[i].item = null;
            tween(item)
                .to(this.fillTime, {position: this._queue[i - 1].pos})
                .start();
            item.setSiblingIndex(-(i - 1) - 1);
        }

        // 生成新的item
        // this.addItem();
        CartManager.instance.createCartToQueue();

        this.scheduleOnce(()=>{
            this.queueMovingFlag = false;
            MainGame_CDX._ins.touchFlag;
        }, this.fillTime);
    }

    /** 获取所有位置上的所有物品 */
    getAllItems(){
        return this._queue.map(val=>val.item).filter(val=>!!val);
    }

    /** 获取队头 */
    getQueueHead(){
        return this._queue[0].item;
    }
}

export interface IQueueGrid{
    pos: Vec3;
    item: Node;
}

