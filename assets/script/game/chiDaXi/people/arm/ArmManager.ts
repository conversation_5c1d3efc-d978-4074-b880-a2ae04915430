import { _decorator, Component, isValid, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('ArmManager')
export class ArmManager extends Component {
    static instance: ArmManager;

    private _arms: Node[] = [];
    private _targetNodes: Node[] = [];

    protected onLoad(): void {
        ArmManager.instance = this;
    }

    /** 
     * @param arm 要注册的手臂节点
     * @param targetNode 目标对标的节点
     */
    registerArm(arm: Node, targetNode: Node): void {
        // 在这里注册手臂节点
        arm.setParent(this.node, true);
        this._arms.push(arm);
        this._targetNodes.push(targetNode);
    }

    update(deltaTime: number) {
        for (let i = this._arms.length - 1; i >= 0; i--) {
            // 更新手臂的位置和旋转等
            let arm = this._arms[i];
            let targetNode = this._targetNodes[i];
            if(!targetNode || !isValid(targetNode)){
                this._arms.splice(i, 1);
                this._targetNodes.splice(i, 1);
                arm.destroy();
                continue;
            }

            arm.setWorldPosition(targetNode.getWorldPosition());
        }
    }
}


