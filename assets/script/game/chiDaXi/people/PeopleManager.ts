import { _decorator, Component, instantiate, Node, Vec3 } from 'cc';
import { gameStateEnum, prefabNameEnums } from '../Index';
import { Tools } from '../../../common/Tools';
import { RuntimeData } from '../data/GameData';
import { VoicePackageManager } from '../../../common/VoicePackageManager';
const { ccclass, property } = _decorator;

@ccclass('PeopleManager')
export class PeopleManager extends Component {
    static _ins: PeopleManager;

    /** 是否挣在上菜 */
    public servingFlag: boolean = false;

    protected onLoad(): void {
        PeopleManager._ins = this;
    }

    start() {
        // 增加催促语音
        this.scheduleOnce(() => {
            this.schedule(() => {
                if (RuntimeData._ins.gameState == gameStateEnum.start) {
                    // console.log("播放催促语音");
                    let voiceCount = 1;
                    let playP = 0.5;
                    let voiceName = "cuiCu";
                    let volume = 2.5;
                    VoicePackageManager.instance.playVoicePackage(voiceName, voiceCount, playP, volume);
                }
            }, 10);
        }, 15);
    }

    // 指定位置生成顾客
    createCustomer(pos: Vec3): Node{
        let path = prefabNameEnums.顾客;
        let customer = Tools.newPrefab(path, this.node, pos);

        return customer;
    }

    update(deltaTime: number) {
        
    }
}


