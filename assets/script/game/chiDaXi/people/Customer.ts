import { _decorator, Component, easing, Node, sp, Tween, tween, UITransform, v3 } from 'cc';
import { PeopleBase } from './PeopleBase';
import { MovementComponent } from '../components/MovementComponent';
import { Tools } from '../../../common/Tools';
import { textureNamesEnum } from '../Index';
import { ShadowManager } from '../ShadowManager';
import { ArmManager } from './arm/ArmManager';
const { ccclass, property } = _decorator;

enum CustomerState{
    standby = 0,
    eat
}

enum SprNodeAnimTag{
    standby = 1,
    eat = 2
}

@ccclass('Customer')
export class Customer extends PeopleBase {
    // spineCom: sp.Skeleton;
    private _sprNode: Node;

    private _shadow: Node;
    private _shadowSpineCom: sp.Skeleton;

    private _state: CustomerState = null;
    private _curArm: Node;

    protected onLoad(): void {
        // this.spineCom = this.node.getComponentInChildren(sp.Skeleton);
        this._sprNode = this.node.getChildByName("SprNode");

        this.initData();
    }

    initData(){
        let randomId = 100 + Math.ceil(Math.random() * 7);
        let imgName = textureNamesEnum.顾客 + randomId;
        let sprNode = this.node.getChildByName("SprNode");
        Tools.setSpriteFrame(sprNode, imgName);
        // this.spineCom.node.active = true;
        // this.spineCom.setSkin(randomId.toString());
        // this.spineCom.node.active = false;
        this.node.setScale(0.5, 0.5, 0.5);
        // 随机时间开始播放动作
        this.scheduleOnce(()=>{
            if(!!this._state) return;

            this.standbyAnim();
        }, Math.random() * 3);

        // let boneNode = this.spineCom.node;
        // 露出身子的长度
        // let showLength = 140;
        // let height = boneNode.getComponent(UITransform).height;
        // // if(height > showLength){
        //     boneNode.setPosition(0, -(height - showLength) / 2);
        // // }

        // // 注册影子
        // this.scheduleOnce(()=>{
        //     this._shadow = ShadowManager._ins.registItem(this.spineCom.node);
        //     this._shadowSpineCom = this._shadow?.getComponent(sp.Skeleton);
        // }, 0);
        // 注册影子
        this.scheduleOnce(() => {
            this._shadow = ShadowManager._ins.registItem(this._sprNode);
        }, 0);
    }

    standbyAnim(){
        this._state = CustomerState.standby;

        // if(this.spineCom && this.spineCom.isValid){
        //     this.spineCom.setAnimation(0, "standby", true);
        //     this.spineCom.timeScale = 0.5;
        // }

        // if(this._shadowSpineCom && this._shadowSpineCom.isValid){
        //     this._shadowSpineCom.setAnimation(0, "standby", true);
        //     this._shadowSpineCom.timeScale = 0.5;
        // }

        // 给静止人物精灵节点增加一个呼吸动画
        tween(this._sprNode)
            .tag(SprNodeAnimTag.standby)
            .repeatForever(
                tween()
                    .by(1, { scale: v3(-0.05, 0.1, 0) }, { easing: easing.sineOut })
                    .to(2, { scale: v3(1, 1, 1) }, { easing: easing.sineIn })
            )
            .start();
    }
    eatAnim(){
        if(this._state == CustomerState.eat) return;
        // this.node.getChildByName("SprNode").active = false;
        // this.spineCom.node.active = true;

        this._state = CustomerState.eat;

        // if(this.spineCom && this.spineCom.isValid){
        //     this.spineCom.setAnimation(0, "eat", true);
        //     this.spineCom.timeScale = 1;
        // }

        // if(this._shadowSpineCom && this._shadowSpineCom.isValid){
        //     this._shadowSpineCom.setAnimation(0, "eat", true);
        //     this._shadowSpineCom.timeScale = 1;
        // }

        let armParentNode = this.node.getChildByName("Arm");
        let arm: Node = armParentNode.getChildByName("ArmForEat");
        if(!arm) return;
        this._curArm = arm;
        arm.active = true;
        ArmManager.instance.registerArm(arm, armParentNode);

        // 停止呼吸动作，变为快速吃饭动作
        Tween.stopAllByTag(SprNodeAnimTag.standby, this._sprNode);
        tween(this._sprNode)
            .tag(SprNodeAnimTag.eat)
            .repeatForever(
                tween()
                    .by(0.2, { scale: v3(-0.05, 0.1, 0) })
                    .to(0.2, { scale: v3(1, 1, 1) })
            )
            .start();
    }

    update(deltaTime: number) {
        super.update(deltaTime);
    }
}