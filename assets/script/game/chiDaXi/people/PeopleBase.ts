import { _decorator, Component, Node, UITransform } from 'cc';
import { IComponent } from '../components/IComponent';
const { ccclass, property } = _decorator;

@ccclass('PeopleBase')
export class PeopleBase extends Component {
    protected _components: IComponent[] = [];

    protected _transform: UITransform;

    protected start(): void {
        this._transform = this.getComponent(UITransform);
    }

    public addIComponent(component: IComponent): void {
        this._components.push(component);
    }

    public removeIComponent(component: IComponent): void {
        const index = this._components.indexOf(component);
        if (index !== -1) {
            this._components.splice(index, 1);
        }
    }

    public update(deltaTime: number): void {
        for (const component of this._components) {
            component.update(deltaTime);
        }
    }
}


