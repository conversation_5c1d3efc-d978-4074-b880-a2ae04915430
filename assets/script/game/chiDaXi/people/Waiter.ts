import { _decorator, Component, Node, UITransform } from 'cc';
import { colorIdEnum, prefabNameEnums, textureNamesEnum } from '../Index';
import { Tools } from '../../../common/Tools';
import { DishManager } from '../dish/DishManager';
import { RuntimeData } from '../data/GameData';
const { ccclass, property } = _decorator;

@ccclass('Waiter')
export class Waiter extends Component {
    private _colorId: colorIdEnum;
    private _waiterId: number;

    private _sprNode: Node;

    protected onLoad(): void {
        this._sprNode = this.node.getChildByName("SprNode");
    }

    start() {
        let colorId = RuntimeData._ins.getRandomColorId();
        this.initData(colorId);
    }

    initData(colorId: colorIdEnum){
        this._colorId = colorId;
        this._waiterId = 100 + colorId;

        this._waiterId = 101;
        let imgName = textureNamesEnum.服务员 + this._waiterId;
        Tools.setSpriteFrame(this._sprNode, imgName);

        // 生成手中的盘子
        this.createDish();
    }

    createDish(){
        let dish = DishManager.instance.createDish(this._colorId);
        dish.setParent(this.node);
        let sprNodeTransform = this._sprNode.getComponent(UITransform);
        dish.setPosition(-sprNodeTransform.width / 3, 10);
        dish.setScale(0.8, 0.8);
    }

    get colorId(){
        return this._colorId;
    }
}


