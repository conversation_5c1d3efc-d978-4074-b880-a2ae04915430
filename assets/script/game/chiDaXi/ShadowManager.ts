import { _decorator, color, Component, instantiate, isValid, Node, sp, Sprite, UIOpacity, v3 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('ShadowManager')
export class ShadowManager extends Component {
    static _ins: ShadowManager = null;

    offsetX: number = 10;
    offsetY:number = -14;

    private _shadows: Node[] = [];
    private _items: Node[] = [];

    protected onLoad(): void {
        ShadowManager._ins = this;
    }

    // 向影子管理器注册物体
    public registItem(item: Node): Node{
        this._items.push(item);
        let shadow = instantiate(item);
        shadow.parent = this.node;

        let worldPos = v3();
        item.getWorldPosition(worldPos);
        shadow.setWorldPosition(worldPos.x + this.offsetX, worldPos.y + this.offsetY, 0);
        this._shadows.push(shadow);
        // shadow.addComponent(UIOpacity).opacity = 123;
        // 判断是否包含精灵组件
        shadow.getComponentsInChildren(Sprite).forEach(sprCom=>{
            sprCom.color = color(0, 0, 0, 123);
        });
        shadow.getComponent(Sprite) && (shadow.getComponent(Sprite).color = color(0, 0, 0, 123));

        // 判断是否包含骨骼组件
        let spineCom = shadow.getComponent(sp.Skeleton);
        if (spineCom) {
            spineCom.color = color(0, 0, 0, 123);
            // 把骨骼动画节点放最前面，防止打断合批
            shadow.setSiblingIndex(0);
        }

        return shadow;
    }

    protected lateUpdate(dt: number): void {
        for(let i = 0; i < this._items.length; i++){
            let shadow = this._shadows[i];
            let item = this._items[i];

            if(!isValid(item)){
                this._items.splice(i, 1);
                this._shadows.splice(i, 1);
                shadow.destroy();
                i--;
                continue;
            }

            let worldPos = v3();
            item.getWorldPosition(worldPos);
            shadow.setWorldPosition(worldPos.x + this.offsetX, worldPos.y + this.offsetY, 0);
            shadow.setWorldScale(item.worldScale);
            shadow.setWorldRotation(item.worldRotation);
        }
    }
}