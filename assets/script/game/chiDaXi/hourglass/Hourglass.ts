import { _decorator, Component, Node, Sprite, tween, UITransform, v3 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('Hourglass')
export class Hourglass extends Component {
    @property(Sprite)
    private bodyUp: Sprite = null;
    @property(Sprite)
    private bodyDown: Sprite = null;
    @property(Node)
    private sandflow: Node = null;

    private _cur: number = 0;
    private _total: number = 0;
    
    initData(cur: number, total: number){
        this._cur = cur;
        this._total = total;

        this.updateSandflow();
    }

    setCur(cur: number){
        this._cur = cur;
        this.updateSandflow();
    }

    setSurplus(surplus: number){
        this._cur = this._total - surplus;
        this.updateSandflow();
    }

    updateSandflow(){
        let bodyDownHeight = this.bodyDown.getComponent(UITransform).contentSize.height;
        let fillRange = this._cur / this._total;

        tween(this.bodyUp)
            .to(0.12, {fillRange: 1 - fillRange})
            .start();
        tween(this.bodyDown)
            .to(0.12, { fillRange })
            .start();

        let y = -bodyDownHeight / 2 + 4.5 + bodyDownHeight * fillRange;
        tween(this.sandflow)
            .to(0.12, {position: v3(0, y)})
            .start();
        this.sandflow!.getComponent(Sprite).fillRange = 1 - fillRange;
    }
}


