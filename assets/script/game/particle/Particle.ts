import { Vec3, Node, Layers, tween, v3, Sprite, UIOpacity, Vec2, v2, easing, find, Tween, isValid, view } from "cc";
import { PoolManager } from "../../manager/PoolManager";
import { colorIdEnum, prefabNameEnums } from "../chiDaXi/Index";
import { Tools } from "../../common/Tools";
import JumpBy from "../../common/JumpBy";

export class Particle {
    public static placeDishToTableEff(parentNode: Node, pos: Vec3) {
        let fragmentCount = Math.random() * 20 + 15;
        let colors = Tools.getDiffNumRandom(1, 6, 2);
        for (let i = 0; i < fragmentCount; i++) {
            let fragment = PoolManager.Spawn(prefabNameEnums.singleSpr, parentNode, pos, 0);
            Tween.stopAllByTarget(fragment);
            let colorId = colors[Math.floor(Math.random() * colors.length)];
            Tools.setSpriteFrame(fragment, "dishPlaceToTableParticle_" + (100 * colorId + 1));
            fragment.layer = Layers.Enum.DEFAULT;
            let scale = Math.random() > 0.3 ? (Math.random() * 0.3 + 0.2) : (Math.random() * 0.3 + 0.7);
            fragment.setScale(scale, scale, 1);
            let jumpTime = Math.random() * 0.2 + 0.15;

            let opacityCom = fragment.getComponent(UIOpacity);
            opacityCom.opacity = 255;
            Tween.stopAllByTarget(opacityCom);
            tween(opacityCom)
                .to(jumpTime, { opacity: 50 })
                .call(() => {
                    opacityCom.opacity = 255;
                })
                .start();

            let targetX = 130 * (0.5 - Math.random());
            let targetY = 80 * (0.5 - Math.random());

            let jumpBy = fragment.addComponent(JumpBy);
            jumpBy.runAction(jumpTime, v3(targetX, targetY), targetY + 50, Math.random() * 1)
                .callBack(() => {
                    fragment.angle = 0;
                    Tween.stopAllByTarget(fragment);
                    Tween.stopAllByTarget(opacityCom);
                    PoolManager.Despawn(fragment);
                    jumpBy.destroy();
                });
        }
    }

    public static bombRibbon(parentNode: Node, pos: Vec3, fragmentCount?: number) {
        // parentNode = find("Canvas/UILayer");

        if (fragmentCount == undefined) fragmentCount = Math.random() * 20 + 15;
        for (let i = 0; i < fragmentCount; i++) {
            let fragment = PoolManager.Spawn(prefabNameEnums.singleSpr, parentNode, pos, 0);
            Tween.stopAllByTarget(fragment);
            let randomFragmentId = 100 + Math.ceil(Math.random() * 17);
            Tools.setSpriteFrame(fragment, "cleanTableRibbonEff_" + randomFragmentId);
            fragment.layer = Layers.Enum.UI_2D;
            fragment.getComponent(UIOpacity).opacity = 255;

            // 弹出的时间
            let jumpTime = Math.random() * 0.34 + 0.56;

            // 弹出的过程中，缩放
            fragment.setScale(0, 0);
            let targetScale = Math.random() < 0.3 ? (Math.random() * 0.3 + 0.7) : (Math.random() * 0.2 + 0.3);
            if (randomFragmentId == 101 || randomFragmentId == 102) {
                targetScale = 0.8;
            }
            tween(fragment)
                .to(jumpTime * 2 / 3, { scale: v3(targetScale, targetScale) })
                .start();

            // 获取弹出后的目标位置
            // let targetPos = v2();
            // let length = Math.random() * 150 + 50;
            // Vec2.random(targetPos, length);
            let targetX = (Math.random() * 350) * (Math.random() > 0.5 ? -1 : 1);
            let targetY = Math.random() * 350 - 20;
            let jumpBy = fragment.addComponent(JumpBy);
            jumpBy.runAction(jumpTime, v3(targetX, targetY), targetY + 50, Math.random() * -2 + 1)
                .callBack(() => {
                    // 落叶飘动作
                    let obj = {
                        y: fragment.position.y,
                        x: fragment.position.x
                    }

                    let moveOutTime = 5;
                    tween(obj)
                        .by(moveOutTime, { y: -1500 }, {
                            onUpdate: (target, ratio) => {
                                if (!isValid(fragment) || !isValid(parentNode) || !parentNode.active) {
                                    Tween.stopAllByTarget(obj);
                                    Tween.stopAllByTarget(fragment);
                                    return;
                                }
                                fragment.setPosition(v3(target.x, target.y));
                            },
                            easing: easing.sineOut
                        })
                        .start();
                    let tw2 = tween(obj);
                    let repeatCount = 10;
                    for (let i = 0; i < repeatCount; i++) {
                        let randomDir = Math.random() > 0.5 ? 1 : -1;
                        let randomOffsetX = Math.random() * 15 + 10;
                        tw2.by(moveOutTime / repeatCount, { x: randomDir * randomOffsetX }, { easing: easing.sineInOut })
                    }
                    tw2.start();

                    // 淡出
                    let opacityCom = fragment.getComponent(UIOpacity);
                    // Tween.stopAllByTarget(opacityCom);
                    let fadeOutTime = 1;
                    tween(opacityCom)
                        .delay(fadeOutTime / 3)
                        .to(fadeOutTime * 1 / 6 + Math.random() * fadeOutTime * 3 / 6, { opacity: 0 })
                        .start();

                    // 旋转
                    let rotateDir = Math.random() > 0.5 ? 1 : -1;
                    let rotateAngle = Math.random() * 720 + 360;
                    tween(fragment)
                        .by(moveOutTime + fadeOutTime, { angle: rotateDir * rotateAngle * 5 })
                        .start();

                    tween(fragment)
                        .delay(fadeOutTime)
                        .call(() => {
                            Tween.stopAllByTarget(obj);
                            Tween.stopAllByTarget(fragment);
                            Tween.stopAllByTarget(opacityCom);

                            fragment.angle = 0;
                            fragment.setScale(1, 1);
                            fragment.getComponent(UIOpacity).opacity = 255;
                            PoolManager.Despawn(fragment);
                            jumpBy.destroy();
                            // fragment.destroy();
                        })
                        .start();
                });
        }
    }

    public static successRibbon(parentNode: Node, pos: Vec3, fragmentCount?: number) {
        for (let i = 0; i < fragmentCount; i++) {
            let fragment = PoolManager.Spawn(prefabNameEnums.singleSpr, parentNode, pos, 0);;

            Tween.stopAllByTarget(fragment);
            let randomFragmentId = 100 + Math.ceil(Math.random() * 17);
            Tools.setSpriteFrame(fragment, "cleanTableRibbonEff_" + randomFragmentId);
            fragment.setPosition(0, 50 + view.getVisibleSize().height * 0.3);
            let scale = Tools.random(0.5, 1, false);
            fragment.setScale(scale, scale);
            let opacityCom = fragment.getComponent(UIOpacity);
            opacityCom.opacity = 255;

            fragment.layer = Layers.Enum.UI_2D;

            let angle = Math.random() * 360 * Math.PI / 180;
            let length = Math.random() * view.getVisibleSize().width * 1.5 / 2;
            let targetP = v3(fragment.position.x + Math.sin(angle) * length, fragment.position.y + Math.cos(angle) * length + 150);
            let time = 0.17 + 0.1;
            let offsetX = 100;

            tween(fragment)
                .to(time * 1.5, { position: targetP }, { easing: easing.sineOut })
                .start();
            tween(fragment)
                .delay(time * (Math.random() * 0.2 + 1.3))
                .call(() => {
                    // 落叶飘动作
                    let obj = {
                        y: fragment.position.y,
                        x: fragment.position.x
                    }

                    let moveOutTime = time * 12.5;
                    tween(obj)
                        .by(moveOutTime, { y: -1500 }, {
                            onUpdate: (target, ratio) => {
                                if (!isValid(fragment) || !isValid(parentNode) || !parentNode.active) {
                                    Tween.stopAllByTarget(obj);
                                    Tween.stopAllByTarget(fragment);
                                    return;
                                }
                                fragment.setPosition(v3(target.x, target.y));
                            },
                            easing: easing.sineOut
                        })
                        .start();
                    let tw2 = tween(obj);
                    let repeatCount = 10;
                    for (let i = 0; i < repeatCount; i++) {
                        let randomDir = Math.random() > 0.5 ? 1 : -1;
                        let randomOffsetX = Math.random() * 15 + 10;
                        tw2.by(moveOutTime / repeatCount, { x: randomDir * randomOffsetX }, { easing: easing.sineInOut })
                    }
                    tw2.start();

                    // 淡出
                    let opacityCom = fragment.getComponent(UIOpacity);
                    // Tween.stopAllByTarget(opacityCom);
                    let fadeOutTime = 1;
                    tween(opacityCom)
                        .delay(time * 4)
                        .to(time * 6, { opacity: 0 })
                        .start();

                    // 旋转
                    let rotateDir = Math.random() > 0.5 ? 1 : -1;
                    let rotateAngle = (Math.random() * 1800 + 1200);
                    tween(fragment)
                        .by(time * 25, { angle: rotateDir * rotateAngle })
                        .start();

                    tween(fragment)
                        .delay(time * (Math.random() * 18 + 7))
                        .call(() => {
                            Tween.stopAllByTarget(obj);
                            Tween.stopAllByTarget(fragment);
                            Tween.stopAllByTarget(opacityCom);

                            fragment.angle = 0;
                            fragment.setScale(1, 1);
                            fragment.getComponent(UIOpacity).opacity = 255;
                            // PoolManager.Despawn(fragment);
                            fragment.destroy();
                        })
                        .start();
                })
                .start();
        }
    }

    public static showRedPacketRain(parentNode: Node, surplusCount: number, createCount = 1) {
        if (!isValid(parentNode) || !parentNode.active || surplusCount == 0) return;
        for (let i = 0; i < createCount; i++) {
            let winSize = view.getVisibleSize();
            let winHeight = winSize.height;
            let winWidth = winSize.width;

            let randomX = winWidth * (Math.random() - 0.5);
            let y = winHeight / 2 + 200;
            // 红包 资源id
            let randomFragmentId = 100 + Math.ceil(Math.random() * 2);
            let fragment = Tools.newSprite("LcleanTableRibbonEff_" + randomFragmentId, parentNode);
            let opacityCom = fragment.addComponent(UIOpacity);
            fragment.setPosition(randomX, y);
            let scale = Math.random() > 0.3 ? (Math.random() * 0.3 + 0.7) : (Math.random() * 0.3 + 0.2);
            fragment.setScale(scale, scale);
            opacityCom.opacity = 255;
            fragment.layer = Layers.Enum.UI_2D;
            let angle = Math.random() * 360 * Math.PI / 180;
            let length = Math.random() * view.getVisibleSize().width * 1.5 / 2;
            let time = 0.17 + 0.1;
            fragment.angle = angle;
            // 落叶飘动作
            let obj = {
                y: fragment.position.y,
                x: fragment.position.x
            }
            let moveOutTime = time * 20;
            tween(obj)
                .by(moveOutTime, { y: -2500 }, {
                    onUpdate: (target, ratio) => {
                        if (!isValid(fragment) || !isValid(parentNode) || !parentNode.active || surplusCount == 0) {
                            Tween.stopAllByTarget(obj);
                            Tween.stopAllByTarget(fragment);
                            return;
                        }
                        fragment.setPosition(v3(obj.x, obj.y));
                    },
                    easing: easing.sineOut
                })
                .start();
            let tw2 = tween(obj);
            let repeatCount = 10;
            for (let i = 0; i < repeatCount; i++) {
                let randomDir = Math.random() > 0.5 ? 1 : -1;
                let randomOffsetX = Math.random() * 15 + 10;
                tw2.by(moveOutTime / repeatCount, { x: randomDir * randomOffsetX }, { easing: easing.sineInOut })
            }
            tw2.start();

            // 淡出
            // Tween.stopAllByTarget(opacityCom);
            let fadeOutTime = 1;
            tween(opacityCom)
                .delay(time * 4)
                .to(time * 6, { opacity: 0 })
                .start();

            // 旋转
            let rotateDir = Math.random() > 0.5 ? 1 : -1;
            let rotateAngle = (Math.random() * 1800 + 1200);
            tween(fragment)
                .by(time * 25, { angle: rotateDir * rotateAngle })
                .start();

            tween(fragment)
                .delay(time * (Math.random() * 18 + 7))
                .call(() => {
                    Tween.stopAllByTarget(obj);
                    Tween.stopAllByTarget(fragment);
                    Tween.stopAllByTarget(opacityCom);

                    fragment.angle = 0;
                    fragment.setScale(1, 1);
                    fragment.getComponent(UIOpacity).opacity = 255;
                    fragment.destroy();
                    // PoolManager.Despawn(fragment);
                })
                .start();
        }

        tween(parentNode)
            .delay(Math.random() * 0.34)
            .call(() => {
                this.showRedPacketRain(parentNode, surplusCount - 1, Math.random() > 0.7 ? 1 : 2);
            })
            .start();
    }
}