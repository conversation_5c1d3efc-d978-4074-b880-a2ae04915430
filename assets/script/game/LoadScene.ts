import { _decorator, Asset, Asset<PERSON>anager, asset<PERSON>anager, Component, director, Enum, error, Label, Node, ProgressBar, tween, Tween } from 'cc';
import { GameCtrl } from '../ctrl/GameCtrl';
import { GameModel } from '../model/GameModel';
import { ResManager } from '../manager/ResManager';
import { Util } from '../common/Util';
import { releaseType } from '../Enum/Enum';
import { AudioManager } from '../manager/AudioManager';
import { UIManager } from '../manager/UIManager';
import { AdaptCanvas } from '../common/AdaptCanvas';
import { UINamesEnum } from '../uiPanel/UIConfig';
import { bundleNamesEnum, sceneNamesEnum } from './chiDaXi/Index';
import { RuntimeData } from './chiDaXi/data/GameData';
import { AdManager } from '../ads/AdManager';
import { Particle } from './particle/Particle';
import { AutoGame } from '../common/AutoGame';
import { RemoteServerData } from './chiDaXi/data/RemoteServerData';
const { ccclass, property } = _decorator;

/** 游戏入口场景 */
@ccclass('LoadScene')
export class LoadScene extends Component {
    @property(ProgressBar)
    private progressBar: ProgressBar = null;
    @property(Label)
    private percentNum: Label = null;
    @property({
        type: Enum(releaseType),
        displayName: "发布类型"
    })
    public releaseType: releaseType = releaseType.test_TEST;

    @property
    private autoGameVer: boolean = false;

    // 初始场景
    private _initScene: string;
    // 初始bundle
    private _initBundle: string;

    private _loadBundleCount: number = 1;
    private _completeCount: number = 0;

    protected async onLoad() {
        // 是否是自动游戏版本

        AutoGame.instance.autoGameVer = this.autoGameVer;
        // 适配屏幕
        AdaptCanvas.adapterScreen();

        GameModel.instance.initGameModel(this.releaseType);
        AudioManager.init();
        UIManager.instance.init();
        AdManager.loadAds();

        // 运行时数据初始化
        RuntimeData._ins.init();
        // 加载服务器数据并赋值到本地
        RuntimeData._ins.downloadServerData();

        this._initBundle = bundleNamesEnum.主页场景;
        this._initScene = sceneNamesEnum.主页场景;

        let initScene = this._initScene;
        let initBundle = this._initBundle;

        // 预加载初始（主页）场景，并加载需要的资源包
        director.preloadScene(initScene);
        ResManager.instance.loadBundle(initBundle).then(bundle => {
            tween(this.progressBar)
                .to(10, { progress: 0.3 })
                .start();
            ResManager.instance.bundelLoadDir(initBundle, "", this.onProgress.bind(this), this.onComplete.bind(this))
        }).catch(err => {
            console.error(`[LoadScene] ${initBundle} 加载失败:`, err);
        });
    }

    protected start(): void {
        Particle.showRedPacketRain(this.node.getChildByName("Particle"), 1000);
    }

    private onProgress(finished: number, total: number): void {
        Tween.stopAllByTarget(this.progressBar);

        if (finished / total < 0.3) return;
        this.progressBar.progress = finished / total;
    }

    private onComplete(err: Error, assets: Asset[]): void {
        if (err) {
            error('[LoadScene] 加载bundle资源失败:', err);
            return;
        }

        if (assets?.length > 0) {
            console.log('资源加载完成');
            assets.forEach(asset => {

                if (asset?.name) {
                    ResManager.instance.setAsset(asset.name, asset);
                }
            });
        }

        AdManager.onShow(() => {
            if (GameModel.instance.curBgmName == null || GameModel.instance.curBgmName == "")
                return;
            console.log("播放背景音乐：", GameModel.instance.curBgmName);
            AudioManager.playMusic(GameModel.instance.curBgmName, 0.5);
        });
        AdManager.onHide(() => {
            AudioManager.pauseMusic();
        })

        // 加载完毕，手动加载进度条后进入主页场景
        // AudioManager.playMusic("gameBGM", 0.5);
        tween(this.progressBar)
            .to(0.5, { progress: 1 })
            .call(() => {
                this.enterScene();
            })
            .start();
    }

    private enterScene() {
        this._completeCount += 1;
        if (this._completeCount >= this._loadBundleCount) {
            UIManager.instance.showUI(UINamesEnum.globalUI);
            director.loadScene(this._initScene);
        }
    }

    protected update(dt: number): void {
        this.percentNum.string = Math.ceil(this.progressBar.progress * 100).toString();
    }
}


