import { find, tween } from "cc";
import { UIManager } from "../../../manager/UIManager";
import { UINamesEnum } from "../../../uiPanel/UIConfig";
import { LoadingUI } from "./LoadingUI";

export class CommonLoadingUIManager{
    private static _startTime: number;

    public static addLoadingUI(){
        UIManager.instance.showUI(UINamesEnum.全局加载页);
        this._startTime = new Date().getTime();
    }

    public static updateProgress(cur: number, total: number){
        let loadingUI = UIManager.instance.getUI(UINamesEnum.全局加载页);
        if(loadingUI){
            loadingUI.getComponent(LoadingUI).updateProgress(cur, total);
        }
    }

    public static async closeLoadingUI(){
        let index = 0;
        while(index <= 10000){
            if(UIManager.instance.getUI(UINamesEnum.全局加载页)){
                // if (new Date().getTime() - this._startTime < 1500) {
                //     tween(find("Canvas"))
                //         .call(() => {
                //             let loadingUI = UIManager.instance.getUI(UINamesEnum.全局加载页);
                //             loadingUI.getComponent(LoadingUI).setComplete(0.56);
                //         })
                //         .delay(1)
                //         .call(() => {
                //             UIManager.instance.hideUI(UINamesEnum.全局加载页);
                //         })
                //         .start();
                //     }else{
                        UIManager.instance.hideUI(UINamesEnum.全局加载页);
                    // }
                    break;
            }
            index ++;
            if(index == 10000){
                console.log("关闭加载页面失败");
            }
            await this.sleep(10);
        }
    }

    public static sleep(t: number){
        return new Promise(resolve => setTimeout(resolve, t));
    }
}