import { _decorator, Component, easing, Label, Node, Sprite, Tween, tween } from 'cc';
import { PageActive } from '../../../manager/UIManager';
import { Particle } from '../../particle/Particle';
const { ccclass, property } = _decorator;

@ccclass('LoadingUI')
export class LoadingUI extends Component implements PageActive {
    @property(Sprite)
    bar: Sprite = null;
    @property(Label)
    cur: Label = null;
    @property(Label)
    count: Label = null;
    @property(Label)
    percentLabelCom: Label = null;

    private _startFakeBarAnim: Tween = null;

    protected onEnable(): void {
        this._startFakeBarAnim = tween(this.bar)
            .to(10, { fillRange: 0.3 })
            .start();
        let index = 1;
        this.schedule(() => {
            this.cur.string = index++ + "";
            this.percentLabelCom.string = this.numberToPercentSign(Math.ceil(this.bar.fillRange * 100));
        }, 0.1, 20);

        Particle.showRedPacketRain(this.node, 1000);
    }
    protected onDisable(): void {
        this.unscheduleAllCallbacks();
    }

    show(...args: any[]): void {
    }
    hide(...args: any[]): void {
        this.bar.fillRange = 0;
        this.cur.string = '0';
        this.count.string = '0';
    }
    enableBtn(): void {
    }
    disableBtn(): void {
    }

    updateProgress(curNum: number, count: number) {
        if(this._startFakeBarAnim) this._startFakeBarAnim.stop();

        this.cur.string = curNum + '';
        this.count.string = count + '';

        let fillRange = curNum / count;
        if(fillRange > 0.3)
            this.bar.fillRange = fillRange;
        this.percentLabelCom.string = this.numberToPercentSign(Math.ceil(fillRange * 100));
    }

    setComplete(fillTime: number){
        this.cur.string = this.count.string;
        tween(this.bar)
            .to(fillTime, {fillRange: 1}, {easing: easing.sineInOut})
            .start();
    }

    private numberToPercentSign(num: number): string {
        return `${num}`;
    }
}


