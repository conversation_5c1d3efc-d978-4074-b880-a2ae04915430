import { _decorator, Component, Node } from 'cc';
import { PageActive, UIManager } from '../../../manager/UIManager';
import { UINamesEnum } from '../../../uiPanel/UIConfig';
const { ccclass, property } = _decorator;

@ccclass('GlobalUI')
export class GlobalUI extends Component implements PageActive {

    openSettingUI(){
        UIManager.instance.showUI(UINamesEnum.settingUI);
    }

    show(): void {
    }
    hide(): void {
    }
    enableBtn(): void {
    }
    disableBtn(): void {
    }
}


