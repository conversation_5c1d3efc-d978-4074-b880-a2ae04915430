import { _decorator, Button, Component, easing, Node, tween, v3 } from 'cc';
import { AdManager } from 'db://assets/script/ads/AdManager';
import { AdManager_ZJ } from 'db://assets/script/ads/AdManager_ZJ';
import { DailyRefresh, DailyRefreshItems } from 'db://assets/script/common/DailyRefresh';
import { Tools } from 'db://assets/script/common/Tools';
import { PageActive, UIManager } from 'db://assets/script/manager/UIManager';
import { UINamesEnum } from 'db://assets/script/uiPanel/UIConfig';
const { ccclass, property } = _decorator;

@ccclass('GuideSidebarUI')
export class GuideSidebarUI extends Component implements PageActive {
    clickCloseBtn(){
        this.closeAnim(() => {
            UIManager.instance.hideUI(UINamesEnum.guideSidebar);
        });
    }

    clickGetBtn(){
        // console.log("点击领取按钮");
        AdManager_ZJ._ins.enterSidebar();
        this.closeAnim(() => {
            UIManager.instance.hideUI(UINamesEnum.guideSidebar);
        });
    }

    /** 关闭动作 */
    public closeAnim(callBack?: Function) {
        let rootNode = this.node.getChildByName("Root");
        tween(rootNode)
            .to(0.23, { scale: v3(0.1, 0.1, 0.1) }, { easing: easing.backIn })
            .call(() => {
                rootNode.setScale(1, 1);
                callBack && callBack();
            })
            .start();
    }

    show(): void {
        // 判断今天是否已经领取
        let isGet = DailyRefresh.recordBehaviorNum(DailyRefreshItems.launchFromSidebar, 0, false);
        if(isGet > 0){
            let rootNode = this.node.getChildByName("Root");
            let getBtn = rootNode.getChildByName("GetBtn");
            Tools.setSpriteFrame(getBtn, "guideSizebarUI_gotBtn");

            getBtn.getComponent(Button).interactable = false;
        }

        let rootNode = this.node.getChildByName("Root");
        let scale = rootNode.getScale();
        rootNode.scale = v3(0, 0, 0);
        rootNode.setPosition(rootNode.position.x, rootNode.position.y + 500);
        tween(rootNode)
            .by(0.3, {scale, position: v3(0, -500, 0)}, {easing: easing.backOut})
            .start();
    }
    hide(): void {
    }
    enableBtn(): void {
    }
    disableBtn(): void {
    }
}


