import { _decorator, Component, Node } from 'cc';
import { AdManager } from 'db://assets/script/ads/AdManager';
import { AdManager_ZJ } from 'db://assets/script/ads/AdManager_ZJ';
import { DailyRefreshItems } from 'db://assets/script/common/DailyRefresh';
import { PageActive, UIManager } from 'db://assets/script/manager/UIManager';
import { UINamesEnum } from 'db://assets/script/uiPanel/UIConfig';
const { ccclass, property } = _decorator;

@ccclass('GuideAddTableUI')
export class GuideAddTableUI extends Component implements PageActive {
    clickCloseBtn(){
        UIManager.instance.hideUI(UINamesEnum.guideAddTable);
    }

    clickGetBtn(){
        // AdManager_ZJ._ins.enterSidebar();
        // UIManager.instance.hideUI(UINamesEnum.guideSidebar);
        AdManager_ZJ._ins.addTable(
            ()=>{
                console.log("领取奖励");
                let actName = DailyRefreshItems.addToTable;
                AdManager_ZJ._ins.getReward(actName);
            }
        );
    }

    show(): void {
    }
    hide(): void {
    }
    enableBtn(): void {
    }
    disableBtn(): void {
    }
}


