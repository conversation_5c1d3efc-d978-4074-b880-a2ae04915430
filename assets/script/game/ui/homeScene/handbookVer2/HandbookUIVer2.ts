import { _decorator, Component, easing, Node, tween, UITransform, v3 } from 'cc';
import { PageActive, UIManager } from 'db://assets/script/manager/UIManager';
import { UINamesEnum } from 'db://assets/script/uiPanel/UIConfig';
import { HandbookPage } from '../handbook/HandbookPage';
const { ccclass, property } = _decorator;

@ccclass('HandbookUIVer2')
export class HandbookUIVer2 extends Component implements PageActive {
    @property(Node)
    rootNode: Node = null;

    exitBtn(){
        this.closeAnim(() => {
            UIManager.instance.hideUI(UINamesEnum.handbookUIVer2);
        });
    }

    closeAnim(...args: any[]){
        let [callback] = args;
        let rootNode = this.node.getChildByName("Root");

        let showTime = 0.34;
        tween(rootNode)
            .to(showTime, {position: v3(-1000, 0)}, {easing: easing.sineInOut})
            .call(()=>{
                rootNode.setScale(1, 1);
                callback && callback();
            })
            .start();
    }

    show(): void {
        this.node.getComponentInChildren(HandbookPage).showContentByItemType(4);

        let rootNode = this.node.getChildByName("Root");

        rootNode.setPosition(-1500, 0);
        let showTime = 0.34;
        tween(rootNode)
            .to(showTime, {position: v3(0, 0)}, {easing: easing.sineOut})
            .start();
    }
    hide(): void {
    }
    enableBtn(): void {
    }
    disableBtn(): void {
    }

    update(deltaTime: number) {
        
    }
}


