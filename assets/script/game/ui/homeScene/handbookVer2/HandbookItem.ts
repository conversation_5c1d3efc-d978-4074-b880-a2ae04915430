import { _decorator, color, Component, instantiate, Node, Sprite, UITransform } from 'cc';
import { dishTypeEnum, IDishInfo } from '../../../chiDaXi/data/DishesData';
import { textureNamesEnum } from '../../../chiDaXi/Index';
import { Tools } from 'db://assets/script/common/Tools';
import { ResManager } from 'db://assets/script/manager/ResManager';
import { AutoGame } from 'db://assets/script/common/AutoGame';
const { ccclass, property } = _decorator;

@ccclass('HandbookItem')
export class HandbookItem extends Component {
    @property(Node)
    itemFrame: Node = null;
    @property(Node)
    itemSpr: Node = null;
    @property(Node)
    itemName: Node = null;

    private _itemInfo: IDishInfo;

    public initByItemInfo(itemInfo: IDishInfo) {
        this._itemInfo = itemInfo;

        let maxWidth = 900 * 0.191;
        let maxHeight = 900 * 0.191;

        let itemType = this._itemInfo.type;
        itemType = 1;
        let itemId = this._itemInfo.id;
        let sprImg = "oldObject_" + (100 * itemType + itemId);

        Tools.setSpriteFrame(this.itemSpr, sprImg);
        let sprWidth = this.itemSpr.getComponent(UITransform).width;
        let sprHeight = this.itemSpr.getComponent(UITransform).height;
        let scale = Math.min(maxWidth / sprWidth, maxHeight / sprHeight);
        this.itemSpr.setScale(scale, scale);

        let unlocked = this._itemInfo.unlocked;
        if(AutoGame.instance.autoGameVer) unlocked = true;
        unlocked ? this.unlocked() : this.locked();
    }

    public locked(){
        // console.log("锁定:", this._dishInfo.name);
        this.itemSpr.getComponent(Sprite).grayscale = true;
        // this.itemName.active = false;
        let mask = instantiate(this.itemSpr);
        mask.setParent(this.itemSpr);
        mask.setScale(1, 1, 1);
        mask.setPosition(0, 0);
        mask.getComponent(Sprite).color = color(0, 0, 0, 210);

        let itemNameImg = "oldObjectName_" + (100);
        Tools.setSpriteFrame(this.itemName, itemNameImg);

        // 物品框（方框）遮罩
        let itemFrame = this.itemFrame;
        let mask2 = instantiate(itemFrame);
        mask2.name = "ItemFrameMask";
        mask2.setParent(itemFrame);
        mask2.setPosition(0, 0);
        mask2.getComponent(Sprite).color = color(0, 0, 0, 156);
    }
    public unlocked(){
        // console.log("解锁:", this._dishInfo.name);
        this.itemSpr.getComponent(Sprite).grayscale = false;
        this.itemName.active = true;
        this.itemSpr.removeAllChildren();

        let itemNameImg = "oldObjectName_" + (100 * 1 + this._itemInfo.id);
        Tools.setSpriteFrame(this.itemName, itemNameImg);

        // 清除物品框遮罩
        // let itemFrame = this.itemFrame;
        // itemFrame.removeAllChildren();
    }
}


