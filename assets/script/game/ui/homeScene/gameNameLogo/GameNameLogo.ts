import { _decorator, Component, easing, Node, Tween, tween, UIOpacity, UITransform, v3 } from 'cc';
import { Tools } from 'db://assets/script/common/Tools';
import { prefabNameEnums } from '../../../chiDaXi/Index';
const { ccclass, property } = _decorator;

@ccclass('GameNameLogo')
export class GameNameLogo extends Component {
    bulingParentNode: Node;

    protected onEnable(): void {
        this.bulingParentNode = this.node.getChildByName("Buling");

        let createCount = 7;

        for (let i = 0; i < createCount; i++) {
            let buling = Tools.newPrefab(prefabNameEnums.singleSpr, this.bulingParentNode);
            Tools.setSpriteFrame(buling, "GameNameLogo_buling");
            buling.layer = 1 << 30;
            let opacityCom = buling.addComponent(UIOpacity);
            opacityCom.opacity = 0;
            buling.scale = v3(0.5, 0.5, 0.5);

            let randomDelayTime = Math.random() * 3;
            this.scheduleOnce(() => {
                this.randomBuling(buling);
            }, randomDelayTime);
        }
    }

    randomBuling(buling: Node) {
        let contentSize = this.bulingParentNode.getComponent(UITransform).contentSize;

        let randomX = contentSize.width * (0.5 - Math.random());
        let randomY = contentSize.height * (0.5 - Math.random());

        buling.setPosition(randomX, randomY, 0);

        let bulingTime = 0.56 * (Math.random() * 0.5 + 0.5);
        let showTime = 0.34 * (Math.random() * 0.5 + 0.5);
        let hideTime = 0.23 * (Math.random() * 0.5 + 0.5);
        let opacityCom = buling.getComponent(UIOpacity);
        tween(opacityCom)
            .to(bulingTime, {opacity: 255}, {easing: easing.sineOut})
            .delay(showTime)
            .to(hideTime, {opacity: 0}, {easing: easing.sineIn})
            .start();
        tween(buling)
            .to(bulingTime, {scale: v3(1, 1, 1)}, {easing: easing.sineOut})
            .delay(hideTime)
            .to(bulingTime, {scale: v3(0.3, 0.3, 0.3)}, {easing: easing.sineIn})
            .start();

        this.scheduleOnce(()=>{
            Tween.stopAllByTarget(buling);
            Tween.stopAllByTarget(opacityCom);
            this.randomBuling(buling);
        }, bulingTime + showTime + hideTime);
    }
}


