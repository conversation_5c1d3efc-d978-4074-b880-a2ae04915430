import { _decorator, Component, tween, easing, Sprite, Node, v3, Sprite<PERSON><PERSON>e, error, ImageAsset, Texture2D } from "cc";
import SettingUI from "../../SettingUI";
import { Tools } from "db://assets/script/common/Tools";
import { ResManager } from "db://assets/script/manager/ResManager";

const {ccclass, property} = _decorator;

@ccclass("SliderCtrlBase")
export class SliderCtrlBase extends Component {
    /** 滑块 */
    slider: Node;
    bg: Node;

    /** 开闭状态 */
    isOpening: boolean = true;
    /** 初始滑块位置 */
    initSliderX: number;
    
    protected onLoad(): void {
        this.bg = this.node.getChildByName("SlideBtn").getChildByName("Bg");
        this.slider = this.node.getChildByName("SlideBtn").getChildByName("Slider");
        this.bg.on(Node.EventType.TOUCH_START, this._onTouchStart, this);

        this.slider.setPosition(Math.abs(this.slider.position.x) * (this.isOpening ? 1 : -1), this.slider.position.y);
        this.initSliderX = this.slider.position.x;
        this.updateSliderUI();
    }

    protected _onTouchStart(){
        // 滑块移动
        this.initSliderX *= -1;
        tween(this.slider)
            .to(0.12, {position: v3(this.initSliderX, this.slider.position.y)}, {easing: easing.sineInOut})
            .start();
        this.isOpening = !this.isOpening;
        this.updateSliderUI();
    }

    /** 根据滑块的开闭状态，更新滑块的展示外观 */
    public updateSliderUI(): void {
        let mainNodeName = "SettingUI";
        let mainNode: Node = this.node;
        while(!mainNode || mainNode.name != mainNodeName){
            mainNode = mainNode.parent;
        }
        // 更新滑块背景
        let imgName = this.isOpening ? "bg_open" : "bg_close";
        let imgs = mainNode.getComponent(SettingUI).imgs;
        this.bg.getComponent(Sprite).spriteFrame = imgs.find(img=>img.name == imgName);
        // 更新滑块开关状态文字描述
        // let stateDescriptionImgName = this.isOpening ? "stateDescription_open" : "stateDescription_close";
        // this.slider.getChildByName("StateDescription").getComponent(Sprite).spriteFrame = imgs.find(img=>img.name == stateDescriptionImgName);
    }
}
