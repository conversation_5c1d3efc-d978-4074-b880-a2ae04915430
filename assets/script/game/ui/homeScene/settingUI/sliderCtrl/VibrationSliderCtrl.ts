import { AudioManager } from "db://assets/script/manager/AudioManager";
import {SliderCtrlBase} from "./base/SliderCtrlBase";
import { GameModel } from "db://assets/script/model/GameModel";
import { _decorator } from "cc";
import { AdManager } from "db://assets/script/ads/AdManager";
const {ccclass} = _decorator;
@ccclass
export class VibrationSliderCtrl extends SliderCtrlBase{
    protected onLoad(): void {
        this.isOpening = GameModel.instance.vibrateFlag == 1;
        super.onLoad();
    }

    protected _onTouchStart(): void {
        super._onTouchStart();

        // 根据 开关isOpening情况 震动的开关
        GameModel.instance.vibrateFlag = this.isOpening ? 1 : 0;
        // VibrationTools._ins.vibrateShort();
        AdManager.shortVibrate();
    }
}