import { _decorator } from "cc";
import {SliderCtrlBase} from "./base/SliderCtrlBase";
import { GameModel } from "db://assets/script/model/GameModel";
import { AudioManager } from "db://assets/script/manager/AudioManager";
const {ccclass} = _decorator;
@ccclass("MusicSliderCtrl")
export class MusicSliderCtrl extends SliderCtrlBase{
    protected onLoad(): void {
        this.isOpening = GameModel.instance.musicFlag == 1;
        super.onLoad();
    }

    protected _onTouchStart(): void {
        super._onTouchStart();

        // 根据 开关isOpening情况 音乐的开关
        GameModel.instance.musicFlag = this.isOpening ? 1 : 0;
        if(this.isOpening)
            // AudioManager.resumeMusic();
            AudioManager.playMusic(GameModel.instance.curBgmName, 0.5);
        else
            AudioManager.pauseMusic();
    }
}