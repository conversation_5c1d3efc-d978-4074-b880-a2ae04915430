import { GameModel } from "db://assets/script/model/GameModel";
import {SliderCtrlBase} from "./base/SliderCtrlBase";
import { _decorator } from "cc";
const {ccclass} = _decorator;
@ccclass
export class MusicSliderCtrl extends SliderCtrlBase{
    protected onLoad(): void {
        this.isOpening = GameModel.instance.soundFlag == 1;
        super.onLoad();
    }

    protected _onTouchStart(): void {
        super._onTouchStart();

        // 根据 开关isOpening情况 音效的开关
        GameModel.instance.soundFlag = this.isOpening ? 1 : 0;
    }
}