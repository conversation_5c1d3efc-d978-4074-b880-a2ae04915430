import { _decorator, Component, director, easing, SpriteFrame, sys, Tween, tween, v3 } from "cc";
import { sceneNamesEnum } from "../../../chiDaXi/Index";
import { PageActive } from "db://assets/script/manager/UIManager";
import { UIManager } from "../../../../manager/UIManager";
import { UINamesEnum } from "db://assets/script/uiPanel/UIConfig";
import { AdManager } from "db://assets/script/ads/AdManager";
import { GameModel } from "db://assets/script/model/GameModel";

const { ccclass, property } = _decorator;

@ccclass("SettingUI")
export default class SettingUI extends Component implements PageActive {
    public uiName: string;

    @property([SpriteFrame])
    public imgs: SpriteFrame[] = [];

    protected start(): void {
    }

    /** 返回主页 */
    backHomeScene() {
        this.closeAnim();

        director.loadScene(sceneNamesEnum.主页场景);
        // 停止录屏
        AdManager.stopGameVideo();
    }

    /** 点击关闭按钮 */
    clickCloseBtn(...data: any) {
        this.closeAnim();
    }

    /** 重新开始游戏 */
    againGameBtn() {
        this.closeAnim(() => {
            if (director.getScene().name === sceneNamesEnum.吃大席场景) {
                director.loadScene(sceneNamesEnum.吃大席场景);
            } else if (director.getScene().name === sceneNamesEnum.吃甜品) {
                director.loadScene(sceneNamesEnum.吃甜品);
            }
        });
    }


    /** 关闭动作 */
    public closeAnim(callBack?: Function) {
        let rootNode = this.node.getChildByName("Root");
        tween(rootNode)
            .to(0.23, { scale: v3(0.1, 0.1, 0.1) }, { easing: easing.backIn })
            .call(() => {
                rootNode.setScale(1, 1, 1);
                UIManager.instance.hideUI(UINamesEnum.settingUI, callBack);
            })
            .start();
    }

    show(): void {
        let rootNode = this.node.getChildByName("Root");
        let scale = rootNode.getScale();
        rootNode.scale = v3(0, 0, 0);
        rootNode.setPosition(rootNode.position.x, rootNode.position.y + 500);
        tween(rootNode)
            .by(0.3, { scale, position: v3(0, -500, 0) }, { easing: easing.backOut })
            .start();

        // 隐藏返回主页按钮
        let activeFlag = director.getScene().name !== sceneNamesEnum.主页场景;
        this.node.getChildByName("Root").getChildByName("Btns").getChildByName("BackHomeBtn").active = activeFlag;
        this.node.getChildByName("Root").getChildByName("Btns").getChildByName("AgainBtn").active = activeFlag;
    }
    hide(): void {
        // let rootNode = this.node.getChildByName("Root");
        // Tween.stopAllByTarget(rootNode);
        // rootNode.setScale(1, 1);
    }
    enableBtn(): void {

    }
    disableBtn(): void {

    }

    // test
    cleanStorage() {
        sys.localStorage.clear();
        director.loadScene(sceneNamesEnum.主页场景);
    }
}
