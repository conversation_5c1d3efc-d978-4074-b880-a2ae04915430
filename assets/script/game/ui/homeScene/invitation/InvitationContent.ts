import { _decorator, Component, easing, Label, Node, Sprite, tween, UITransform, v3, view } from 'cc';
import { Tools } from 'db://assets/script/common/Tools';
import { IInvitationInfo, prefabNameEnums } from '../../../chiDaXi/Index';
import { RuntimeData } from '../../../chiDaXi/data/GameData';
import { CoupleData } from '../../../chiDaXi/data/CoupleData';
const { ccclass, property } = _decorator;

@ccclass('InvitationContent')
export class InvitationContent extends Component {
    @property(Label)
    private Y: Label = null;
    @property(Label)
    private M: Label = null;
    @property(Label)
    private D: Label = null;
    @property([Label])
    private inviterNames: Label[] = [];
    @property([Label])
    private inviterSignatureNames: Label[] = [];
    @property(Label)
    private dayCount: Label = null;

    protected onLoad(): void {
        // 获取邀请函内容信息
        let info: IInvitationInfo = RuntimeData._ins.curInvitationInfo;
        if(!info){
            let maleName = CoupleData.instance.genRandomMaleName();
            let femaleName = CoupleData.instance.genRandomFemaleName();
            info = {
                inviterName: [maleName, femaleName],
                startTime: new Date().getTime(),
                dayCount: RuntimeData._ins.targetLevel
            }
            RuntimeData._ins.curInvitationInfo = info;
        }

        // 设置邀请函内容
        let date = new Date(info.startTime);
        let y = date.getFullYear();
        let m = date.getMonth() + 1;
        let d = date.getDate();

        this.Y.string = y.toString();
        this.M.string = m.toString();
        this.D.string = d.toString();

        for(let i = 0; i < info.inviterName.length; i++){
            this.inviterNames[i].string = info.inviterName[i];
            this.inviterSignatureNames[i].string = info.inviterName[i];
        }

        this.dayCount.string = info.dayCount.toString();
    }
}