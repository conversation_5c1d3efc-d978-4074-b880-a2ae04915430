import { _decorator, Component, easing, Node, size, Sprite, tween, UITransform, v3, view } from 'cc';
import { Tools } from 'db://assets/script/common/Tools';
import { prefabNameEnums } from '../../../chiDaXi/Index';
const { ccclass, property } = _decorator;

@ccclass('Invitation')
export class Invitation extends Component {
    @property(Node)
    lid: Node;
    @property(Node)
    contentParentNode: Node;
    contentNode: Node;

    start() {
        this.node.children.forEach((child, index)=>{
            child.setSiblingIndex(index);
        });

        this.contentNode = Tools.newPrefab(prefabNameEnums.请帖内容, this.contentParentNode);
    }

    public open(){
        this.openLid();
    }

    private openLid() {
        let openTime = 0.23; 
        tween(this.lid)
            .to(openTime, {scale: v3(1, -1, 1)}, {easing: easing.sineInOut})
            .call(()=>{
                this.lid.setSiblingIndex(this.contentParentNode.getSiblingIndex() - 1);
                this.takeOut();
            })
            .start();
    }

    private takeOut(){
        let pickUpTime = 0.34;
        let letter = this.contentNode.getChildByName("Content");
        tween(letter)
            .by(pickUpTime, {position: v3(0, letter.getComponent(UITransform).contentSize.height)})
            .call(()=>{
                this.contentNode.setParent(this.node);
                this.contentNode.setSiblingIndex(this.node.children.length - 1);
                let bgNode = this.contentNode.getChildByName("Bg");
                bgNode.getComponent(UITransform).setContentSize(view.getVisibleSize());
                bgNode.active = true;
                bgNode.once(Node.EventType.TOUCH_START, this.close, this);
            })
            .to(0.12, { scale: v3(1.3, 1.3) })
            .to(0.23, { scale: v3(1, 1), position: v3(0, letter.getComponent(UITransform).contentSize.height * 3 / 4)}, {easing: easing.sineInOut})
            .start();

        tween(letter.getComponent(Sprite))
            .to(pickUpTime, {fillRange: -1})
            .start();

        let contentMaskTransform = letter.getChildByName("Mask").getComponent(UITransform);
        tween(contentMaskTransform)
            .to(pickUpTime, {contentSize: size(contentMaskTransform.contentSize.width, 803)})
            .start();
    }

    close(){
        // console.log("关闭");
        this.takeBack();
    }

    private closeLid() {
        let closeTime = 0.23;
        tween(this.lid)
            .call(() => {
                this.lid.setSiblingIndex(this.contentParentNode.getSiblingIndex() + 1);
            })
            .to(closeTime, { scale: v3(1, 1, 1) }, { easing: easing.sineInOut })
            .call(()=>{
                // 信封隐藏收回动作
                tween(this.node)
                    .by(0.23, { position: v3(0, -500), scale: v3(0.3, 0.3) }, { easing: easing.backIn })
                    .call(() => {
                        this.contentNode.destroy();
                        this.node.destroy();
                    })
                    .start();
            })
            .start();
    }

    private takeBack() {
        let takeBackTime = 0.34;
        let letter = this.contentNode.getChildByName("Content");
        tween(letter)
            .to(0.12, { scale: v3(1.3, 1.3) })
            .to(0.23, { scale: v3(1, 1), position: v3(0, letter.getComponent(UITransform).contentSize.height) }, { easing: easing.sineInOut })
            .call(() => {
                this.contentNode.setParent(this.contentParentNode);
                let bgNode = this.contentNode.getChildByName("Bg");
                bgNode.active = false;
            })
            .to(takeBackTime, { position: v3(0, 0) })
            .start();

        tween(letter.getComponent(Sprite))
            .delay(0.12 + 0.23)
            .to(takeBackTime, { fillRange: -0.3 })
            .call(()=>{
                this.closeLid();
            })
            .start();

        let contentMaskTransform = letter.getChildByName("Mask").getComponent(UITransform);
        tween(contentMaskTransform)
            .to(takeBackTime, { contentSize: size(contentMaskTransform.contentSize.width, 803 * 0.3) })
            .start();
    }

    update(deltaTime: number) {
        
    }
}


