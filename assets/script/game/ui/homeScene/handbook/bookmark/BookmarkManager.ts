import { _decorator, Component, EventTouch, Node, UITransform, v3 } from 'cc';
import { Tools } from 'db://assets/script/common/Tools';
import { prefabNameEnums, textureNamesEnum } from '../../../../chiDaXi/Index';
import { Bookmark } from './Bookmark';
import { dishTypeEnum, dishTypeName } from '../../../../chiDaXi/data/DishesData';
import { DishInHandbook } from '../DishInHandbook';
import { HandbookPage } from '../HandbookPage';
const { ccclass, property } = _decorator;

@ccclass('BookmarkManager')
export class BookmarkManager extends Component {
    rootNode: Node;

    protected onLoad(): void {
        this.rootNode = this.node.parent;
    }

    start() {
        this.createBookmarks();

        this.selectBookmark(this.node.children[0]);
    }

    createBookmarks(){
        let dishTypes = Object.keys(dishTypeName);

        let contentWidth = this.node.getComponent(UITransform).contentSize.width;
        let left = 15;
        let itemWidth = 185;
        let itemCount = dishTypes.length;
        let spaceX = (contentWidth - left * 2 - itemWidth * itemCount) / (itemCount - 1);
        let firstX = -contentWidth / 2 + left + itemWidth / 2;

        for(let i = 0; i < itemCount; i++){
            let x = firstX + i * (spaceX + itemWidth);
            let bookmark = Tools.newPrefab(prefabNameEnums.书签, this.node, v3(x, 0));
            let bookmarkScript = bookmark.getComponent(Bookmark);
            bookmarkScript.initByDishType(parseInt(dishTypes[i]));

            bookmark.on(Node.EventType.TOUCH_START, this.onTouchStart, this);
        }
    }

    onTouchStart(event: EventTouch, component: BookmarkManager){
        let touchWorldPos = event.getUILocation();
        let touchPos = Tools.getToNodePosForWorld(v3(touchWorldPos.x, touchWorldPos.y), this.node);
        let targetBookmark: Node = null;
        let minDistance = Number.MAX_VALUE;
        this.node.children.forEach(child=>{
            let childPosX = child.position.x;
            let distance = Math.abs(touchPos.x - childPosX);
            if(distance < minDistance){
                minDistance = distance;
                targetBookmark = child;
            }
        });

        if(minDistance > 150) return;

        let bookmarkScript = targetBookmark.getComponent(Bookmark);
        if(bookmarkScript.selectedFlag) return;

        this.selectBookmark(targetBookmark);
    }

    selectBookmark(bookmark: Node){
        this.node.children.forEach(child=>{
            let selectedFlag = child == bookmark;
            let bookmarkScript = child.getComponent(Bookmark);
            bookmarkScript.selectedFlag = selectedFlag;
        });

        let bookmarkScript = bookmark.getComponent(Bookmark);
        // 展示选中种类的菜品
        let dishType = bookmarkScript.dishType;
        let handbookPageScript = this.rootNode.getComponentInChildren(HandbookPage);
        handbookPageScript.showContentByDishType(dishType);
    }

    update(deltaTime: number) {
        
    }
}


