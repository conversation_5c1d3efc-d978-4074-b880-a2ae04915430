import { _decorator, Component, Node, UITransform } from 'cc';
import { Tools } from 'db://assets/script/common/Tools';
import { BookmarkManager } from './BookmarkManager';
import { textureNamesEnum } from '../../../../chiDaXi/Index';
import { dishTypeEnum } from '../../../../chiDaXi/data/DishesData';
const { ccclass, property } = _decorator;

@ccclass('Bookmark')
export class Bookmark extends Component {
    @property(Node)
    bg: Node = null;
    @property(Node)
    description: Node = null;

    private _selectedFlag: boolean = false;

    public dishType: number = -1;

    protected start(): void {

        this.updateDisplay();
    }

    initByDishType(dishType: dishTypeEnum) {
        this.dishType = dishType;

        let dishTypeNameImg = textureNamesEnum.书签描述 + (100 + dishType);
            
        Tools.setSpriteFrame(this.node.getChildByName("Description"), dishTypeNameImg);
    }

    public get selectedFlag(): boolean {
        return this._selectedFlag;
    }
    public set selectedFlag(value: boolean) { 
        this._selectedFlag = value;
        this.updateDisplay();
    }

    updateDisplay(){
        let bgImgName = textureNamesEnum.书签背景 + (this.selectedFlag ? 1 : 0);
        Tools.setSpriteFrame(this.bg, bgImgName);

        let offsetY = 0;
        if(this.selectedFlag){
            offsetY = -20;
        }
        let bgHeight = this.bg.getComponent(UITransform).contentSize.height;
        this.node.setPosition(this.node.position.x, bgHeight / 2 + offsetY);
    }
}


