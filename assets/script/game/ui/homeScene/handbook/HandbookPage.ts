import { _decorator, Component, Label, Node } from 'cc';
import { Tools } from 'db://assets/script/common/Tools';
import { prefabNameEnums } from '../../../chiDaXi/Index';
import { RuntimeData } from '../../../chiDaXi/data/GameData';
import { HandbookItem } from '../handbookVer2/HandbookItem';
const { ccclass, property } = _decorator;

@ccclass('HandbookPage')
export class HandbookPage extends Component {
    @property(Node)
    private nextPageBtn: Node = null;
    @property(Node)
    private proviousBtn: Node = null;
    @property(Label)
    private pageNumLabel: Label = null;

    private _pageNum: number = 1;
    private set pageNum(val: number){
        this._pageNum = val;
        this.pageNumLabel.string = val.toString();
    }
    public get pageNum(): number{
        return this._pageNum;
    }

    showContentByItemType(itemType: any){
        this.node.destroyAllChildren();
        
        let showCountSinglePage = 12;
        let startIndex = 0 + showCountSinglePage * (this._pageNum - 1);
        let endIndex = startIndex + showCountSinglePage;

        let items = RuntimeData._ins.handbookItems.filter(item => item.type == itemType);
        // console.log(items, RemoteServerData.instance.serverUserInfoData);
        
        for(let i = startIndex; i < endIndex; i++){
            let itemInfo = items[i];
            if(!itemInfo) break;

            let dishInHandbook = Tools.newPrefab(prefabNameEnums.图鉴界面物品, this.node);
            dishInHandbook.getComponent(HandbookItem).initByItemInfo(itemInfo);
        }

        this.updateTrunPageBtn(Math.ceil(items.length / showCountSinglePage));
    }

    turnPage(touch: any, isNext: string){
        if(isNext == "1"){
            this.pageNum++;
        }else{
            this.pageNum--;
        }
        this.showContentByItemType(4);
    }

    updateTrunPageBtn(maxPageNum: number){
        if(maxPageNum <= 1){
            this.nextPageBtn.active = false;
            this.proviousBtn.active = false;
            return;
        }
        this.proviousBtn.active = this._pageNum > 1;
        this.nextPageBtn.active = this._pageNum < maxPageNum;
    }
}