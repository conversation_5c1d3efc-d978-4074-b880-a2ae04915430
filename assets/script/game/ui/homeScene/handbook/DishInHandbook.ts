import { _decorator, Component, Node, Sprite } from 'cc';
import { dishTypeEnum, IDishInfo } from '../../../chiDaXi/data/DishesData';
import { textureNamesEnum } from '../../../chiDaXi/Index';
import { Tools } from 'db://assets/script/common/Tools';
const { ccclass, property } = _decorator;

@ccclass('DishInHandbook')
export class DishInHandbook extends Component {
    @property(Node)
    dishSpr: Node = null;
    @property(Node)
    dishName: Node = null;

    private _dishInfo: IDishInfo;

    public initByDishType(dishInfo: IDishInfo) {
        this._dishInfo = dishInfo;

        let dishType = this._dishInfo.type;
        let dishId = this._dishInfo.id;
        let sprImg = textureNamesEnum.菜品 + (100 * dishType + dishId);

        Tools.setSpriteFrame(this.dishSpr, sprImg);

        let dishNameImg = textureNamesEnum.菜品名字 + (100 * dishType + dishId);
        Tools.setSpriteFrame(this.dishName, dishNameImg);

        this._dishInfo.unlocked ? this.unlocked() : this.locked();
    }

    public locked(){
        // console.log("锁定:", this._dishInfo.name);
        this.dishSpr.getComponent(Sprite).grayscale = true;
        this.dishName.active = false;
    }
    public unlocked(){
        // console.log("解锁:", this._dishInfo.name);
        this.dishSpr.getComponent(Sprite).grayscale = false;
        this.dishName.active = true;

    }
}


