import { _decorator, color, Component, easing, instantiate, Label, Node, Sprite, tween, UITransform, v3 } from 'cc';
import { IAchievementItemInfo } from '../../../chiDaXi/data/achievement/AchievementItemsData';
import { Tools } from 'db://assets/script/common/Tools';
import { prefabNameEnums, textureNamesEnum } from '../../../chiDaXi/Index';
import { AchievementItem } from './AchievementItem';
import { ResManager } from 'db://assets/script/manager/ResManager';
const { ccclass, property } = _decorator;

@ccclass('AchievementItemDetail')
export class AchievementItemDetail extends Component {
    @property(Node)
    collectionProgress: Node = null;

    public achievementInfo: IAchievementItemInfo = null;

    protected onLoad(): void {
        this.node.getChildByName("CloseBg").on(Node.EventType.TOUCH_START, ()=>{
            this.node.active = false;
        });

        // this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this);
    }

    openAnim(){
        let rootNode = this.node.getChildByName("Root");
        let scale = rootNode.scale.x;
        rootNode.setScale(0, 0);
        tween(rootNode)
            .to(0.34, { scale: v3(scale, scale, scale) }, { easing: easing.backOut })
            .start();
    }

    initData(itemInfo: IAchievementItemInfo){
        this.achievementInfo = itemInfo;

        this.createItemOnCollectionProgress();

        // 初始化图标
        let iconScript = this.node.getComponentInChildren(AchievementItem);
        iconScript.node.off(Node.EventType.TOUCH_START);
        iconScript.initData(itemInfo);

        let count = this.achievementInfo.progressBarNumDescription.length;
        // 获取当前解锁的进度
        let index = itemInfo.unlockedProgress.findIndex(val=>!val);
        if(index == -1) index = itemInfo.unlockedProgress.length + 1;
        if(index > count) index = count;

        // 初始化文字描述
        let descriptionNode = this.node.getChildByName("Root").getChildByName("Description");
        let descriptionStrIndex = 1;
        let str: string;
        do{
            str = "unlockCondition_" + (itemInfo.id * 100 + index * 10 + descriptionStrIndex);
            
            let sprNode = new Node("DescriptionStrSprNode");
            let sprCom = sprNode.addComponent(Sprite);
            sprNode.setParent(descriptionNode);
            // 获取精灵帧
            let sf = ResManager.instance.getSpriteFrame(str);
            if(!sf){
                str = null;
                sf = ResManager.instance.getSpriteFrame("unlockCondition_tail");
            }
            // 赋值
            sprCom.spriteFrame = sf;

            descriptionStrIndex ++;
        }while(str);

        this.scheduleOnce(()=>{
            // 适配文字介绍
            let y = descriptionNode.children[0].position.y;
            let yArr = descriptionNode.children.map(node=>node.position.y);
            let lineArr: Node[][] = [[]];
            yArr.forEach((tempY, index)=>{
                if(Math.abs(tempY - y) < 10){
                    lineArr[lineArr.length - 1].push(descriptionNode.children[index]);
                }else{
                    y = tempY;
                    lineArr.push([descriptionNode.children[index]]);
                }
            });
            for(let i = 0; i < lineArr.length; i++){
                let line = lineArr[i];
                let lastWord = line[line.length - 1];
                let lastWordWidth = lastWord.getComponent(UITransform).width;
                let lastWordX = lastWord.position.x;
                let firstWord = line[0];
                let firstWordWidth = firstWord.getComponent(UITransform).width;
                let firstWordX = firstWord.position.x;
                let lineWidth = lastWordX + lastWordWidth / 2 - (firstWordX - firstWordWidth / 2);
                let offsetX = -lineWidth / 2 - (firstWordX - firstWordWidth / 2);
                line.forEach(word=>word.setPosition(word.position.x + offsetX, word.position.y));
            }
        }, 0);
    }

    onTouchStart(event: Event){
        
    }

    createItemOnCollectionProgress(){
        let itemCount = this.achievementInfo.progressBarNumDescription.length;

        let barWidth = this.collectionProgress.getComponent(UITransform).width;
        let spaceX = barWidth / (itemCount - 1);
        let firstX = -barWidth / 2;

        this.collectionProgress.removeAllChildren();

        for(let i = 0; i < itemCount; i++){
            let item = Tools.newPrefab(prefabNameEnums.itemOnCollectionProgress, this.collectionProgress);
            let x = firstX + spaceX * i;
            item.setPosition(x, 0);

            let description = this.achievementInfo.progressBarNumDescription[i];
            item.getChildByName("Description").getComponent(Label).string = description;

            // 初始化小图标
            let isUnlocked = this.achievementInfo.unlockedProgress[i];
            let iconSprNode = item.getChildByName("Icon");
            Tools.setSpriteFrame(iconSprNode, textureNamesEnum.成就图标 + (this.achievementInfo.id * 100 + i + 1));
            let targetHeight = 100;
            let sprRealHeight = iconSprNode.getComponent(UITransform).contentSize.height;
            let scale = targetHeight / sprRealHeight;
            iconSprNode.setScale(scale, scale);
            // 生成一层遮罩
            let iconMask = instantiate(iconSprNode);
            iconMask.setParent(iconSprNode);
            iconMask.setPosition(0, 0);
            iconMask.getComponent(Sprite).color = color(0, 0, 0, 156);
            iconMask.active = !isUnlocked;
            iconMask.setScale(1, 1, 1);

            // 生成分隔线
            if(i < itemCount - 1){
                Tools.newSprite("achievementDetail_separationLine", this.collectionProgress, v3(x + spaceX / 2, 60 * scale));
            }
        }
    }
}