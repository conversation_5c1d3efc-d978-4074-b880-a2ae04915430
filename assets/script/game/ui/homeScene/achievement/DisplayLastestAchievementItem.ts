import { _decorator, Component, easing, Node, tween, UITransform, v3 } from 'cc';
import { Tools } from 'db://assets/script/common/Tools';
import { textureNamesEnum } from '../../../chiDaXi/Index';
import { AchievementItemsData, IAchievementItemInfo } from '../../../chiDaXi/data/achievement/AchievementItemsData';
import { DisplayItemWithBgLightBase } from '../../../chiDaXi/DisplayItemWithBgLightBase';
const { ccclass, property } = _decorator;

@ccclass('DisplayLastestAchievementItem')
export class DisplayLastestAchievementItem extends DisplayItemWithBgLightBase {
    protected start(): void {
        this.bgLight.setScale(0, 0, 0);
        tween(this.bgLight)
            .to(0.34, {scale: v3(1, 1, 1)}, {easing: easing.backInOut})
            .repeat(
                Number.MAX_SAFE_INTEGER,
                tween()
                    .by(0.34, { scale: v3(0.07, 0.07) }, { easing: easing.sineOut })
                    .by(0.23, { scale: v3(-0.07, -0.07) }, { easing: easing.sineIn })
            )
            .start();

        this.node.once(Node.EventType.TOUCH_START, ()=>{
            tween(this.itemSprNode)
                .to(0.34, {scale: v3(0, 0)}, {easing: easing.backIn})
                .call(()=>{
                    this.node.destroy();
                })
                .start();
        })
    }

    initData(...args: any[]){
        let [itemInfo] = args;
        let index = itemInfo.getUnlockedIndex();
        if (index < 0) index = 0;
        index += 1;
        
        let sprName = textureNamesEnum.成就图标 + (100 * itemInfo.id + index);
        let nameSprName = textureNamesEnum.成就名称 + (100 + itemInfo.id);
        super.initData(sprName, nameSprName);
        
        AchievementItemsData._ins.syncToLastestData();
        
        return 1;
    }
}


