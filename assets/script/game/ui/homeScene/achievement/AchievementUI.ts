import { _decorator, Component, easing, EventTouch, Node, tween, UITransform, v3, Vec3, view } from "cc";
import { Tools } from "db://assets/script/common/Tools";
import { prefabNameEnums } from "../../../chiDaXi/Index";
import { AchievementItemsData } from "../../../chiDaXi/data/achievement/AchievementItemsData";
import { PageActive, UIManager } from "db://assets/script/manager/UIManager";
import { AchievementItem } from "./AchievementItem";
import { UINamesEnum } from "db://assets/script/uiPanel/UIConfig";
import { AchievementItemDetail } from "./AchievementItemDetail";
import { DisplayLastestAchievementItem } from "./DisplayLastestAchievementItem";

const {ccclass, property} = _decorator;

@ccclass("AchievementUI")
export class AchievementUI extends Component implements PageActive{
    @property(Node)
    public contentNode: Node = null;

    private achievementItemDetail: Node;

    private itemPoses: Vec3[] = [];

    protected onLoad(): void {
        // 适配放置板的位置
        let winHeight = view.getVisibleSize().height;
        let normalWinHeight = 1920;
        let bgMask = this.node.getChildByName("BgMask");
        if(winHeight > normalWinHeight){
            // 加一点误差，防止漏边
            normalWinHeight -= 10;
            let scale = winHeight / normalWinHeight;
            bgMask.children.forEach(board=>{
                board.setPosition(board.position.x, board.position.y * scale);
            });
        }

        // 设定成就item（奖杯勋章等）的放置位置
        let colNum = 2;
        let offsetX = view.getVisibleSize().width / 2 * 1 / 2;
        for(let i = 0; i < bgMask.children.length; i++){
            let y = bgMask.children[i].position.y;
            let dir = -1;
            for(let col = 0; col < colNum; col++){
                let x = offsetX * dir;
                dir *= -1;
                this.itemPoses.push(v3(x, y));
            }
        }
    }

    show(): void {
        this.updateAchievementItem();

        let itemId = AchievementItemsData._ins.getLastestUnlockedAchievement();
        if(itemId !== null){
            let itemInfo = AchievementItemsData._ins.achievementItems.find(item => item.id == itemId);
            let asd = Tools.newPrefab("DisplayLastestAchievementItem", this.node);
            let asdScript = asd.getComponent(DisplayLastestAchievementItem);
            asdScript.initData(itemInfo);
        }

        this.contentNode.children.forEach(item => {
            let y = item.position.y;
            let x = item.position.x;
            item.setPosition(x - 1000, y);
            tween(item)
                .to(0.34, {position: v3(x, y)}, {easing: easing.backOut})
                .start();
        });
    }
    hide(): void {
    }
    enableBtn(): void {

        
    }
    disableBtn(): void {
        
    }

    private closeUI(){
        // let rootNode = this.node.getChildByName("Root");
        // tween(rootNode)
        //     .to(0.34, {scale: v3(0, 0, 0)}, {easing: easing.backIn})
        //     .call(()=>{
        //         rootNode.setScale(1, 1, 1);
                UIManager.instance.hideUI(UINamesEnum.achievementUI);
            // })
            // .start();
    }

    public updateAchievementItem(){
        if(this.contentNode.children.length == 0){
            let achievementItems = AchievementItemsData._ins.achievementItems;
            let countIsOddNum = achievementItems.length % 2 !== 0;
            achievementItems.forEach((itemInfo, index)=>{
                itemInfo.checkLocked();
                let item = Tools.newPrefab(prefabNameEnums.achievementItem, this.contentNode);
                let itemScript = item.getComponent(AchievementItem);
                itemScript.initData(itemInfo);
                itemScript.mainScript = this;

                let pos = this.itemPoses[index + 2];
                if(countIsOddNum && index == achievementItems.length - 1){
                    pos = v3(0, pos.y);
                    item.scale.multiplyScalar(1.1);
                }
                item.setPosition(pos);
            });
        }else{
            this.contentNode.children.forEach(item=>{
                let itemScript = item.getComponent(AchievementItem);
                let achievementInfo = itemScript.achievementInfo;
                achievementInfo?.checkLocked();
                itemScript?.initData(achievementInfo);
            }) 
        }
    }

    public openItemDetail(targetItem: Node){
        let rootNode = this.node.getChildByName("Root");
        let itemInfo = targetItem.getComponent(AchievementItem).achievementInfo;
        let itemDetail = this.achievementItemDetail;
        if(!itemDetail)
            itemDetail = Tools.newPrefab(prefabNameEnums.achievementItemDetail, rootNode, v3(0, 100));
        itemDetail.active = true;
        let script = itemDetail.getComponent(AchievementItemDetail);
        script.initData(itemInfo);
        script.openAnim();
    }
}