import { _decorator, color, Component, Label, Node, Sprite, UITransform } from 'cc';
import { IAchievementItemInfo } from '../../../chiDaXi/data/achievement/AchievementItemsData';
import { AchievementUI } from './AchievementUI';
import { Tools } from 'db://assets/script/common/Tools';
import { textureNamesEnum } from '../../../chiDaXi/Index';
const { ccclass, property } = _decorator;

@ccclass('AchievementItem')
export class AchievementItem extends Component {
    @property(Node)
    public nameNode: Node = null;

    public mainScript: AchievementUI;

    public achievementInfo: IAchievementItemInfo = null;

    protected onLoad(): void {
        this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this);
    }

    initData(itemInfo: IAchievementItemInfo){
        this.achievementInfo = itemInfo;
        Tools.setSpriteFrame(this.nameNode, textureNamesEnum.成就名称 + (100 + itemInfo.id));

        // 获取当前解锁的进度
        let index = itemInfo.getUnlockedIndex();
        if(index < 0) index = 0;
        index += 1;

        let sprNode = this.node.getChildByName("SprNode");
        Tools.setSpriteFrame(sprNode, textureNamesEnum.成就图标 + (100 * itemInfo.id + index));
        let sprNodeSize = sprNode.getComponent(UITransform).contentSize;
        this.node.getComponent(UITransform).setContentSize(sprNodeSize);
        this.node.setScale(0.8, 0.8);

        // sprNode.getComponent(Sprite).grayscale = itemInfo.getUnlockedIndex() < 0;
        if (itemInfo.getUnlockedIndex() < 0)
            sprNode.getComponent(Sprite).color = color(41, 41, 41, 200);
    }

    onTouchStart(event: Event){
        this.mainScript.openItemDetail(this.node);
    }
}