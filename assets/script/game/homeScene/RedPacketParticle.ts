
import { _decorator, Component, is<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>we<PERSON>, tween, UIOpacity, UITransform, v3, Vec3 } from "cc";
import { BeizerUtil } from "../../common/BeizerUtil";
import { Tools } from "../../common/Tools";
import { PoolManager } from "../../manager/PoolManager";
const {ccclass, property} = _decorator;

@ccclass("RedPacketParticle")
export class RedPacketParticle extends Component{
    // protected onLoad(): void {
    //     this.reuse();
    // }

    protected reuse(): void {
        let imgName = "redPacket_JQ_";
        let id = 100 + (Math.random() > 0.2 ? 1 : Math.ceil(Math.random() * 8));
        Tools.setSpriteFrame(this.node, imgName + id);
        // let transformCom = this.node.getComponent(UITransform);
        // let contentSize = transformCom.contentSize;
        let scale = 0.5; // Math.random() > 0.3 ? 0.3: Math.random() * 0.3 + 0.3;
        // transformCom.setContentSize(contentSize.width * scale, contentSize.height * scale);
        this.node.setScale(scale, scale, 1);

        this.scheduleOnce(() => {
            this.splashAnim();
        }, 0);
    }

    /** 钞票枪喷射动作 */
    splashAnim() {
        let positionList: { x: number, y: number }[] = [];
        let pointCount = Math.random() * 5 + 20;
        let startPos = this.node.getPosition();
        positionList.push({
            x: startPos.x,
            y: startPos.y
        });
        let pos: Vec3 = startPos;
        let preAngle = 0;
        for (let i = 0; i < pointCount; i++) {
            let randomAngle = preAngle + (Math.random() > 0.023 ? (Math.random() * 90 - 45) : ((Math.random() > 0.5 ? 1 : -1) * (Math.random() * 90 + 90)));
            // let randomAngle = Math.random() * 90 - 45;
            let ranodmDis = Math.random() * 50 + 100;
            let randomDir = Tools.getPosForAngleLen(randomAngle, ranodmDis);
            pos.add(v3(randomDir.x, randomDir.y));
            positionList.push({
                x: pos.x,
                y: pos.y
            });
            preAngle = randomAngle;
        }
        let pathLength = BeizerUtil.getLength(positionList, 100);
        let moveV = Math.random() * 300 + 500;
        let moveTime = pathLength / moveV;
        this.bezierFunc(moveTime, positionList);

        tween(this.node)
            .to(moveTime, { angle: Math.random() * 7200 - 3600 })
            .start();
        tween(this.node)
            .repeatForever(
                tween()
                    .to(0.23, { scale: v3(-0.5, 0.5) })
                    .to(0.23, { scale: v3(0.5, 0.5) })
            )
            .start();
        tween(this.node.getComponent(UIOpacity))
            .delay(1)
            .to(moveTime, { opacity: 0 })
            .start();
    }

    bezierFunc(duration: number, positionList: {
        x: number;
        y: number;
    }[]): void {
        let targetNode = this.node;
        let animEndCallback: Function;
        let startPos = targetNode.getPosition();
        tween(targetNode)
            .to(duration, {}, {
                onUpdate(target: any, ratio: number) {
                    let pos: { x: number, y: number } = BeizerUtil.getPosition(positionList, ratio);
                    targetNode.setPosition(pos.x, pos.y);
                }
            })
            .call(() => {
                if (!targetNode || !isValid(targetNode)) return;
                animEndCallback && animEndCallback();
                Tween.stopAllByTarget(this.node);
                Tween.stopAllByTarget(targetNode);
                Tween.stopAllByTarget(startPos);
                let opacityCom = this.node.getComponent(UIOpacity);
                Tween.stopAllByTarget(opacityCom);
                opacityCom.opacity = 255;
                this.node.angle = 0;
                this.node.setScale(1, 1, 1);
                this.node.setPosition(0, 0);
                PoolManager.Despawn(this.node);
            })
            .start();
    }
}