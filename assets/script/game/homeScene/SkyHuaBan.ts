import { _decorator, Component, Node, Sprite, tween, Tween, UIOpacity, v3, Vec3, view } from 'cc';
import { PoolManager } from '../../manager/PoolManager';
import { Util } from '../../common/Util';
import { ResManager } from '../../manager/ResManager';
const { ccclass, property } = _decorator;

@ccclass('SkyHuaBan')
export class SkyHuaBan extends Component {
    start() {

    }
    initHuaBan() {
        let caidai = this.GetEmptySpriteNode(this.node);
        let type = Util.getRandomNum(1, 3, true);
        caidai.getChildByName("caidai").getComponent(Sprite).spriteFrame = ResManager.instance.getSpriteFrame("huaban" + type);
        let x = Math.random() * view.getVisibleSize().width - view.getVisibleSize().width / 2;
        let y = view.getVisibleSize().height / 2 + 30;
        caidai.setPosition(x, y);
        caidai.getChildByName("caidai").setPosition(Vec3.ZERO);
        var sca = Util.getRandomNum(0.7, 1, false);
        caidai.scale = v3(sca, sca, sca);
        caidai.getComponent(UIOpacity).opacity = 255;
        let time = 0.27 + 0.1;
        let offsetX = 100;

        var TX1 = Math.random() * 6 + 4;
        var TX2 = Math.random() * 6 + 4;
        var TX3 = Math.random() * 6 + 4;
        var offY = -(Math.random() * 600 + 1000) * 0.7
        tween(caidai.getComponent(UIOpacity))
            .delay(time * 4)
            .to(time * 6, { opacity: 0 })
            .call(() => {
                Tween.stopAllByTarget(caidai.getChildByName("caidai"));
                PoolManager.Despawn(caidai);
            })
            .start();
        tween(caidai.getChildByName("caidai"))
            .parallel(
                tween().by(time * 10, { angle: (Math.random() * 720 + 480) * (Math.random() > 0.5 ? 1 : -1) }),
                tween().by(time * TX1, { position: v3((Math.random() * offsetX + offsetX) * (Math.random() > 0.5 ? -1 : 1), offY * (TX1 / 10)) })
                    .by(time * TX2, { position: v3((Math.random() * offsetX + offsetX) * (Math.random() > 0.5 ? -1 : 1), offY * (TX2 / 10)) })
                    .by(time * TX3, { position: v3((Math.random() * offsetX + offsetX) * (Math.random() > 0.5 ? -1 : 1), offY * (TX3 / 10)) })
            )
            .start();
    }
    /**
    * 获取可替换图片的预制体
    * @param parent 预制体的父节点
    * @param zIndex 预制体在父节点下的层级
    * @returns 预制体
    */
    GetEmptySpriteNode(parent: Node): Node {
        const emptyNode: Node = PoolManager.Spawn("emptySpriteNode");
        parent.addChild(emptyNode);
        return emptyNode;
    }
    det: number = 15;
    countT: number = 0;
    update(deltaTime: number) {
        this.countT++;
        if (this.countT >= this.det) {
            this.countT = 0;
            this.initHuaBan();
        }
    }
}


