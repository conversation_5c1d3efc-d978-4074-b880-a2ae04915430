import { _decorator, Component, instantiate, Prefab, tween, Node, v3, easing } from 'cc';
import { PoolManager } from '../../manager/PoolManager';
const { ccclass, property } = _decorator;

@ccclass('JieQinAnim')
export class JieQinAnim extends Component {
    @property(Prefab)
    redPacketPrefab: Prefab;
    @property(Node)
    moneyGun: Node;

    start() {
        this.createRedPacket();

        this.scheduleOnce(()=>{
            this.showDialog();
        }, 2);
    }

    createRedPacket(){
        let count = Math.random() > 0.23 ? 1 : Math.random() * 3 + 2;
        for (let i = 0; i < count; i++) {
            // let rpp = instantiate(this.redPacketPrefab);
            // rpp.setPosition(this.moneyGun.getPosition());
            // rpp.setParent(this.node);
            let rpp = PoolManager.Spawn(this.redPacketPrefab.name, this.moneyGun);
        }

        this.scheduleOnce(()=>{
            this.createRedPacket();
        }, 0.07 + Math.random() * 0.1);
    }

    showDialog(){
        let dialogParent = this.node.getChildByName("Dialog");
        for(let i = 0; i < 2; i++){
            let dialog = dialogParent.children[i];
            dialog.setScale(0, 0, 1);
            dialog.active = true;
            tween(dialog)
                .delay(2 * i)
                .to(0.56, {scale: v3(1, 1, 1)}, {easing: easing.bounceOut})
                .delay(2)
                .call(()=>{
                    dialog.active = false;
                    dialog.setScale(0, 0, 1);
                })
                .start();
        }

        this.scheduleOnce(()=>{
            this.showDialog();
        }, Math.random() * 3 + 5);
    }
}


