import { _decorator, Component, Node, Sprite, tween, Tween, UIOpacity, v3, Vec3, view } from 'cc';
import { Util } from '../../common/Util';
import { PoolManager } from '../../manager/PoolManager';
import { ResManager } from '../../manager/ResManager';
const { ccclass, property } = _decorator;

@ccclass('HuaBanTs')
export class HuaBanTs extends Component {
    @property(Node)
    initNode:Node = null;
    @property(Node)
    targetNode: Node = null;
    ribbonArr: Node[] = [];
    start() {
        this.createRibbon();
        this.schedule(() => {
            this.createRibbon();
        }, 2/0.8)
    }
    createRibbon() {
        let num = Util.getRandomNum(5, 15, true);
        // AudioTools._ins.playAudio("ribbon", GameModel._ins.comMusicVolume);

        for (let i = 0; i < num; i++) {
            let caidai = this.GetEmptySpriteNode(this.node);

            let type = Util.getRandomNum(1, 3, true);
            caidai.getChildByName("caidai").getComponent(Sprite).spriteFrame = ResManager.instance.getSpriteFrame("huaban" + type);
            caidai.setPosition(this.initNode.position);
            caidai.getChildByName("caidai").setPosition(Vec3.ZERO);
            var sca = Util.getRandomNum(0.7, 1.2, false);
            caidai.scale = v3(sca, sca);
            caidai.getComponent(UIOpacity).opacity = 255;
            this.ribbonArr.push(caidai);

            let angle = Math.random() * 360 * Math.PI / 180;
            let length = Math.random() * 400;
            let targetP = v3(this.targetNode.position.x + Math.sin(angle) * length, this.targetNode.position.y + Math.cos(angle) * length + 150);
            let time = 0.27 + 0.1;
            let time1 = 0.37;
            let offsetX = 100;

            var ranSca = Math.random() * 0.2 + 0.5;
            var TX1 = Math.random() * 6 + 4;
            var TX2 = Math.random() * 6 + 4;
            var TX3 = Math.random() * 6 + 4;
            var offY = -(Math.random() * 600 + 1000) * 0.7
            tween(caidai.getComponent(UIOpacity))
                .delay(time * 4)
                .to(time * 6, { opacity: 0 })
                .call(() => {
                    Tween.stopAllByTarget(caidai.getChildByName("caidai"));
                    PoolManager.Despawn(caidai);
                })
                .start();
            tween(caidai)
                .parallel(
                    tween().to(time1 * 1.5, { position: v3(targetP.x, targetP.y) }, { easing: "cubicOut" }),
                    tween().to(time1 * 1.5, { scale: v3(ranSca, ranSca) })
                )
                .start();
            tween(caidai.getChildByName("caidai"))
                .parallel(
                    tween().by(time * 10, { angle: (Math.random() * 720 + 480) * (Math.random() > 0.5 ? 1 : -1) }),
                    tween().by(time * TX1, { position: v3((Math.random() * offsetX + offsetX) * (Math.random() > 0.5 ? -1 : 1), offY * (TX1 / 10)) })
                        .by(time * TX2, { position: v3((Math.random() * offsetX + offsetX) * (Math.random() > 0.5 ? -1 : 1), offY * (TX2 / 10)) })
                        .by(time * TX3, { position: v3((Math.random() * offsetX + offsetX) * (Math.random() > 0.5 ? -1 : 1), offY * (TX3 / 10)) })
                )
                .start();
        }
    }

    ClearRibbon() {
        for (var i = 0; i < this.ribbonArr.length; i++) {
            Tween.stopAllByTarget(this.ribbonArr[i]);
            PoolManager.Despawn(this.ribbonArr[i]);
        }
        this.ribbonArr = [];
    }
    /**
  * 获取可替换图片的预制体
  * @param parent 预制体的父节点
  * @param zIndex 预制体在父节点下的层级
  * @returns 预制体
  */
    GetEmptySpriteNode(parent: Node): Node {
        const emptyNode: Node = PoolManager.Spawn("emptySpriteNode");
        parent.addChild(emptyNode);
        return emptyNode;
    }
    update(deltaTime: number) {

    }
}


