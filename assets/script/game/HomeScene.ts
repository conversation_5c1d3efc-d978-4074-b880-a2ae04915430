import { _decorator, Asset, assetManager, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Component, director, easing, EditBox, error, Label, Node, Sprite, Tween, tween, utils, v3 } from 'cc';
import { AudioManager } from '../manager/AudioManager';
import { bundleNamesEnum, prefabNameEnums, sceneNamesEnum } from './chiDaXi/Index';
import { UIManager } from '../manager/UIManager';
import { UINamesEnum } from '../uiPanel/UIConfig';
import { AdManager } from '../ads/AdManager';
import { DailyRefresh, DailyRefreshItems } from '../common/DailyRefresh';
import { RuntimeData } from './chiDaXi/data/GameData';
import { AdManager_ZJ } from '../ads/AdManager_ZJ';
import { Tools } from '../common/Tools';
import { Invitation } from './ui/homeScene/invitation/Invitation';
import { ResManager } from '../manager/ResManager';
import { CommonLoadingUIManager } from './ui/loadingUI/CommonLoadingUIManager';
import { LoadingUI } from './ui/loadingUI/LoadingUI';
import { GameNameLogo } from './ui/homeScene/gameNameLogo/GameNameLogo';
import { GameModel } from '../model/GameModel';
import { Particle } from './particle/Particle';
import { AutoGame } from '../common/AutoGame';
import { releaseType } from '../Enum/Enum';
const { ccclass, property } = _decorator;

@ccclass('HomeScene')
export class HomeScene extends Component {
    @property(Node)
    startBtn: Node = null;
    @property(Node)
    specialDishLevelBtn: Node = null;
    @property(Node)
    private enterMainGameBtn = null;
    @property(Node)
    eatCakeBtn: Node = null
    @property(LoadingUI)
    private loadingUI: LoadingUI = null;
    @property(Label)
    private levelLabelCom: Label = null;
    @property(Node)
    private autoStartBtnNode: Node = null;
    @property(EditBox)
    private enterLevelNum: EditBox = null;
    @property(Node)
    openAchievementBtn: Node = null;
    @property(Node)
    shareGameBtn: Node = null;
    @property(Node)
    openHandBookBtn: Node = null;
    @property(Node)
    CurLevel_StartGameBtn: Node = null
    /**是否连续点击 */
    isLoop: boolean = false
    protected onLoad(): void {
        // 先加载主游戏资源包
        assetManager.loadBundle('mainGameBundle', (err: Error, bundle: AssetManager.Bundle) => {
            if (err) {
                console.error('[HomeScene] 加载mainGameBundle失败:', err);
                return;
            }
            + bundle.preloadDir("")
        });
        RuntimeData._ins.init();

        // 展示加载页面，并加载主页需要的资源包
        this.loadingUI.node.active = true;
        ResManager.instance.bundelLoadDir(bundleNamesEnum.主页场景, "",
            this.onProgress.bind(this),
            this.onComplete.bind(this)
        );
        this.scheduleOnce(() => {
            AudioManager.stopMusic();
        }, 0);
        this._adaptWechatAppletBtnPos();
        director.preloadScene(sceneNamesEnum.吃大席场景);
        this.enterMainGameBtn.setPosition(v3(0, this.openAchievementBtn.position.y))
        this.eatCakeBtn.setPosition(v3(0, this.openAchievementBtn.position.y + 320))
        this.CurLevel_StartGameBtn.setPosition(v3(0, this.openAchievementBtn.position.y + 110))
    }
    /** 适配微信小游戏按钮位置 */
    private _adaptWechatAppletBtnPos() {
        if (GameModel.instance.releaseType !== releaseType.applet_wechat) return;
        this.openAchievementBtn.setPosition(this.openAchievementBtn.position.x, this.openAchievementBtn.position.y + 180);
        this.shareGameBtn.setPosition(this.shareGameBtn.position.x, this.shareGameBtn.position.y + 90);
        this.openHandBookBtn.setPosition(this.openHandBookBtn.position.x, this.openHandBookBtn.position.y + 90);
    }
    /** 按钮醒目动作 */
    btnAttentionAnim(btnNode: Node) {
        // 主玩法按钮，呼吸动作
        let animTime = 0.56;
        tween(btnNode)
            .tag(1)
            .by(animTime / 4, { scale: v3(0.1, 0.1) }, { easing: easing.sineIn })
            .by(animTime / 4, { scale: v3(-0.1, -0.1) }, { easing: easing.sineInOut })
            .by(animTime / 4, { scale: v3(0.1, 0.1) }, { easing: easing.sineIn })
            .by(animTime / 4, { scale: v3(-0.1, -0.1) }, { easing: easing.sineInOut })
            .delay(0.78)
            .union()
            .repeatForever()
            .start();
        btnNode.once(Node.EventType.TOUCH_START, () => {
            // console.log("一次性");
            Tween.stopAllByTag(1, btnNode);
            btnNode.setScale(1, 1);
        });
        btnNode.once(Node.EventType.TOUCH_CANCEL, () => {
            btnNode.setScale(1, 1);
            this.btnAttentionAnim(btnNode);
        });
    }
    /**
     * 等待资源加载完毕后，主页的入口函数
     */
    protected startCallback(): void {
        // 播放主页背景音乐
        GameModel.instance.curBgmName = "homeBGM";
        this.scheduleOnce(() => {
            AudioManager.playMusic(GameModel.instance.curBgmName, 0.5);
        }, 0);
        this.loadingUI.node.destroy();
        this.btnAttentionAnim(this.eatCakeBtn);
        // UIManager.instance.showUI(UINamesEnum.globalUI);
        this.updateStartGameBtn();
        this.updateSpecialLevelBtn();
        // console.log("吃席 ", RuntimeData._ins.targetLevel, " 天");
        // 不是自动模式，且是抖音小游戏的情况下
        if (!AutoGame.instance.autoGameVer && GameModel.instance.releaseType == releaseType.applet_ziJie) {
            // 判断今天是否已经领取侧边栏奖励
            let isGet = DailyRefresh.recordBehaviorNum(DailyRefreshItems.launchFromSidebar, 0, false);
            // 如果没有获取，则打开侧边栏UI
            if (!isGet) {
                this.scheduleOnce(() => {
                    this.openGoToSidebarUI();
                }, 0.1);
            }
        }
        let animId = RuntimeData._ins.homeSceneAnimId;
        if (animId == -1) {
            animId = RuntimeData._ins.getRandomHomeSceneAnimId();
            RuntimeData._ins.homeSceneAnimId = animId;
        }
        if (animId == 1) {
            // 展示钞票枪动作
            let jieQinAnim = Tools.newPrefab("JieQinAnim", this.node.getChildByName("GameLayer").getChildByName("JieQinAnim"));
            jieQinAnim.getComponentInChildren(GameNameLogo).enabled = true;
        } else {
            this.node.getChildByName("GameLayer").getChildByName("StageAnim").active = true;
            this.node.getComponentInChildren(GameNameLogo).enabled = true;
        }
    }
    play(event: any, data: string): void {
        if (data == "1") {
            console.log("auto play");
            AutoGame.instance.autoFlag = true;
        } else {
            console.log("play");
            AutoGame.instance.autoFlag = false;
        }
        if (AutoGame.instance.autoGameVer) {
            if (this.enterLevelNum.node.active && this.enterLevelNum.string.length > 0) {
                let startLevelNum = parseInt(this.enterLevelNum.string);
                RuntimeData._ins.passTotalLevelForRankingList = startLevelNum - 1;
            }
        }
        AudioManager.playSound("gz", 0.5);
        //AudioManager.playLongSound("homeBobao",0.5);
        director.preloadScene(sceneNamesEnum.吃大席场景, () => {
            director.loadScene(sceneNamesEnum.吃大席场景);
        })
    }
    clickEatCake: boolean = false;
    eatCakeBtnFunc() {
        if (this.clickEatCake) return;
        this.clickEatCake = true;
        director.preloadScene(sceneNamesEnum.吃甜品, () => {
            director.loadScene(sceneNamesEnum.吃甜品);
        })
    }
    startSpecialDishLevelBtn() {
        console.log("开始特色菜关卡");
        let enterSpecialDishLevel = () => {
            if (!RuntimeData._ins.allowEnterSpecialLevel) return;
            DailyRefresh.recordBehaviorNum(DailyRefreshItems.specialLevelFreeCount, 1, true);
            RuntimeData._ins.allowEnterSpecialLevel = false;
            // 进入特殊菜关卡
            RuntimeData._ins.isSpecialDishLevel = true;
            director.preloadScene(sceneNamesEnum.吃大席场景, () => {
                director.loadScene(sceneNamesEnum.吃大席场景);
            });
        }
        if (!RuntimeData._ins.allowEnterSpecialLevel) {
            AdManager.showVideoAd(
                () => {
                    // error("播放视频激励广告成功");
                    // console.log("播放一次特色菜关卡的广告");
                    RuntimeData._ins.watchSpecialLevelVideoAdCount += 1;
                    this.updateSpecialLevelBtn();
                    if (RuntimeData._ins.allowEnterSpecialLevel)
                        enterSpecialDishLevel();
                },
                () => {
                    // error("播放视频激励广告失败");
                }
            )
            return;
        };

        // console.log("直接免费进入");
        enterSpecialDishLevel();
    }

    openHandbookUI() {
        UIManager.instance.showUI(UINamesEnum.handbookUIVer2);
    }

    openRankingListUI() {
        console.log("打开排行榜");
        AdManager.showRankList();
    }

    openGoToSidebarUI() {
        console.log("引导侧边栏");
        UIManager.instance.showUI(UINamesEnum.guideSidebar);
    }

    openAddTableUI() {
        if (AutoGame.instance.autoGameVer) {
            // 自动档模式下，切换是否展示开始自动游戏按钮
            this.enterLevelNum.node.active = !this.enterLevelNum.node.active;
            this.autoStartBtnNode.active = !this.autoStartBtnNode.active;

        } else {
            console.log("添加桌面");
            // UIManager.instance.showUI(UINamesEnum.guideAddTable);
            AdManager_ZJ._ins.addTable(
                () => {
                    console.log("添加桌面成功");
                }
            );
        }
    }

    openShareGameUI() {
        console.log("分享游戏");
        AdManager.shareGame("吃大席");
    }

    openAchievementUI() {
        console.log("成就");
        UIManager.instance.showUI(UINamesEnum.achievementUI);
    }

    openInvitation() {
        // console.log("打开请帖");
        let invitation = Tools.newPrefab(prefabNameEnums.请帖, this.node.getChildByName("UILayer"));
        invitation.setPosition(0, -1000);
        invitation.setScale(0.2, 0.2);
        tween(invitation)
            .to(0.34, { position: v3(0, 0), scale: v3(1, 1, 1) }, { easing: easing.backOut })
            .call(() => {
                invitation.getComponent(Invitation).open();
            })
            .start();
    }

    /** 更新开始游戏按钮的关卡数等数据展示 */
    updateStartGameBtn() {
        let levelNum = RuntimeData._ins.curLevel;

        let levelNumLabelNode = this.startBtn.getChildByName("CurDay").getChildByName("Num");
        levelNumLabelNode.getComponent(Label).string = levelNum.toString();

        let 开席天数LabelCom = this.startBtn.getChildByName("Invitation").getChildByName("DayCount").getComponent(Label);
        开席天数LabelCom.string = RuntimeData._ins.targetLevel.toString();
        this.levelLabelCom.string = (RuntimeData._ins.passTotalLevelForRankingList + 1).toString();
    }

    /** 更新特殊关开始按钮 */
    updateSpecialLevelBtn() {
        let flag = RuntimeData._ins.allowEnterSpecialLevel;
        let lockIcon = this.specialDishLevelBtn.getChildByName("Description").getChildByName("LockIcon");

        if (flag) {
            this.specialDishLevelBtn.getChildByName("FreeIcon").active = true;
            this.specialDishLevelBtn.getChildByName("AdIcon").active = false;
            lockIcon.active = false;
        } else {
            lockIcon.active = true;
            this.specialDishLevelBtn.getChildByName("FreeIcon").active = false;
            this.specialDishLevelBtn.getChildByName("AdIcon").active = true;
            this.specialDishLevelBtn.getChildByName("AdIcon").getComponent(Label).string = `看${2 - RuntimeData._ins.watchSpecialLevelVideoAdCount}次广告解锁`;
            let curNum = RuntimeData._ins.watchSpecialLevelVideoAdCount;
            let targetCount = 2;
            let adIconNode = this.specialDishLevelBtn.getChildByName("AdIcon");
            let watchCountNode = adIconNode.getChildByName("WatchCount");
            Tools.setSpriteFrame(watchCountNode, `${curNum}_specialLevelBtn`);
            let targetWatchCountNode = adIconNode.getChildByName("TargetWatchCount");
            Tools.setSpriteFrame(targetWatchCountNode, `${targetCount}_specialLevelBtn`);
        }
    }

    protected onEnable(): void {

    }
    protected onDisable(): void {

    }

    private onProgress(finished: number, total: number): void {
        // console.log(`加载进度：${finished}/${total}`);
        this.loadingUI.updateProgress(finished, total);
    }

    private onComplete(err: Error, assets: Asset[]): void {
        if (err) {
            error('[HOME_Scene] 加载bundle资源失败:', err);
            return;
        }

        if (assets?.length > 0) {
            console.log(sceneNamesEnum.主页场景 + '资源加载完成');
            assets.forEach(asset => {
                if (asset?.name) {
                    ResManager.instance.setAsset(asset.name, asset);
                }
            });
        }

        this.startCallback();
    }

}


