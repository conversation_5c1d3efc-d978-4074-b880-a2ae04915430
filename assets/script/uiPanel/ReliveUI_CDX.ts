import { _decorator, Component, easing, Label, Node, Touch, tween, v3 } from 'cc';
import { PageActive, UIManager } from '../manager/UIManager';
import { UINamesEnum } from './UIConfig';
import { MainGame_CDX } from '../game/chiDaXi/MainGame_CDX';
import { AdManager } from '../ads/AdManager';
import { LevelData_CDX } from '../game/chiDaXi/data/LevelData_CDX';
import { TableManager } from '../game/chiDaXi/table/TableManager';
import { RuntimeData } from '../game/chiDaXi/data/GameData';
import { AutoGame } from '../common/AutoGame';
import { GameModel } from '../model/GameModel';
import { releaseType } from '../Enum/Enum';
import { DailyRefresh, DailyRefreshItems } from '../common/DailyRefresh';
const { ccclass, property } = _decorator;

@ccclass('ReliveUI_CDX')
export class ReliveUI_CDX extends Component implements PageActive {
    rootNode: Node;
    exitBtnNode: Node;

    gameOverCb: Function;

    protected onLoad(): void {
        this.rootNode = this.node.getChildByName("Root");

        // 自动游戏 注册复活逻辑
        if (AutoGame.instance.autoFlag) {
            this.scheduleOnce(() => {
                AutoGame.instance.reliveGame(this.noBtn.bind(this));
            }, 3);
        }
    }

    /** 点击关闭按钮 */
    noBtn(...data: any) {
        let delayTime = 0;
        if (AutoGame.instance.autoFlag) {
            this.exitBtnNode = this.rootNode.getChildByName("Btns").getChildByName("NoBtn");
            tween(this.exitBtnNode)
                .by(0.23, { scale: v3(-0.1, -0.1, 0) }, { easing: easing.sineOut })
                .by(0.12, { scale: v3(0.1, 0.1, 0) }, { easing: easing.sineIn })
                .start();
            delayTime = 0.5;
        }

        this.scheduleOnce(() => {
            this.closeAnim(() => {
                UIManager.instance.hideUI(UINamesEnum.吃大席复活页面);
                this.gameOverCb && this.gameOverCb();
            });
        }, delayTime);
    }

    /** 复活 */
    yesBtn(touch: Touch, data: string) {
        if (data == "1") {
            AdManager.showVideoAd(
                () => {
                    this.closeAnim(() => {
                        UIManager.instance.hideUI(UINamesEnum.吃大席复活页面);
                        MainGame_CDX._ins.relive();
                    });
                },
                () => {
                    console.error("播放失败");
                }
            )
        } else {
            AdManager.shareGame(
                "吃大席",
                () => {
                    this.closeAnim(() => {
                        UIManager.instance.hideUI(UINamesEnum.吃大席复活页面);
                        MainGame_CDX._ins.relive();
                    });
                }
            )
        }
    }

    /** 关闭动作 */
    private closeAnim(callBack?: Function) {
        let rootNode = this.rootNode;
        tween(rootNode)
            .to(0.23, { scale: v3(0.1, 0.1, 0.1) }, { easing: easing.backIn })
            .call(() => {
                callBack && callBack();
            })
            .start();
    }

    show(...args: any[]): void {
        let [gameOverCb] = args;
        this.gameOverCb = gameOverCb;

        // 每天第一次 是分享免费复活，否则是视频复活
        let showReliveUITimes = DailyRefresh.recordBehaviorNum(DailyRefreshItems.showReliveCount, 0, true);

        let showVideoAd = showReliveUITimes > 1; // || GameModel.instance.releaseType === releaseType.applet_wechat;
        if (showVideoAd) {
            this.rootNode = this.node.getChildByName("Root");
            this.node.getChildByName("Root_ShareGame").active = false;
        } else {
            this.rootNode = this.node.getChildByName("Root_ShareGame");
            this.node.getChildByName("Root").active = false;
        }
        this.rootNode.active = true;
        let rootNode = this.rootNode;

        let scale = rootNode.getScale();
        rootNode.scale = v3(0, 0, 0);
        rootNode.setPosition(rootNode.position.x, rootNode.position.y + 500);
        tween(rootNode)
            .by(0.3, { scale, position: v3(0, -500, 0) }, { easing: easing.backOut })
            .start();

        let surplusCountNode = this.rootNode.getChildByName("TableSurplusCount");
        let surplusCountLabel = surplusCountNode.getComponentInChildren(Label);
        surplusCountLabel.string = RuntimeData._ins.curShowingSurplusTableCount.toString();

        // 剩余数量，数字呼吸动作
        let animTime = 0.56;
        let animScale = 0.3;
        tween(surplusCountLabel.node)
            .tag(1)
            .by(animTime / 4, { scale: v3(animScale, animScale) }, { easing: easing.sineIn })
            .by(animTime / 4, { scale: v3(-animScale, -animScale) }, { easing: easing.sineInOut })
            .by(animTime / 4, { scale: v3(animScale, animScale) }, { easing: easing.sineIn })
            .by(animTime / 4, { scale: v3(-animScale, -animScale) }, { easing: easing.sineInOut })
            .delay(0.78)
            .union()
            .repeatForever()
            .start();
    }

    hide(): void {
    }
    enableBtn(): void {
    }
    disableBtn(): void {
    }
}


