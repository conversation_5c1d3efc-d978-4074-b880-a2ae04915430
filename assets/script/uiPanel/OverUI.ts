import { _decorator, Component, Node, Button, Sprite, tween, UIOpacity, Vec3, director } from 'cc';
import { PageActive, UIManager } from '../manager/UIManager';
import { sceneNamesEnum } from '../game/chiDaXi/Index';
const { ccclass, property } = _decorator;

@ccclass('OverUI')
export class OverUI extends Component implements PageActive {
    @property(Node)
    black: Node = null;
    @property(Node)
    panel: Node = null;
    @property(Button)
    againBtn: Button = null;
    @property(Button)
    homeBtn: Button = null;

    onLoad() {

    }

    start() {

    }

    show(): void {
        this.disableBtn();
        const blackUIOpacity = this.black.getComponent(UIOpacity)!;
        blackUIOpacity.opacity = 0;
        tween(blackUIOpacity)
            .to(0.2, { opacity: 150 })
            .start()

        this.panel.setScale(Vec3.ZERO);
        tween(this.panel)
            .to(0.2, { scale: Vec3.ONE }, { easing: "backOut" })
            .start()

        this.againBtn.node.setScale(Vec3.ZERO);
        tween(this.againBtn.node)
            .delay(0.1)
            .to(0.2, { scale: Vec3.ONE }, { easing: "backOut" })
            .call(() => {
                this.enableBtn()
            })
            .start()
        this.homeBtn.node.setScale(Vec3.ZERO);
        tween(this.homeBtn.node)
            .delay(0.1)
            .to(0.2, { scale: Vec3.ONE }, { easing: "backOut" })
            .call(() => {
                this.enableBtn()
            })
            .start()
    }
    hide(): void {
    }
    enableBtn(): void {
        this.againBtn.interactable = true;
        this.homeBtn.interactable = true;
    }
    disableBtn(): void {
        this.againBtn.interactable = false;
        this.homeBtn.interactable = false;
    }

    againBtnClick() {
        director.loadScene(sceneNamesEnum.吃大席场景);
        UIManager.instance.hideUI("overUI");
    }

    homeBtnClick() {
        director.loadScene(sceneNamesEnum.主页场景);
        UIManager.instance.hideUI("overUI");
    }

    update(deltaTime: number) {

    }
}


