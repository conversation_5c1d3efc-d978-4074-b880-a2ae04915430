import { _decorator, Component, Node, Button, Sprite, tween, UIOpacity, Vec3, director, sp, v3 } from 'cc';
import { PageActive, UIManager } from '../manager/UIManager';
import { sceneNamesEnum } from '../game/chiDaXi/Index';
import { AdManager } from '../ads/AdManager';
import { UINamesEnum } from './UIConfig';
import { RuntimeData } from '../game/chiDaXi/data/GameData';
import { Particle } from '../game/particle/Particle';
const { ccclass, property } = _decorator;

@ccclass('SuccessUI_CDX')
export class SuccessUI_CDX extends Component implements PageActive {
    @property(Node)
    black: Node = null;
    @property(Node)
    panel: Node = null;
    // @property(Button)
    // againBtn: Button = null;
    // @property(Button)
    // homeBtn: Button = null;

    onLoad() {

    }

    start() {
        // 目标关卡等于0，代表收集成功一道大席菜，屏蔽掉继续游戏按钮
        // if(RuntimeData._ins.targetLevel == 0){
        //     this.againBtn.node.active = false;
        // }

        let spineComs = this.node.getComponentsInChildren(sp.Skeleton);
        let randomIndex = Math.floor(Math.random() * spineComs.length);
        randomIndex = 1;
        let spineCom = spineComs[randomIndex];
        spineCom.node.active = true;
        let trackEntry = spineCom.setAnimation(0, "animation", false);
        spineCom.timeScale = 1.34;
        spineCom.setTrackCompleteListener(trackEntry, ()=>{
            this.scheduleOnce(() => {
                if (RuntimeData._ins.targetLevel == 0) {
                    console.log("收集一道菜成功（通关一道菜），返回主页展示战利品");
                    director.loadScene(sceneNamesEnum.主页场景);
                } else {
                    console.log("继续闯关");
                    director.loadScene(sceneNamesEnum.吃大席场景);
                }
                spineCom.node.active = false;
                UIManager.instance.hideUI(UINamesEnum.吃大席成功页面);
            }, 0.23);
        })

        // Particle.bombRibbon(this.node, v3(0, 330), 79);
        this.scheduleOnce(() => {
            Particle.successRibbon(this.node, v3(0, 330), 79);
        }, 0.23);
    }

    show(): void {
        this.disableBtn();
        const blackUIOpacity = this.black.getComponent(UIOpacity)!;
        blackUIOpacity.opacity = 0;
        tween(blackUIOpacity)
            .to(0.2, { opacity: 150 })
            .start()

        this.panel.setScale(Vec3.ZERO);
        tween(this.panel)
            .to(0.2, { scale: Vec3.ONE }, { easing: "backOut" })
            .start()

        // this.againBtn.node.setScale(Vec3.ZERO);
        // tween(this.againBtn.node)
        //     .delay(0.1)
        //     .to(0.2, { scale: Vec3.ONE }, { easing: "backOut" })
        //     .call(() => {
        //         this.enableBtn()
        //     })
        //     .start()
        // this.homeBtn.node.setScale(Vec3.ZERO);
        // tween(this.homeBtn.node)
        //     .delay(0.1)
        //     .to(0.2, { scale: Vec3.ONE }, { easing: "backOut" })
        //     .call(() => {
        //         this.enableBtn()
        //     })
        //     .start()
    }
    hide(): void {
    }
    enableBtn(): void {
        // this.againBtn.interactable = true;
        // this.homeBtn.interactable = true;
    }
    disableBtn(): void {
        // this.againBtn.interactable = false;
        // this.homeBtn.interactable = false;
    }

    // againBtnClick() {
    //     director.loadScene(sceneNamesEnum.吃大席场景);
    //     UIManager.instance.hideUI(UINamesEnum.吃大席成功页面);
    // }

    // homeBtnClick() {
    //     director.loadScene(sceneNamesEnum.主页场景);
    //     UIManager.instance.hideUI(UINamesEnum.吃大席成功页面);
    // }

    // shareGameBtn(){
    //     AdManager.shareGame("吃大席");
    // }

    // shareVideoBtn(){
    //     console.log("分享录屏");
    //     // AdManager.shareVideo();
    // }

    update(deltaTime: number) {

    }
}


