import { _decorator, Component, Node, Button, Sprite, tween, UIOpacity, Vec3, director, v3, easing, UITransform } from 'cc';
import { PageActive, UIManager } from '../manager/UIManager';
import { sceneNamesEnum } from '../game/chiDaXi/Index';
import { AdManager } from '../ads/AdManager';
import { UINamesEnum } from './UIConfig';
import { AudioManager } from '../manager/AudioManager';
import { AutoGame } from '../common/AutoGame';
import { Tools } from '../common/Tools';
import { dialog } from 'electron';
const { ccclass, property } = _decorator;

@ccclass('OverUI_CDX')
export class OverUI_CDX extends Component implements PageActive {
    @property(Node)
    black: Node = null;
    @property(Node)
    panel: Node = null;
    @property(Button)
    againBtn: Button = null;
    @property(Button)
    homeBtn: Button = null;
    @property(Node)
    people: Node = null;
    @property([Node])
    dialogs: Node[] = [];
    @property(Node)
    title: Node = null;
    @property(Node)
    shareVideoBtnNode: Node = null;
    @property(Node)
    shareGameBtnNode: Node = null;

    onLoad() {

    }

    start() {
        // 自动游戏 注册结束逻辑
        if(AutoGame.instance.autoFlag){
            this.scheduleOnce(() => {
                AutoGame.instance.overGame(this.againBtnClick.bind(this));
            }, 3);
        }
    }

    show(): void {
        console.log("结束界面 show");

        // 初始化角色形象
        this.people.children.forEach(charactor=>{
            let imgName = charactor.name == "Left" ? "overPeople_10" : "overPeople_11";

            Tools.setSpriteFrame(charactor, imgName + Math.ceil(Math.random() * 2));
        });
        // 初始化对话框
        this.dialogs.forEach((dialog, index)=>{
            let imgName: string;
            let id = 1;
            if (dialog.name == "Left") {
                id = 1;
                imgName = "overDialog_10";
            } else {
                imgName = "overDialog_11";
                id = Math.ceil(Math.random() * 3);
            } 
            Tools.setSpriteFrame(dialog, imgName + id);

            // 设置位置
            let charactor = this.people.children.find(val=>val.name == dialog.name);
            let charactorHeight = charactor.getComponent(UITransform).height;
            dialog.setPosition(dialog.position.x, charactor.position.y + charactorHeight + 212);
        });

        // this.disableBtn();
        const blackUIOpacity = this.black.getComponent(UIOpacity)!;
        blackUIOpacity.opacity = 0;
        tween(blackUIOpacity)
            .to(0.2, { opacity: 200 })
            .start();

        // this.panel.setScale(Vec3.ZERO);
        // tween(this.panel)
        //     .to(0.2, { scale: Vec3.ONE }, { easing: "backOut" })
        //     .start();

        let animNodes = [[this.title], [this.people], [this.dialogs[0]], [this.dialogs[1]], [this.againBtn.node], [this.homeBtn.node], [this.shareVideoBtnNode, this.shareGameBtnNode]];
        let showTime = 0.18;
        let time = 0;
        for(let i = 0; i < animNodes.length; i++){
            let deltaTime = time;
            let tempArr = animNodes[i];
            tempArr.forEach((animNode)=>{
                let initScale = animNode.scale.x;
                animNode.setScale(0, 0);
                tween(animNode)
                    .delay(deltaTime)
                    .to(showTime, { scale:v3(initScale, initScale, initScale) }, { easing: easing.backOut })
                    .start();
            });
            time += showTime * 2.5 / 4;
        }
        this.scheduleOnce(()=>{
            this.enableBtn();

            // // 分享按钮呼吸动作
            // let animTime = 0.56;
            // let animScale = 0.3;
            // tween(this.shareVideoBtnNode)
            //     .tag(1)
            //     .by(animTime / 4, { scale: v3(animScale, animScale, 1) }, { easing: easing.sineIn })
            //     .by(animTime / 4, { scale: v3(-animScale, -animScale, 1) }, { easing: easing.sineInOut })
            //     .by(animTime / 4, { scale: v3(animScale, animScale, 1) }, { easing: easing.sineIn })
            //     .by(animTime / 4, { scale: v3(-animScale, -animScale, 1) }, { easing: easing.sineInOut })
            //     .call(() => {
            //         this.shareVideoBtnNode.setScale(1, 1, 1);
            //     })
            //     .delay(0.78)
            //     .union()
            //     .repeatForever()
            //     .start();
        }, time);
    }
    hide(): void {
    }
    enableBtn(): void {
        this.againBtn.interactable = true;
        this.homeBtn.interactable = true;
    }
    disableBtn(): void {
        this.againBtn.interactable = false;
        this.homeBtn.interactable = false;
    }

    againBtnClick() {
        let delayTime = 0;
        if (AutoGame.instance.autoFlag) {
            tween(this.againBtn.node)
                .by(0.23, { scale: v3(-0.1, -0.1, 0) }, { easing: easing.sineOut })
                .by(0.12, { scale: v3(0.1, 0.1, 0) }, { easing: easing.sineIn })
                .start();
            delayTime = 0.5;
        }

        this.scheduleOnce(() => {
            director.loadScene(sceneNamesEnum.吃大席场景);
            UIManager.instance.hideUI(UINamesEnum.吃大席结束页面);
        }, delayTime);
    }

    homeBtnClick() {
        director.loadScene(sceneNamesEnum.主页场景);
        UIManager.instance.hideUI(UINamesEnum.吃大席结束页面);
    }

    shareGameBtn(){
        AdManager.shareGame("吃大席");
    }

    shareVideoBtn(){
        console.log("分享录屏");
        AdManager.shareGameVideo();
    }

    update(deltaTime: number) {

    }
}


