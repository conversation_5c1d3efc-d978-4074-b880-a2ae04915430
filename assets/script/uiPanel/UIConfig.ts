type UIConfig = {
    /**ui预制体名称 */
    name: string,
    /**ui所在的bundle包名称 */
    bundleName: string,
    /**ui在bundle下的路径 */
    bundlePath: string,
    /**是否自动释放资源 */
    isAutoRelease: boolean,
}

export enum UINamesEnum {
    overUI = "OverUI",
    /** 图鉴UI */
    handbookUI = "handbookUI",
    handbookUIVer2 = "handbookUIVer2",
    /** 设置UI */
    settingUI = "settingUI",
    /** 全局UI */
    globalUI = "globalUI",
    /** 引导侧边栏 */
    guideSidebar = "guideSidebarUI",
    /** 引导添加桌面 */
    guideAddTable = "guideAddTableUI",
    /** 成就系统界面 */
    achievementUI = "achievementUI",
    //#region 吃大席游戏场景内的
    /** 复活页面 */
    吃大席复活页面 = "reliveUI_CDX",
    吃大席结束页面 = "overUI_CDX",
    吃大席成功页面 = "successUI_CDX",
    //#endregion
    /** 加载页面 */
    全局加载页 = "loadingUI"
}

export const UIConfigTable: { [k: string]: UIConfig } = {
    overUI: { name: "OverUI", bundleName: "uiBundle", bundlePath: "prefab/OverUI", isAutoRelease: true },
    handbookUI: { name: "HandbookUI", bundleName: "homeBundle", bundlePath: `prefab/handbook/HandbookUI`, isAutoRelease: false },
    handbookUIVer2: { name: "HandbookUIVer2", bundleName: "homeBundle", bundlePath: `prefab/handbookVer2/HandbookUIVer2`, isAutoRelease: false },
    guideSidebarUI: { name: "GuideSidebarUI", bundleName: "homeBundle", bundlePath: `prefab/GuideSidebarUI`, isAutoRelease: false },
    guideAddTableUI: { name: "GuideAddTableUI", bundleName: "homeBundle", bundlePath: `prefab/GuideAddTableUI`, isAutoRelease: false },
    achievementUI: { name: "AchievementUI", bundleName: "homeBundle", bundlePath: `prefab/achievement/AchievementUI`, isAutoRelease: false },
    settingUI: { name: "SettingUI", bundleName: "uiBundle", bundlePath: `prefab/SettingUI`, isAutoRelease: false },
    globalUI: { name: "GlobalUI", bundleName: "uiBundle", bundlePath: `prefab/GlobalUI`, isAutoRelease: false },
    loadingUI: { name: "LoadingUI", bundleName: "uiBundle", bundlePath: `prefab/LoadingUI`, isAutoRelease: true },
    reliveUI_CDX: { name: "ReliveUI_CDX", bundleName: "mainGameBundle", bundlePath: "prefab/ReliveUI_CDX", isAutoRelease: true },
    overUI_CDX: { name: "OverUI_CDX", bundleName: "uiBundle", bundlePath: "prefab/OverUI_CDX", isAutoRelease: true },
    successUI_CDX: { name: "SuccessUI_CDX", bundleName: "uiBundle", bundlePath: "prefab/SuccessUI_CDX", isAutoRelease: true },
}