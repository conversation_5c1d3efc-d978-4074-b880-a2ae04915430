import { _decorator, Node, instantiate, NodePool, Tween, Vec3 } from 'cc';
import { ResManager } from './ResManager';
const { ccclass, property } = _decorator;

/**对象池管理类，管理游戏内依赖到的预制体资源 */
@ccclass('PoolManager')
export class PoolManager {
    /**对象池管理器 */
    private static poolHandle = new Map<string, NodePool>();
    private constructor() { }

    /**
     * 根据预制体名称从对象池中取出一个对象
     * @param name 对象名称
     */
    public static Spawn(name: string, parent?: Node, pos?: Vec3, zIndex?: number): Node {
        if (!this.poolHandle.has(name)) {
            const prefab = ResManager.instance.getPrefab(name);
            if (!prefab) {
                console.error("没有该预制体：" + name);
                return;
            }
            console.log("创建了对象池：" + name);
            const pool = new NodePool(name);
            this.poolHandle.set(name, pool);
        }
        const pool = this.poolHandle.get(name);
        let node = null;
        if (pool.size() <= 0) {
            node = instantiate(ResManager.instance.getPrefab(name));
            pool.put(node);
        }
        node = pool.get();
        node.active = true;
        if (parent) {
            if (zIndex) {
                parent.insertChild(node, zIndex);
            }
            else {
                parent.addChild(node);
            }
        }
        if (pos) {
            node.setPosition(pos);
        }
        return node;
    }

    /**
     * 回收一个对象
     * @param name 对象名称
     * @param node 对象节点
     */
    public static Despawn(node: Node): void {
        const name = node.name;
        if (!this.poolHandle.has(name)) {
            console.error("没有该预制体：" + name);
            return;
        }
        Tween.stopAllByTarget(node);
        node.active = false;
        const pool = this.poolHandle.get(name);
        pool.put(node);
    }

    /**
     * 删除指定对象池
     * @param name 对象池名字
     * @returns 
     */
    public static Delete(name: string): void {
        if (!this.poolHandle.has(name)) {
            console.error("prefab not exist：" + name);
            return;
        }
        this.poolHandle.delete(name);
        console.error("对象池已销毁：" + name);
    }
}