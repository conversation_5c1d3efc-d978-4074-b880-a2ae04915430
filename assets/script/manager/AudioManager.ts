
import { _decorator, Node, AudioSource, AudioClip, director } from 'cc';
import { Util } from '../common/Util';
import { GameModel } from '../model/GameModel';
import { ResManager } from './ResManager';
const { ccclass, property } = _decorator;

@ccclass('AudioManager')
export class AudioManager {
    private static _bgmAudioSource?: AudioSource;
    private static _soundAudioSource?: AudioSource;
    private static _cachedAudioClipMap: Record<string, AudioClip> = {};

    public static init() {
        console.log('Init AudioManager!');

        ResManager.instance.loadBundle("audioBundle");
        const node1 = new Node();
        node1.name = "bgmAudio";
        director.getScene().addChild(node1);
        director.addPersistRootNode(node1);
        AudioManager._bgmAudioSource = node1.addComponent(AudioSource);
        AudioManager._bgmAudioSource.loop = true;
        AudioManager._bgmAudioSource.playOnAwake = false;

        const node2 = new Node();
        node2.name = "soundAudio";
        director.getScene().addChild(node2);
        director.addPersistRootNode(node2);
        AudioManager._soundAudioSource = node2.addComponent(AudioSource);
        AudioManager._soundAudioSource.loop = false;
        AudioManager._soundAudioSource.playOnAwake = false;
    }

    public static addAudio(name: string, audio: AudioClip) {
        if (this._cachedAudioClipMap[name]) {
            console.log("audio already exists: " + name);
        }
        else {
            this._cachedAudioClipMap[name] = audio;
        }
    }

    /**播放背景音乐 */
    public static playMusic(bgmName: string, volum: number = 0.5) {
        if (!GameModel.instance.musicFlag) {
            return;
        }
        this.stopMusic();
        const audioSource = this._bgmAudioSource!;
        audioSource.loop = true;
        let cachedAudioClip = this._cachedAudioClipMap[bgmName];
        if (cachedAudioClip) {
            audioSource.clip = cachedAudioClip;
            audioSource.play();
        }
        else {
            ResManager.instance.loadAudio(bgmName, (err: Error, audio: AudioClip) => {
                if (err) {
                    console.warn(err);
                    return;
                }

                this._cachedAudioClipMap[bgmName] = audio;
                audioSource.clip = audio;
                audioSource.play();
                audioSource.volume = volum;
            });
        }
    }

    /**暂停播放背景音乐 */
    public static pauseMusic() {
        const audioSource = this._bgmAudioSource!;
        audioSource.pause();
    }

    /** 恢复播放背景音乐 */
    public static resumeMusic() {
        const audioSource = this._bgmAudioSource!;
        audioSource.play();
    }

    /**停止播放背景音乐 */
    public static stopMusic() {
        const audioSource = this._bgmAudioSource!;
        audioSource.stop();
    }

    /**播放长音效 */
    public static playLongSound(longSoundName: string, volum: number) {
        if (!GameModel.instance.soundFlag) {
            return;
        }

        const audioSource = this._soundAudioSource!;
        let cachedAudioClip = this._cachedAudioClipMap[longSoundName];
        if (cachedAudioClip) {
            audioSource.clip = cachedAudioClip;
            audioSource.volume = volum;
            audioSource.play();
        }
        else {
            ResManager.instance.loadAudio(longSoundName, (err: Error, audio: AudioClip) => {
                if (err) {
                    console.warn(err);
                    return;
                }

                this._cachedAudioClipMap[longSoundName] = audio;
                audioSource.clip = audio;
                audioSource.volume = volum;
                audioSource.play();
            });
        }
    }

    /**暂停播放长音效 */
    public static pauseLongSound() {
        const audioSource = this._soundAudioSource!;
        audioSource.pause();
    }

    /**停止播放长音效 */
    public static stopLongSound() {
        const audioSource = this._soundAudioSource!;
        audioSource.stop();
    }

    /**
     * 播放指定音效
     * @param name 音效名
     * @param volum 音量
     */
    public static playSound(name: string, volum: number) {
        if (!GameModel.instance.soundFlag) {
            return;
        }
        const audioSource = this._soundAudioSource!;

        let cachedAudioClip = this._cachedAudioClipMap[name];
        if (cachedAudioClip) {
            audioSource.playOneShot(cachedAudioClip, volum);
        }
        else {
            ResManager.instance.loadAudio(name, (err: Error, audio: AudioClip) => {
                if (err) {
                    console.warn(err);
                    return;
                }

                this._cachedAudioClipMap[name] = audio;
                audioSource.playOneShot(audio, volum);
            })
        }
    }
}
