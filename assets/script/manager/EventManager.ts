import Singleton from "../base/Singleton";
export default class EventManager extends Singleton {
   
    private eventMap: Map<string, Array<{ callback: Function, target: any }>> = new Map();
    static get instance() {
        return super.getInstance<EventManager>()
    }
    
    public on(eventName: string, callback: Function, target: any): void {
        let handlers = this.eventMap.get(eventName);

        if (!handlers) {
            handlers = [];
            this.eventMap.set(eventName, handlers);
        }

        handlers.push({ callback, target });
    }

    /**
     * 触发指定事件
     * @param eventName 事件名称
     * @param args 传递给回调函数的参数
     */
    public emit(eventName: string, ...args: any[]): void {
        const handlers = this.eventMap.get(eventName);

        if (handlers) {
            handlers.forEach(handler => {
                if (handler.target) {
                    handler.callback.apply(handler.target, args);
                } else {
                    handler.callback(...args);
                }
            });
        }
    }

    public off(eventName: string, callback: Function, target: any): void {
        const handlers = this.eventMap.get(eventName);

        if (handlers) {
            const index = handlers.findIndex(handler =>
                handler.callback === callback && handler.target === target
            );

            if (index !== -1) {
                handlers.splice(index, 1);
            }
        }
    }
}