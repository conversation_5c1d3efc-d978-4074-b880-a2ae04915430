import { _decorator, Asset, AssetManager, assetManager, AudioClip, Component, instantiate, js, JsonAsset, Node, Prefab, SpriteFrame } from 'cc';
import Singleton from '../base/Singleton';
const { ccclass, property } = _decorator;

@ccclass('ResManager')
export class ResManager extends Singleton {

    //==================== 私有属性 ====================
    private _assetsCache: Map<string, Asset> = new Map<string, Asset>();


    //==================== 属性访问器 ====================
    public get assetsCache(): Map<string, Asset> { return this._assetsCache; }
    //==================== 单例相关 ====================
    static get instance() {
        return super.getInstance<ResManager>()
    }
    //====================== 方法 ======================

    /**存储资源
     * @param key 资源键
     * @param asset 资源
     */
    public setAsset(key: string, asset: Asset, bundleName?: string): void {
        const assetType = js.getClassName(asset);
        let cacheKey = `${key}:${assetType}`;
        if (bundleName) cacheKey += `:${bundleName}`;
        this._assetsCache.set(cacheKey, asset);
    }
    //移除资源
    public removeAsset<T extends typeof Asset>(key: string, type: T, bundleName?: string): void {
        const assetType = js.getClassName(type);
        let cacheKey = `${key}:${assetType}`;
        if (bundleName) cacheKey += `:${bundleName}`;
        this._assetsCache.delete(cacheKey);
    }
    /**清空资源缓存 */
    public clearAssetsCache(): void {
        this._assetsCache.clear();
    }
    /**加载bundle
   * @param bundleName bundle名称
   */
    public loadBundle(bundleName: string): Promise<AssetManager.Bundle> {
        return new Promise((resolve, reject) => {
            assetManager.loadBundle(bundleName, (err: Error, bundle: AssetManager.Bundle) => {
                if (err) {
                    console.error('[LoadScene] 加载+bundleName+失败:', err);
                    reject(err);
                    return;
                }
                console.log(bundle)
                resolve(bundle);
            });
        });
    }

    /**加载bundle种指定文件夹所有资源 
     * @param bundle bundle对象
     * @param path 文件夹路径(""是在遍历bundle根目录)
     * @param onProgress 加载进度回调
     * @param onComplete 加载完成回调
    */
    public bundelLoadDir(bundleName: string, path: string, onProgress: (finish: number, total: number) => void, onComplete: (err: Error, assets: Asset[]) => void): void {

        const bundle = assetManager.getBundle(bundleName);
        if (!bundle) {
            console.warn(`未能找到bundle: ${bundleName}`);
            return;
        }
        bundle.loadDir(path,
            (finished: number, total: number) => {
                onProgress(finished, total);
            },
            (err: Error, assets: Asset[]) => {
                onComplete(err, assets);
            }
        );
    }

    /**加载bundle中指定资源 
     * @param bundle bundle对象
     * @param path 资源路径
     * @param type 资源类型
     * @param onComplete 加载完成回调
    */
    public bundelLoad<T extends Asset>(bundleName: string, path: string, onComplete: (err: Error, asset: T) => void): void {
        const bundle = assetManager.getBundle(bundleName);
        if (!bundle) {
            console.warn(`未能找到bundle: ${bundleName}`);
            return;
        }
        bundle.load(path, (err: Error, asset: T) => {
            onComplete(err, asset);
        })
    }

    /**
     * 从bundle包中加载ui页面
     * @param uiName 预制体名称
     * @param onComplete 加载完成回调
     */
    public loadUI(bundleName: string, bundlePath: string, onComplete: (err: Error, prefab: Prefab) => void): void {
        this.bundelLoad(bundleName, bundlePath, (err: Error, prefab: Prefab) => {
            if (err) {
                console.log("ui not exist: " + bundlePath);
            }
            else {
                onComplete(err, prefab);
            }
        })
    }

    public loadAudio(audioName: string, onComplete: (err: Error, audio: AudioClip) => void): void {
        this.bundelLoad("audioBundle", audioName, (err: Error, audio: AudioClip) => {
            if (err) {
                console.log("audio not exist: " + audioName);
            }
            else {
                onComplete(err, audio);
            }
        })
    }

    /**
     * 移除bundle中指定资源
     * @param bundleName bundle名称
     * @param path 资源路径
     * @returns 
     */
    removeBundleAssets(bundleName: string, path: string) {
        const bundle = assetManager.getBundle(bundleName);
        if (!bundle) {
            console.warn(`Bundle ${bundleName} not found.`);
            return;
        }
        bundle.release(path)
    }
    /**
     * 清除bundle中所有资源
     * @param bundleName bundle名称
     * @returns 
     */
    clearBundleAssets(bundleName: string) {
        const bundle = assetManager.getBundle(bundleName);
        if (!bundle) {
            console.warn(`Bundle ${bundleName} not found.`);
            return;
        }
        bundle.releaseAll();
    }
    /**获取资源
     * @param path 资源路径
     * @param type 资源类型
     * @returns 资源
     */
    public getAsset<T extends typeof Asset>(path: string, type: T, bundleName?: string): InstanceType<T> | null {
        const assetType = js.getClassName(type);
        let cacheKey = `${path}:${assetType}`;
        if (bundleName) cacheKey += `:${bundleName}`;

        let cachedAsset: Asset;
        let keys = this._assetsCache.keys();
        for (let key of keys) {
            let [tempPath, tempType] = key.split(":");
            if (tempPath == path && tempType == `${assetType}`) {
                cachedAsset = this._assetsCache.get(key);
                break;
            }
        }

        if (cachedAsset && cachedAsset instanceof type) {
            return cachedAsset as InstanceType<T>;
        }
        console.warn(`未能找到资源: ${cacheKey}`);
        return null;
    }
    /**获取预制体
     * @param prefabPath 预制体路径
     * @returns 预制体
     */
    public getPrefab(prefabPath: string): Prefab {
        const prefab = this.getAsset(prefabPath, Prefab) as Prefab;
        if (!prefab) return null;
        return prefab;
    }

    /**获取图片资源
     * @param spritePath 图片路径
     * @returns 图片资源
     */
    public getSpriteFrame(spritePath: string): SpriteFrame {
        const spriteFrame = this.getAsset(spritePath, SpriteFrame) as SpriteFrame;
        if (!spriteFrame) {
            console.warn(`未能找到图片资源: ${spritePath}`);
            return null;
        }
        return spriteFrame;
    }
    /**获取json资源
     * @param jsonPath json路径
     * @returns json资源
     */
    public getJsonAsset(jsonPath: string): JsonAsset {
        const jsonAsset = this.getAsset(jsonPath, JsonAsset) as JsonAsset;
        if (!jsonAsset) {
            console.warn(`未能找到json资源: ${jsonPath}`);
            return null;
        }
        return jsonAsset;
    }

    /** 释放制定bundle包资源 */
    public releaseBundle(bundleName: string) {
        this._assetsCache.forEach((value, key) => {
            let [path, type, tempBundleName] = key.split(":");
            if (tempBundleName == bundleName) {
                value.decRef();
            }
        })
    }
}


