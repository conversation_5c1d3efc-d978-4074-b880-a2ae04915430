import { _decorator, Component, Node, instantiate, director, Vec3, UITransform, Canvas, Camera, Widget, gfx, renderer, view } from 'cc';
import { UIConfigTable } from '../uiPanel/UIConfig';
import { ResManager } from './ResManager';
import Singleton from '../base/Singleton';
const { ccclass, property } = _decorator;

export interface PageActive {
    show(...args: any[]): void,
    hide(...args: any[]): void,
    enableBtn(): void,
    disableBtn(): void,
}

@ccclass('UIManager')
export class UIManager extends Singleton {
    //==================== 成员属性 ====================
    private _dictPanel = new Map<string, Node>();
    private _uiRoot?: Node;

    //==================== 构造函数 ====================
    constructor() {
        super();
    }

    //==================== 单例相关 ====================
    static get instance() {
        return super.getInstance<UIManager>()
    }
    //====================== 方法 ======================

    public init() {
        console.log('Init UIManager !');
        ResManager.instance.loadBundle("uiBundle");
        this._uiRoot = new Node();
        this._uiRoot.name = "GloabUIRoot";
        this._uiRoot.layer = 1 << 25;
        director.getScene().addChild(this._uiRoot);
        this._uiRoot.setPosition(view.getVisibleSize().width / 2, view.getVisibleSize().height / 2);
        director.addPersistRootNode(this._uiRoot);
        let uiTransform = this._uiRoot.addComponent(UITransform);
        uiTransform.setContentSize(view.getVisibleSize().width, view.getVisibleSize().height);
        const nodeCanvas = this._uiRoot.addComponent(Canvas);

        const cameraNode = new Node();
        cameraNode.name = "UICamera";
        cameraNode.layer = 1 << 25;
        this._uiRoot.addChild(cameraNode);
        const camera = cameraNode.addComponent(Camera);
        camera.priority = 65535;
        camera.visibility = 1 << 25;
        camera.clearFlags = gfx.ClearFlagBit.DEPTH;
        camera.projection = renderer.scene.CameraProjection.ORTHO;
        camera.orthoHeight = view.getVisibleSize().height / 2;

        nodeCanvas.cameraComponent = camera;
        nodeCanvas.alignCanvasWithScreen = true;



    }

    /**
     * 显示一个页面
     * @param name 页面名称，要求首字母小写
     * @param cb 页面显示之后的回调
     * @param args 相关参数
     * @returns 
     */
    public showUI(name: string, cb?: Function, ...args: any[]) {
        const scriptName = name.substring(0, 1).toUpperCase() + name.substring(1);
        if (this._dictPanel.has(name)) {
            const panel = this._dictPanel.get(name)!;
            panel.parent = this._uiRoot;
            const comp = panel.getComponent(scriptName);
            if (comp && (comp as Component & PageActive)['show']) {
                (comp as Component & PageActive)['show'].apply(comp, args);
            }

            if (cb) {
                cb();
            }

            return;
        }

        const uiConfig = UIConfigTable[name];
        ResManager.instance.loadUI(uiConfig.bundleName, uiConfig.bundlePath, (err, prefab) => {
            if (err) {
                console.warn(err);
                return;
            }

            const panel = instantiate(prefab!);
            this._dictPanel.set(name, panel);
            panel.parent = this._uiRoot;
            panel.setPosition(Vec3.ZERO);
            const comp = panel.getComponent(scriptName);
            if (comp && (comp as Component & PageActive)['show']) {
                (comp as Component & PageActive)['show'].apply(comp, args);
            }

            if (cb) {
                cb();
            }
        });
    }

    /**
  * 隐藏一个页面
  * @param name 页面名称，要求首字母小写
  * @param cb 页面隐藏之后的回调
  */
    public hideUI(name: string, cb?: Function) {
        if (this._dictPanel.has(name)) {
            const scriptName = name.substring(0, 1).toUpperCase() + name.substring(1);
            const panel = this._dictPanel.get(name)!;
            panel.parent = null;
            const comp = panel.getComponent(scriptName);
            if (comp && (comp as Component & PageActive)['hide']) {
                (comp as Component & PageActive)['hide'].apply(comp);
            }
            if (cb) {
                cb();
            }
            const uiConfig = UIConfigTable[name];
            if (uiConfig.isAutoRelease) {
                panel.destroy();
                this._dictPanel.delete(name);
                ResManager.instance.removeBundleAssets(uiConfig.bundleName, uiConfig.bundlePath);
            }
        }
    }

    public getUI(name: string) {
        if (this._dictPanel.has(name)) {
            const scriptName = name.substring(0, 1).toUpperCase() + name.substring(1);
            const panel = this._dictPanel.get(name)!;
            return panel;
        }
        return null;
    }
}


