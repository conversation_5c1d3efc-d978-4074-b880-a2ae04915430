import { _decorator, Component, Node, sys } from 'cc';
import Singleton from '../base/Singleton';
import { releaseType } from '../Enum/Enum';
import { RemoteServerData } from '../game/chiDaXi/data/RemoteServerData';
import { RuntimeData } from '../game/chiDaXi/data/GameData';
const { ccclass, property } = _decorator;

@ccclass('GameModel')
export class GameModel extends Singleton {
    //==================== 公有属性 ====================
    public readonly GameName: string = "chidaxi4";
    public readonly HomeSceneName: string = "HomeScene";
    public readonly MainGameSceneName: string = "gameScene_chiDaXi";
    /**发布平台类型 */
    releaseType: number;
    //==================== 私有属性 ====================
    /**关卡数 */
    private _level: number = null;
    /**小程序游玩次数*/
    private _playNum: number = null;
    /**登录时间 */
    private _loginTime: number = null;
    /**背景音乐开关 */
    private _musicFlag: number = null;
    /**音效开关 */
    private _soundFlag: number = null;
    /**震动开关 */
    private _vibrateFlag: number = null;
    /** 分享游戏次数 */
    private _shareGameNum: number = -1;
    /** 看广告次数 */
    private _watchAdNum: number = -1;
    public curBgmName: string;

    public eatCakeAuto: boolean = true
    //==================== 属性访问器 ====================

    public get level(): number {
        return this._level;

    }
    public set level(value: number) {
        this._level = value;
        this.saveToLocal("level", this._level);
    }
    public get playNum(): number {
        return this._playNum;
    }
    public set playNum(value: number) {
        this._playNum = value;
        this.saveToLocal("playNum", this._playNum);
    }
    public get loginTime(): number {
        return this._loginTime;
    }
    public set loginTime(value: number) {
        this._loginTime = value;
        this.saveToLocal("loginTime", this._loginTime);
    }
    public get musicFlag(): number {
        return this._musicFlag;
    }
    public set musicFlag(value: number) {
        this._musicFlag = value;
        this.saveToLocal("musicFlag", this._musicFlag);
    }
    public get soundFlag(): number {
        return this._soundFlag;
    }
    public set soundFlag(value: number) {
        this._soundFlag = value;
        this.saveToLocal("soundFlag", this._soundFlag);
    }
    public get vibrateFlag(): number {
        return this._vibrateFlag;
    }
    public set vibrateFlag(value: number) {
        this._vibrateFlag = value;
        this.saveToLocal("vibrateFlag", this._vibrateFlag);
    }

    public get shareGameNum(): number {
        if (this._shareGameNum == -1) {
            this._shareGameNum = this.loadFromLocal<number>("shareGameNum", 0);
        }
        return this._shareGameNum;
    }
    public set shareGameNum(val: number) {
        this._shareGameNum = val;
        RemoteServerData.instance.updateShareGameNum(val);
        this.saveToLocal("shareGameNum", this._shareGameNum);
        // console.log("加分享游戏次数", val);
    }
    public get watchAdNum(): number {
        if (this._watchAdNum == -1) {
            this._watchAdNum = this.loadFromLocal<number>("watchAdNum", 0);
        }
        return this._watchAdNum;
    }
    public set watchAdNum(val: number) {
        this._watchAdNum = val;
        RemoteServerData.instance.updateWatchAdNum(val);
        this.saveToLocal("watchAdNum", this._watchAdNum);
        // console.log("加看广告次数", val);
    }

    //==================== 构造函数 ====================
    constructor() {
        super();
        console.log("constructor gameModel")
    }

    //==================== 单例相关 ====================
    static get instance() {
        return super.getInstance<GameModel>()
    }
    //====================== 方法 ======================
    /**初始化GameModel */
    public initGameModel(releaseType: releaseType) {
        console.log("init gameModel")
        //  this._level = this.loadFromLocal<number>("level",1);
        this.releaseType = releaseType;
        this._playNum = this.loadFromLocal<number>("playNum", 0);
        this._loginTime = this.loadFromLocal<number>("loginTime", 0);
        this._musicFlag = this.loadFromLocal<number>("musicFlag", 1);
        this._soundFlag = this.loadFromLocal<number>("soundFlag", 1);
        this._vibrateFlag = this.loadFromLocal<number>("vibrateFlag", 1);

    }
    /**
   * 从本地存储读取数据，如果不存在则保存并返回默认值
   * @param key 存储键名
   * @param defaultValue 默认值
   * @returns 返回读取到的值或默认值
   */
    public loadFromLocal<T>(key: string, defaultValue: T = null): T {
        const fullKey = this.getStorageKey(key);
        const value = sys.localStorage.getItem(fullKey);

        if (value === null || value === '') {
            // 如果本地没有数据，保存默认值
            sys.localStorage.setItem(fullKey, JSON.stringify(defaultValue));
            if (defaultValue === null) {
                console.log(`没有存储默认值,默认值现在为null: ${key}`);
            }
            return defaultValue;
        }

        try {
            return JSON.parse(value) as T;
        } catch (e) {
            console.error(`解析存储数据失败: ${key}`, e);
            sys.localStorage.setItem(fullKey, JSON.stringify(defaultValue));
            return defaultValue;
        }
    }

    /**
     * 将数据保存到本地存储
     * @param key 存储键名
     * @param value 要保存的值
     */
    public saveToLocal<T>(key: string, value: T): void {
        const fullKey = this.getStorageKey(key);
        sys.localStorage.setItem(fullKey, JSON.stringify(value));
    }

    /**通过key值和游戏名字得到一个新的key值 */
    private getStorageKey(key: string): string {
        return `${this.GameName}_${key}`;
    }
}


