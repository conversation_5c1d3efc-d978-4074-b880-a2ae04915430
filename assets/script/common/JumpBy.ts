import { Vec3,_decorator,Component} from 'cc';
const {ccclass, property} = _decorator;

@ccclass
export default class JumpBy extends Component {
    private _startPosition : Vec3 = new Vec3(0,0);
    private _previousPosition : Vec3 = new Vec3(0,0);
    private _delta : Vec3 = new Vec3(0,0);
    private _height : number = 0;
    private _isRuning : boolean = false;
    private _duration : number = 0;
    private _easeList : Array<Function> = [];
    private _elapsed : number = 0;
    private _rotationScale : number = 0;
    private _fcb : Function = null;

    public get isRuning():boolean{return this._isRuning;};

    /**
     * 
     * @param duration 时长
     * @param position 跳出增加的坐标
     * @param height 跳跃调试
     * @param _rotationScale 旋转比例
     * @returns 
     */
    runAction(duration:number, position:Vec3, height:number,_rotationScale:number=0){
        this._duration = duration;
        this._delta = position;
        this._height = height;
        this._isRuning = true;
        this._rotationScale = _rotationScale;
        this._fcb = null
        this._elapsed = 0
        return this;
    }
    easing(ease:Function){
        this._easeList.push(ease);
        return this
    }
    callBack(cb:Function){
        this._fcb = cb;
        return this;
    }
    stop(){
        this._isRuning = false
    }
    _computeEaseTime(dt:number) {
        var locList = this._easeList;
        if ((!locList) || (locList.length === 0))
            return dt;
        for (var i = 0, n = locList.length; i < n; i++)
            dt = locList[i](dt);
        return dt;
    }
    _updateStep(ldt){
        var dt = this._computeEaseTime(ldt);
        var frac = dt % 1.0;
        var y = this._height * 4 * frac * (1 - frac);
        y += this._delta.y * dt;

        var x = this._delta.x * dt;
        var z = this._delta.z * dt;
        var locStartPosition = this._startPosition;
        var targetX = this.node.getPosition().x;
        var targetY = this.node.getPosition().y;
        var targetZ = this.node.getPosition().z;
        var locPreviousPosition = this._previousPosition;

        locStartPosition.x = locStartPosition.x + targetX - locPreviousPosition.x;
        locStartPosition.y = locStartPosition.y + targetY - locPreviousPosition.y;
        locStartPosition.z = locStartPosition.z + targetZ - locPreviousPosition.z;
        x = x + locStartPosition.x;
        y = y + locStartPosition.y;
        z = z + locStartPosition.z;
        locPreviousPosition.x = x;
        locPreviousPosition.y = y;
        locPreviousPosition.z = z;
        this.node.setPosition(x, y,z);
        if(!!this._rotationScale){
            this.node.angle = -dt * 180 * this._rotationScale
        }
        if(dt >= 1.0){
            this._previousPosition = locPreviousPosition;
            this._isRuning = false;
            this._startPosition = this.node.getPosition();
            this._fcb && this._fcb(this);
        }
    }
    update(dt){
        if(this._isRuning){
            this._elapsed += dt;
            var t = this._elapsed / (this._duration > 0.0000001192092896 ? this._duration : 0.0000001192092896);
            t = (1 > t ? t : 1);
            this._updateStep(t);
        }
    }
}
