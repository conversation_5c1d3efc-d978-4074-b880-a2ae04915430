// "use strict";
// Object.defineProperty(exports, "__esModule", { value: true });
// exports.BeizerUtil = void 0;
/**
 * 贝塞尔曲线运动
 * <AUTHOR>
 */
export class BeizerUtil {
    //#region 获得当前坐标
    /**
     * 获得当前坐标
     * @param timer 时间[0,1]
     * @returns
     */
    static getPosition = function (points, timer) {
        var x = 0, y = 0;
        var bezier;
        for (var i = 0, n = points.length; i < n; i++) {
            bezier =
                Math.min(Math.pow(1 - timer, n - i - 1) * Math.pow(timer, i)) *
                BeizerUtil.calculationC(i, n);
            x += points[i].x * bezier;
            y += points[i].y * bezier;
        }
        return { x: x, y: y };
    };
    //#endregion
    //region 获得路线的长度
    static distance = function (p1, p2) {
        var dx = p2.x - p1.x;
        var dy = p2.y - p1.y;
        return Math.sqrt(dx * dx + dy * dy);
    };
    /**
     * 获得整个路线的长度
     * @param precision 精确度（微积分方案）
     */
    static getLength = function (points, precision) {
        if (precision === void 0) { precision = 100; }
        var length = 0;
        var point1;
        var point2;
        for (var t = 0; t < 1; t += 1 / precision) {
            point1 = BeizerUtil.getBezierPoint(points, t);
            point2 = BeizerUtil.getBezierPoint(points, t + 1 / precision);
            length += BeizerUtil.distance(point1, point2);
        }
        return length;
    };
    static calculationC = function (i, n) {
        return (BeizerUtil.factorial(n - 1) /
            (BeizerUtil.factorial(i) * BeizerUtil.factorial(n - 1 - i)));
    };
    static factorial = function (num) {
        if (num < 0) {
            return 1;
        }
        else if (num === 0 || num === 1) {
            return 1;
        }
        else {
            return num * BeizerUtil.factorial(num - 1);
        }
    };
    static getBezierPoint = function (points, t) {
        if (points.length === 1) {
            return points[0];
        }
        var nextPoints = [];
        var x;
        var y;
        for (var i = 0; i < points.length - 1; i++) {
            x = (1 - t) * points[i].x + t * points[i + 1].x;
            y = (1 - t) * points[i].y + t * points[i + 1].y;
            nextPoints.push({ x: x, y: y });
        }
        return BeizerUtil.getBezierPoint(nextPoints, t);
    };
}
