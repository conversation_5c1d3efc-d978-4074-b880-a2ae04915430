import { director } from "cc";
import Singleton from "../base/Singleton";
import { sceneNamesEnum } from "../game/chiDaXi/Index";

export class ChangeScene extends Singleton{
    public static get instance(): ChangeScene{
        return super.getInstance();
    }

    public reloadCurScene(){
        const curSceneName = director.getScene().name;
        director.loadScene(curSceneName);
    }

    public changeScene(sceneName: string) {
        // 加载指定场景的包
        // ResManager.instance.loadBundle(bundleNamesEnum.主页场景);
        // assetManager.loadBundle(bundleNamesEnum.主页场景, (err: Error, bundle: AssetManager.Bundle) => {
        //     if (err) {
        //         console.error('[HomeScene] 加载mainGameBundle失败:', err);
        //         return;
        //     }
        //     + bundle.preloadDir("")
        // });
        director.loadScene(sceneNamesEnum.主页场景);
    }
}