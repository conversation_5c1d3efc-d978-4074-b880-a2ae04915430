import { find, Node } from "cc";
import Singleton from "../base/Singleton";
import { DishProductGenerator, IGrid } from "../game/chiDaXi/dish/DishProductGenerator";
import { Tools } from "./Tools";

export class GuideGame extends Singleton{
    static get instance(){
        return super.getInstance<GuideGame>();
    }

    private _curOptCount: number = 0;

    private _guideFinger: Node = null;

    /** 开始引导 */
    initGuide(){
        this._curOptCount = 0;
        this._guideFinger = null;
        this._addGuideFinger();
    }

    addGuideCount(){
        this._curOptCount += 1;

        this.updateGuideFinger();
    }

    private _addGuideFinger(){
        if (!this._guideFinger) {
            let guideFinger = Tools.newPrefab("GuideFinger", find("Canvas"));
            this._guideFinger = guideFinger;
            this._guideFinger.active = false;
        }
    }
    /** 更新引导小手 */
    public updateGuideFinger(){
        if(!this._guideFinger) return;

        let guideFingerCount = 2;
        if (this._curOptCount > guideFingerCount) return;

        let allGrids: Node[] = DishProductGenerator.instance.getAllOptItems();
        let pos = allGrids[Math.floor(allGrids.length / 2)].position;
        this._guideFinger.setPosition(pos);
        this._guideFinger.active = true;

        if (this._curOptCount >= guideFingerCount){
            this._guideFinger.destroy();
            this._guideFinger = null;
        }
    }
}