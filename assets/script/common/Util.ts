import { _decorator, Asset, assetManager, AssetManager, Button, Component, Node, Prefab, warn } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('Util')
export class Util {
    //==================== 数学 ====================
    /**
          * 获取指定范围的随机数
          * @param {Number} min,最小值
          * @param {Number} max,最大值
          * @param {Boolean} isInt,是否返回整数（取小、会取到max值）
          * @return {Number}随机数
          */
    public static getRandomNum(min: number, max: number, isInt: boolean = false): number {
        if (isInt) {
            return Math.floor(Math.random() * (max - min + 1) + min);
        }
        return Math.random() * (max - min) + min;
    }

  
  

    //==================== 其他 ====================
    /**
  * 为按钮添加点击事件
  * @param button 按钮组件
  * @param target 目标节点
  * @param component 组件名称
  * @param handler 回调函数名称
  * @param customEventData 自定义数据（可选）
  */
    public static addClickEvent(
        button: Button,
        target: Node,
        component: string,
        handler: string,
        customEventData: string = ''
    ): void {
        // 检查参数是否有效
        if (!button || !target) {
            warn('按钮或目标节点为空');
            return;
        }

        const clickHandler = new Component.EventHandler();
        clickHandler.target = target;
        clickHandler.component = component;
        clickHandler.handler = handler;
        if (customEventData) {
            clickHandler.customEventData = customEventData;
        }

        // 确保 clickEvents 数组存在
        if (!button.clickEvents) {
            button.clickEvents = [];
        }

        // 避免重复添加
        if (!button.clickEvents.some(event => event && event.handler === handler)) {
            button.clickEvents.push(clickHandler);
        }
    }
}


