import { director, easing, is<PERSON><PERSON><PERSON>, Node, tween, v3 } from "cc";
import Singleton from "../base/Singleton"
import { DishProductGenerator } from "../game/chiDaXi/dish/DishProductGenerator";
import { LevelData_CDX } from "../game/chiDaXi/data/LevelData_CDX";
import { TableManager } from "../game/chiDaXi/table/TableManager";
import { Table } from "../game/chiDaXi/table/Table";
import { Dish } from "../game/chiDaXi/dish/Dish";
import { colorIdEnum } from "../game/chiDaXi/Index";
import { Tools } from "./Tools";

export enum GameStageEnum{
    IDLE = 0,
    PLAYING = 1,
    SUCCESS,
    PREPARE_RELIVE,
    OVER
}

export class AutoGame extends Singleton{
    static get instance(){
        return super.getInstance<AutoGame>();
    }

    private _schedulerNode: Node;

    /** 自动游戏版本 */
    autoGameVer: boolean = false;

    autoFlag: boolean = false;

    private _gameStage: GameStageEnum = GameStageEnum.IDLE;

    private _reliveCb: Function[];
    private _overCbs: Function[];
    private _playingUsePropCbs: Function[];

    public curOldObjectIndex: number = -1;

    private _colorIdsInCurLevel: colorIdEnum[];

    init(schedulerNode: Node){
        this._schedulerNode = schedulerNode;

        this.curOldObjectIndex += 1;
    }

    public get colorIdsInCurLevel(): colorIdEnum[] {
        if (!this._colorIdsInCurLevel) {
            let randomNums = Tools.getDiffNumRandom(1, 9, 4);
            this._colorIdsInCurLevel = randomNums as colorIdEnum[];
        }
        return this._colorIdsInCurLevel;
    }

    startAutoGame() {
        console.log("自动程序执行中……");
        switch(this._gameStage){
            case GameStageEnum.PLAYING:
                this.autoOptGame();
                break;
            case GameStageEnum.SUCCESS:
                this.overAutoGame();
                break;
            case GameStageEnum.PREPARE_RELIVE:
                this.autoOptReliveUI();
                break;
            case GameStageEnum.OVER:
                this.autoOptOverUI();
                break;
        }

        this.scheduleOnce(() => {
            this.startAutoGame();
        }, Math.random() * 1 + 1.5);
    }

    scheduleOnce(callBack: ()=>void, delayTime: number){
        tween(this._schedulerNode)
            .delay(delayTime)
            .call(callBack)
            .start();
    }

    autoOptGame() {
        if (this._gameStage !== GameStageEnum.PLAYING) return;

        let allOptItems = DishProductGenerator.instance.getAllOptItems();
        let allTable = TableManager._ins.getTables(true);
        // 分别列举所有桌子的颜色（桌子按照顺序） 和 所有盘子的颜色
        let allTableColors = allTable.map(table => table.getComponent(Table).colorId);
        let allDishColors = allOptItems.map(dish => dish.getComponent(Dish).colorId);
        
        // 获取所有可操作 并且有对应颜色桌子的盘子
        let items: Node[] = [];
        for(let i = 0; i < allDishColors.length; i++){
            let colorId = allDishColors[i];
            if (allTableColors.includes(colorId)) {
                items.push(allOptItems[i]);
            }
        }
        // 如果没有上述的盘子，则从所有可操作盘子随机选择盘子进行上菜操作
        if(items.length <= 0){
            console.log("没有对应的颜色");
            items = allOptItems;
        }

        let randomIndex = Math.floor(Math.random() * items.length);
        let randomOptItem = items[randomIndex];
        let grid = DishProductGenerator.instance.getGirdByItem(randomOptItem);
        if (grid) {
            if (Math.random() < 0.02 && LevelData_CDX._ins.levelConfig.length > 20) {
                let randomUsePropCb = this._playingUsePropCbs[Math.floor(Math.random() * this._playingUsePropCbs.length)];
                randomUsePropCb();
            } else {
                DishProductGenerator.instance.optGrid(grid);
            }
        }
    }

    autoOptReliveUI(){
        let randomIndex = Math.floor(Math.random() * this._reliveCb.length);
        let randomCb = this._reliveCb[randomIndex];
        randomCb();

        this.setIdleStage();
    }

    autoOptOverUI(){
        this._overCbs.forEach(cb=>cb());
        this.overAutoGame();
    }

    overAutoGame(){
        this._schedulerNode = null;
        this._colorIdsInCurLevel = null;
    }

    // 以下是设置状态
    public startGame(...args: Function[]){
        this._playingUsePropCbs = args;
        this._gameStage = GameStageEnum.PLAYING;
        this.startAutoGame();
    }
    public reliveGame(...args: Function[]){
        this._gameStage = GameStageEnum.PREPARE_RELIVE;
        this._reliveCb = args;
    }
    public overGame(...args: Function[]){
        this._gameStage = GameStageEnum.OVER;
        this._overCbs = args;
    }
    public setIdleStage(){
        this._gameStage = GameStageEnum.IDLE;
    }

    /** 注册按钮点击动作 */
    public registerBtnClickAnim(btnNode: Node) {
        if(!this.autoFlag) return;
        if(!btnNode || !isValid(btnNode)) return;
        tween(btnNode)
            .by(0.23, { scale: v3(-0.1, -0.1, 0) }, { easing: easing.sineOut })
            .by(0.12, { scale: v3(0.1, 0.1, 0) }, { easing: easing.sineIn })
            .start();
        return 0.5;
    }
}