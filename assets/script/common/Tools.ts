import {  Sprite<PERSON><PERSON>e, Vec3 , Node,  Vec2, Button, v2, sys, instantiate, v3, Sprite, misc,  UITransform } from "cc";
import { ResManager } from "../manager/ResManager";
import { LoadTools } from "./LoadTools";

/** 游戏常用工具类 */
export class Tools {
    //-----------------------------节点预制体相关-------------------------------
    /**
     * 新建一个预制体在场景里
     * @param preName 预制体名字或url
     * @param callFunc 加载预制体 完成后回调
     * @param parent 存放预制体的父节点
     * @param Pos 预制体的坐标
     * @param zindex 预制体的层级
     */
    public static newPrefab(preName: string, parent?: Node, Pos?: Vec3 , callFunc?: (age?: Node) => void): Node {
        return NodeTools._ins.newPrefab(preName, callFunc , parent, Pos );
    }
    /**
     * 新建一个图片在场景里
     * @param sprName 图片名字或url
     * @param callFunc 加载预制体 完成后回调
     * @param parent 存放预制体的父节点
     * @param Pos 预制体的坐标
     * @param zindex 预制体的层级
     */
    public static newSprite(sprName: string,parent?: Node, Pos?: Vec3 , callFunc?: (age?: Node) => void ): Node {
        return NodeTools._ins.newSprite(sprName, callFunc, parent, Pos);
    }
    /**
     * 设置一个节点的SpriteFrame
     * @param nodeT 节点的Node
     * @param sprUrl 图片的url或者存放到resArr的名字
     */
    public static setSpriteFrame(nodeT: Node, sprUrl: string) {
        NodeTools._ins.setSpriteFrame(nodeT, sprUrl);
    }
    /** 设置一个节点的 groupIndex 包含子物体 */
    public static setNodeLayerIndex(nodeT: Node, groupIndex: number) {
        NodeTools._ins.setNodeLayerIndex(nodeT, groupIndex);
    }
    /**
     * 设置一个Button的按下和松开的SpriteFrame
     * @param norUrl 默认状态的名字或者路径
     * @param preUrl 按下状态的名字或者路径
     */
    public static setBtnClickSpr(Btn: Button, norUrl: string, preUrl: string) {
        NodeTools._ins.setBtnClickSpr(Btn, norUrl, preUrl);
    };
    /** 切换父物体 不改变显示位置*/
    public static setNodeParent (node:Node, parent:Node) {
        NodeTools._ins.setNodeParent(node, parent);
    };    
    //----------------------------------数学数组相关----------------------------------
    /**
     * 获取随机数
     * @param isInteger 是否随机整数  默认整数
     */
    public static random(x1: number, x2: number, isInteger = true): number {
        return MathTools._ins.random(x1, x2, isInteger);
    }
    /**
     * 根据概率数组 随机概率 返回数组的index
     * @param chooseArr 需要随机概率的数组 例如[0.05,0.1,0.2,0.3]
     */
    public static chooseRandom(chooseArr: Array<number>) {
        return MathTools._ins.chooseRandom(chooseArr);
    }
    /** 传入一个弧度 返回一个Y轴折射后的弧度 */
    public static refractionY(rad: number) {
        return MathTools._ins.refractionY(rad);
    };
    /** 传入一个弧度 返回一个轴折射后的弧度 */
    public static refractionX(rad: number) {
        return MathTools._ins.refractionX(rad);
    };
    /** 重新打乱一个数组的顺序  洗牌 */
    public static aginSortArr(Arr: Array<any>) {
        MathTools._ins.aginSortArr(Arr);
    };
    /**
     * 将一个数组 按照里面的对象排序 
     * @param tempArr 传入的数组
     * @param sortName 对象属性名字
     * @param isReverse 是否倒序
     */
    public static sortArrForObject(tempArr: Array<any>, sortName: string, isReverse = false) {
        MathTools._ins.sortArrForObject(tempArr, sortName, isReverse);
    };
    /**
     * 取一定范围内不重复的数字
     * @param minNum 最小取值范围
     * @param maxNum 最大取值范围
     * @param getNum 取几个数字
     */
    public static getDiffNumRandom(minNum: number, maxNum: number, getNum: number) {
        return MathTools._ins.getDiffNumRandom(minNum, maxNum, getNum);
    };
    //--------------------------------向量坐标计算相关------------------------------------
    /**
     * 根据两个点  求角度
     * @param pos1 起始点坐标
     * @param pos2 结束点坐标
     * @param isVertical 是否以竖直方向为0度开始
     */
    public static getAngleForPos(pos1: Vec2, pos2: Vec2, isVertical = false): number {
        return VectTools._ins.getAngleForPos(pos1, pos2, isVertical);
    };
    /** 获取两个坐标之间的距离 */
    public static getDistance(pos1: Vec3, pos2: Vec3): number {
        return Vec3.distance(pos1,pos2);
    };
    /**
     * 根据一个角度和长度 计算相对应的坐标
     * @param angle 角度
     * @param len 该角度上的长度
     * @param startPos 初始的坐标
     */
    public static getPosForAngleLen(angle: number, len: number, startPos: Vec2 = v2(0, 0) ) {
        return VectTools._ins.getPosForAngleLen(angle, len, startPos);
    }
    /**
     * 获取节点在另一个节点下的坐标
     * @param obj 节点
     * @param mainObj 相对于的另一个节点
     */
    public static getToNodePosForNode(obj: Node, mainObj: Node): Vec2 {
        return VectTools._ins.getToNodePosForNode(obj, mainObj);
    };
    /** 获取节点的世界坐标 */
    public static getToWorldPosAR(obj: Node) {
        return VectTools._ins.getToWorldPosAR(obj);
    }
    /**
     * 通过世界坐标 获取相对节点的坐标
     * @param worldPos 世界坐标
     * @param obj 相对节点下的
     */
    public static getToNodePosForWorld(worldPos:Vec3 , obj: Node) {
        return VectTools._ins.getToNodePosForWorld(worldPos, obj);
    }

    //--------------------------------数组操作相关------------------------------------
    /** 根据value值 从数组里面移除 */
    public static removeArrForValue(tempArr: Array<any>, value: any) {
        return tempArr.splice(tempArr.indexOf(value), 1);
    }
    /** 从数组里面添加一个该数组里没有的元素 */
    public static addArrNoValue(tempArr: Array<any>, value: any) {
        if (tempArr.indexOf(value) < 0) {
            tempArr.push(value);
            return true;
        }
        return false;
    }
    /** 从数组指定位置 插入某个元素 */
    public static addArrIndex(tempArr: Array<any>, index: number, value: any) {
        return tempArr.splice(index, 0, value);
    }
    //--------------------------------其他------------------------------------
    /**
     *  字符串指定位置插入新字符 
     * @param soure 需要操作的字符串
     * @param start 从那个位置开始插入
     * @param newStr 插入的新的字符
     */
    public static insertStrForIndex(soure: string, start: number, newStr: string): string {
        return soure.slice(0, start) + newStr + soure.slice(start);
    };
    /**
     * 数字整数前边补零 并返回字符串
     * @param num 传入的数字
     * @param length 前边补几个零
     */
    public static prefixInteger(num: number, length = 2): string {
        return (Array(length).join('0') + num).slice(-length);
    };
    /** 获取系统语言 */
    public static getLanguageType(): string {
        var langNumType = "EN";   //默认英语
        if ( sys.language == sys.Language.CHINESE ) {
            if (sys.languageCode.toLowerCase().indexOf("zh-cn") != -1 ||
                sys.languageCode.toLowerCase().indexOf("zh_cn") != -1 ||
                sys.languageCode.toLowerCase().indexOf("zh-hans-cn") != -1) {
                langNumType = "CN";  //简体
            } else {
                langNumType = "CHT";  //繁体
            }
        } 
        // else if ( sys.language == sys.Language.KOREAN ) {
        //     langNumType = "KOR"; //韩语
        // }
        // else if (sys.language == sys.Language.JAPANESE) {
        //     langNumType = "JP"; //日语
        // }
        // else if ( window.navigator.language == "th-TH" ) {
        //     langNumType = "TH"; //泰语
        // }
        return langNumType;
    }

    public static async callFuncUntilSuccess(callFun: Function, conditionFunc: ()=>boolean){
        let index = 0;
        while(index <= 10000){
            if(conditionFunc()){
                callFun();
                // console.log("达成", index);
                break;
            }
            index ++;
            if(index == 10000){
                console.log("关闭加载页面失败");
            }
            await this.sleep(10);
        }
    }

    public static sleep(t: number){
        return new Promise(resolve => setTimeout(resolve, t));
    }
}


/** 节点相关 工具类 */
class NodeTools {
    /** 单例模式 */
    private static _instance: NodeTools = new NodeTools();
    private constructor() { }
    public static get _ins() {
        return this._instance;
    }

    /** 新建一个预制体在场景里 */
    public newPrefab(preName: string, callFunc?: (age?: Node) => void, parent?: Node, Pos?: Vec3 ): Node {
        let prefab = ResManager.instance.getPrefab(preName);
        let clone:Node = null;
        if (prefab != null) {
            clone = instantiate(prefab);
            if (parent) { parent.addChild(clone) };
            if (Pos) { clone.position = v3(Pos.x,Pos.y,Pos.z) };
            if (callFunc != null) {
                callFunc(clone);
            }
        } else {
            LoadTools._ins.loadResPrefab(preName, callFunc, parent, Pos);
        }
        return clone;
    }
    /** 新建一个图片在场景里 */
    public newSprite(sprName: string, callFunc?: (age?: Node) => void, parent?: Node, Pos?: Vec3) {
        let sprite = new Node();
        sprite.name = sprName;
        if (ResManager.instance.getSpriteFrame(sprName) != null) {
            sprite.addComponent(Sprite).spriteFrame = ResManager.instance.getSpriteFrame(sprName);
            if (parent) { parent.addChild(sprite) };
            if (Pos) { sprite.position = v3(Pos.x,Pos.y,Pos.z) };
            if (callFunc != null) {
                callFunc(sprite);
            }
        } else {
            sprite.addComponent(Sprite);
            LoadTools._ins.loadResSpriteFrame(sprName, sprite, parent, Pos, callFunc);
        }
        return sprite;
    }
    /** 设置一个节点的SpriteFrame */
    public setSpriteFrame(nodeT: Node, sprUrl: string) {
        if (ResManager.instance.getSpriteFrame(sprUrl)) {
            nodeT.getComponent(Sprite).spriteFrame = ResManager.instance.getSpriteFrame(sprUrl)
        } else {
            LoadTools._ins.loadResAny(sprUrl, SpriteFrame, (spriteFrame: SpriteFrame) => {
                nodeT.getComponent(Sprite).spriteFrame = spriteFrame;
            })
        }
    }
    /** 设置一个节点的 所属层 包含子物体 */
    public setNodeLayerIndex(nodeT: Node, groupIndex: number) {
        nodeT.layer = groupIndex;
        for (let i = 0; i < nodeT.children.length; i++) {
            this.setNodeLayerIndex(nodeT.children[i],groupIndex);
        }
    }
    /** 设置一个Button的按下和松开的SpriteFrame */
    public setBtnClickSpr(Btn: Button, norUrl: string, preUrl: string) {
        if (ResManager.instance.getSpriteFrame(norUrl)) {
            Btn.getComponent(Button).normalSprite = ResManager.instance.getSpriteFrame(norUrl)
            Btn.getComponent(Button).hoverSprite = ResManager.instance.getSpriteFrame(norUrl)
            Btn.getComponent(Button).pressedSprite = ResManager.instance.getSpriteFrame(preUrl)
        } else {
            LoadTools._ins.loadResAny(norUrl, SpriteFrame, (spr: SpriteFrame) => {
                Btn.getComponent(Button).normalSprite = spr;
                Btn.getComponent(Button).hoverSprite = spr;
            });
            LoadTools._ins.loadResAny(preUrl, SpriteFrame, (spr: SpriteFrame) => {
                Btn.getComponent(Button).pressedSprite = spr;
            });
        }
    };
    /** 切换父物体 不改变坐标 */
    public setNodeParent (node:Node, parent:Node) {
        let Pos = VectTools._ins.getToNodePosForNode(node, parent);
        node.parent = parent;
        node.position = v3(Pos.x,Pos.y);
    };
}

/** 数学数组计算相关 工具类 */
class MathTools {
    /** 单例模式 */
    private static _instance: MathTools = new MathTools();
    private constructor() { }
    public static get _ins() {
        return this._instance;
    }

    /** 获取随机数 */
    public random(x1: number, x2: number, isInteger = true): number {
        if (isInteger) {
            return x1 + Math.floor(Math.random() * (x2 - x1 + 1));
        }
        return Math.random() * (x2 - x1) + x1;
    }
    /**  根据概率数组 随机概率 返回数组的index */
    public chooseRandom(chooseArr: Array<number>) {
        let total = 0;  //概率总值
        //首先计算出概率的总值，用来计算随机范围
        for (let i = 0; i < chooseArr.length; i++) {
            total += chooseArr[i];
        }
        let randNum = this.random(0, total, false)
        for (let i = 0; i < chooseArr.length; i++) {
            if (randNum < chooseArr[i] && chooseArr[i] > 0) {
                return i;
            } else {
                randNum -= chooseArr[i];
            }
        }
        return chooseArr.length - 1;
    }
    /** 弧度折射Y轴 */
    public refractionY(rad: number) {
        return Math.atan2(Math.sin(rad), -Math.cos(rad));
    };
    /** 弧度折射X轴 */
    public refractionX(rad: number) {
        return Math.atan2(-Math.sin(rad), Math.cos(rad));
    };
    /** 重新洗牌 一个数组 */
    public aginSortArr(Arr: Array<any>) {
        for (let i = 0; i < Arr.length; i++) {
            let tempR = Tools.random(0, Arr.length - 1);
            if (tempR != i) {
                let temp = Arr[i];
                Arr[i] = Arr[tempR];
                Arr[tempR] = temp;
            }
        }
    }
    /** 数组 对象排序  对象属性  是否倒序 */
    public sortArrForObject(tempArr: Array<any>, sortName: string, isReverse = false) {
        if (!isReverse) {
            tempArr.sort((a, b) => {
                return a[sortName] - b[sortName];
            });
        } else {
            tempArr.sort((a, b) => {
                return b[sortName] - a[sortName];
            });
        }
    };
    /** 取一定范围内不重复的数字 */
    public getDiffNumRandom(minNum: number, maxNum: number, getNum: number) {
        var arr = [];
        for (let i = minNum; i <= maxNum; i++) {
            arr.push(i);
        }
        const tempLen = arr.length - getNum;
        for (let i = 0; i < tempLen; i++) {
            let tempI = Tools.random(0, arr.length - 1);
            arr.splice(tempI, 1);
        }
        return arr;
    };
}

/** 向量坐标转换相关工具类 */
class VectTools {
    /** 单例模式 */
    private static _instance: VectTools = new VectTools();
    private constructor() { }
    public static get _ins() {
        return this._instance;
    }

    /** 根据两个点  求角度 */
    public getAngleForPos(pos1: Vec2, pos2: Vec2, isVertical = false): number {
        let rad = 0;
        if (isVertical) {
            rad = -Math.atan2(pos2.x - pos1.x, pos2.y - pos1.y);
        } else {
            rad = Math.atan2(pos2.y - pos1.y, pos2.x - pos1.x);
        }
        return misc.radiansToDegrees(rad);
    }
    /** 根据一个角度和长度 计算相对应的坐标 */
    public getPosForAngleLen(angle: number, len: number, startPos:Vec2 = v2(0, 0)) {
        let rad = misc.degreesToRadians(angle);
        return v2(startPos.x + Math.cos(rad) * len, startPos.y + Math.sin(rad) * len);
    }
    /** 获取节点在另一个节点下的坐标 */
    public getToNodePosForNode(obj: Node, mainObj: Node): Vec2 {
        let worldPos = obj.parent.getComponent(UITransform).convertToWorldSpaceAR(obj.position);
        let nodePos = mainObj.getComponent(UITransform).convertToNodeSpaceAR(worldPos)
        return v2(nodePos.x,nodePos.y);
    };
    /** 获取节点的世界坐标 */
    public getToWorldPosAR(obj: Node) {
        return obj.parent.getComponent(UITransform).convertToWorldSpaceAR(obj.position);
    }
    /** 通过世界坐标 获取相对节点的坐标 */
    public getToNodePosForWorld(worldPos:Vec3, obj: Node) {
        return obj.getComponent(UITransform).convertToNodeSpaceAR(worldPos);
    }
}
