import { _decorator, Component, ResolutionPolicy, screen, size, sys, view } from "cc";

const { ccclass, property } = _decorator;
export const G_VIEW_SIZE = size(0, 0);

@ccclass("AdaptCanvas")
export class AdaptCanvas extends Component{
    public static adapterScreen() {
        this.initScreen();
    }
    /** 电脑端  按宽高一起适配 */
    public static initScreen() {
        console.log(sys.os, sys.OS.ANDROID, sys.OS.IOS);

        if (sys.os == sys.OS.ANDROID || sys.os == sys.OS.IOS || sys.os == sys.OS.UNKNOWN) {
            view.setResolutionPolicy(ResolutionPolicy.FIXED_WIDTH);
        } else {
            console.log("适配为其他");
            view.setResolutionPolicy(ResolutionPolicy.SHOW_ALL);
        }
    }
}