import Singleton from "../base/Singleton";
import { RuntimeData } from "../game/chiDaXi/data/GameData";
import { gameStateEnum } from "../game/chiDaXi/Index";
import { AudioManager } from "../manager/AudioManager";

export class VoicePackageManager extends Singleton {
    public static get instance(): VoicePackageManager{
        return super.getInstance();
    }

    private _prePlayTime: number = 0;

    public playVoicePackage(voiceName: string, voiceCount: number = 1, playP: number, volume: number){
        // if (RuntimeData._ins.gameState == gameStateEnum.start) {
            let curTime = new Date().getTime();
            if(curTime - this._prePlayTime < 5000) return;

            this._prePlayTime = curTime;
            if (Math.random() < playP) {
                let resFullName = "voicePackage/" + voiceName + Math.ceil(Math.random() * voiceCount);
                AudioManager.playSound(resFullName, volume);
            }
        // }
    }
}


