import { _decorator, Component, isValid, Node, v3, Vec3 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('GlobalLabelNodesManager')
export class GlobalLabelNodesManager extends Component {
    static instance: GlobalLabelNodesManager = null;

    private _labelNodeInfos: LableNodeInfo[] = [];

    private _tempPos = v3();

    protected onLoad(): void {
        GlobalLabelNodesManager.instance = this;
    }

    public addLabelNode(labelNode: Node, posInTargetNode?: Vec3) {
        let targetNode = labelNode.parent;
        if(posInTargetNode == undefined) posInTargetNode = labelNode.getPosition();
        this._labelNodeInfos.push(
            {
                labelNode,
                targetNode,
                posInTargetNode
            }
        );
        let worldScale = labelNode.getWorldScale();
        labelNode.setParent(this.node, true);
        labelNode.setWorldScale(worldScale);
    }

    protected update(dt: number): void {
        if(this._labelNodeInfos.length > 0){
            for(let i = 0; i < this._labelNodeInfos.length; i++){
                const info = this._labelNodeInfos[i];
                let {labelNode, targetNode, posInTargetNode} = info;
                if(!isValid(targetNode)){
                    labelNode.destroy();
                    this._labelNodeInfos.splice(i, 1);
                    i--;
                    continue;
                }
                targetNode.getWorldPosition(this._tempPos);
                Vec3.add(this._tempPos, this._tempPos, posInTargetNode);
                labelNode.setWorldPosition(this._tempPos);
            }
        }
    }
}

interface LableNodeInfo {
    labelNode: Node;
    targetNode: Node;
    posInTargetNode: Vec3;
}
