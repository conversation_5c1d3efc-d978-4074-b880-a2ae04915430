import { _decorator, Component, Font, Node, SpriteFrame } from 'cc';
import { Tools } from './Tools';
import { ResManager } from '../manager/ResManager';
const { ccclass, property } = _decorator;

/**
 * 防止构建后主包过大
 * 把需要的资源放到预制体中
 * 场景加载成功后动态加载实例资源预制体到场景中
 */
@ccclass('DelayRes')
export class DelayRes extends Component {
    static instance: DelayRes = null;

    @property([SpriteFrame])
    imgs: SpriteFrame[] = [];
    @property([Font])
    fonts: Font[] = [];

    protected onLoad(): void {
        DelayRes.instance = this;

        this.pushToMap();
    }

    getSpriteFrameByName(name: string): SpriteFrame {
        return this.imgs.find(img=>img.name == name);
    }

    private pushToMap(){
        this.imgs.forEach(img=>{
            ResManager.instance.setAsset(img.name, img);
        });
    }

    getFontByName(name: string): Font {
        return this.fonts.find(font => font.name == name);
    }
}


