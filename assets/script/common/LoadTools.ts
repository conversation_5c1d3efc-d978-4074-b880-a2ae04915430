import { director, Node , instantiate, Prefab, Vec3 , v3, SpriteFrame, Sprite, AssetManager, resources, assetManager, error } from "cc";
import { ResManager } from "../manager/ResManager";

/** 加载资源相关工具类 */
export class LoadTools {
    /** 单例模式 */
    private static _instance: LoadTools = new LoadTools();
    private constructor() { }
    public static get _ins() {
        return this._instance;
    }

    /**
     * 加载游戏场景 
     * @param sceneName 加载场景的名字
     * @param callFunc 加载回调
     */
    loadScene(sceneName:string,callFunc:any,isClear = true){
        if(isClear){
            ResManager.instance.clearAssetsCache();
        }
        director.preloadScene(sceneName, () => {
            director.loadScene(sceneName,callFunc);
        });
    };
    /**
     *  加载resource 下的预制体 资源
     * @param url resource 下的资源路径
     * @param callBack 加载完成回调
     */
    loadResPrefab(url:string,callBack?:any,parent?:Node,Pos?:Vec3){
        this.loadResAny( url , Prefab , (prefab:Prefab) => {
            let clone = instantiate(prefab);
            if (parent) { parent.addChild(clone) };
            if (Pos) { clone.position = v3(Pos.x,Pos.y,0) };
            if (callBack != null) {
                callBack(clone);
            }
        } )
    }
    /**
     * 加载resource 下的图片资源并渲染到节点上
     * @param url resource 下的资源路径
     * @param callBack 加载完成回调
     */
    loadResSpriteFrame(url:string, sprite :Node , parent?:Node, Pos?:Vec3, callBack?: any ){
        resources.load(url + "/spriteFrame",SpriteFrame,function(err:any,SpriteFrame:SpriteFrame){
            if(err){
                error(err);
            }else{
                sprite.getComponent(Sprite).spriteFrame = SpriteFrame;
                if (parent) { parent.addChild(sprite) };
                if (Pos) { sprite.position = v3(Pos.x,Pos.y,0) };
                if (callBack != null) {
                    callBack(sprite);
                }
            }
        });
    };
    /**
     * 加载resource 下的游戏资源
     * @param url resource 下的资源路径
     * @param resType 加载资源的类型
     * @param callBack 加载完成回调
     */
    loadResAny(url:string,resType:any,callBack?:any){
        resources.load(url,resType,function(error:any,res:any){
            if(error){
                error(error);
            }else{
                if(callBack != null){
                    callBack(res);
                }
            }
        });
    }
    /** 加载bundle 场景 */
    loadBundleScene(bundleName: string, sceneName: string, onFinshBack?: () => void , isInScene:boolean = true) {
        assetManager.loadBundle(
            bundleName,
            (err:any, bundle:AssetManager.Bundle) => {
                if (err) {
                    console.log(err);
                }
                else {
                    if(!isInScene) { return; }
                    bundle.loadScene(sceneName, (err, scene) => {
                        if (onFinshBack) {
                            onFinshBack();
                        }
                        director.runScene(scene);
                    });
                }
            }
        );
    }
}