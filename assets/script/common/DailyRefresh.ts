import { GameModel } from "../model/GameModel";

export const DailyRefreshItems = {
    launchFromSidebar: "launchFromSidebar",
    addToTable: "addToTable",
    /** 今日关卡数 */
    todayLevel: "todayLevel",
    /** 特殊菜关卡免费次数 */
    specialLevelFreeCount: "specialLevelFreeCount",
    /** 每日是否首次展示复活页面 */
    showReliveCount: "showReliveCount"
}

export class DailyRefresh {
    /** 
     * 记录指定行为，一天访问次数 
     * @param actName 要记录行为的名字
     * @param defaultNum 没有记录时，设定的默认值
     * @param isRecord 本次访问是否计数(传入false不计数代表获取值)
     * @returns 返回访问次数值
     */
    public static recordBehaviorNum(actName: string, defaultNum: number = 0, isRecord: boolean = true): number {
        let isNewDay = this.isNewDay(actName, isRecord);
        // 从本地存储获取上次记录的次数
        let enterCount = GameModel.instance.loadFromLocal<number>('dayEnterCount' + actName, defaultNum);
        // let enterCount = 0
        // 检查是否是同一天(是否在今天0点之后)
        if (isNewDay) {
            // 新的一天,重置计数
            if (isRecord) {
                enterCount = 1;
            } else {
                enterCount = 0;
            }
        } else {
            // 同一天内,计数加1
            if (isRecord) {
                enterCount++;
            }
        }
        // 保存最新的次数
        GameModel.instance.saveToLocal('dayEnterCount' + actName, enterCount);
        return enterCount;
    }

    /** 
     * 判断是否是新的一天 
     * @param actName 行为名字
     * @param isCover 本次访问是否覆盖掉上次访问时间记录
     */
    public static isNewDay(actName: string = "", isCover: boolean = false): boolean {
        // 获取当前时间戳
        let currentTime = new Date();

        // 获取当天0点的时间戳
        let todayStart = new Date(currentTime.getFullYear(), currentTime.getMonth(), currentTime.getDate()).getTime() / 1000;

        let key = 'lastAskTime' + actName;
        // 从本地存储获取上次记录的时间和次数
        let lastTime = GameModel.instance.loadFromLocal<number>(key, 0);
        // 使用最新时间覆盖旧的时间记录
        if (isCover)
            GameModel.instance.saveToLocal(key, currentTime.getTime() / 1000);
        // 检查是否是同一天(是否在今天0点之后)
        if (lastTime < todayStart) {
            // 新的一天,重置计数
            return true;
        }
        return false;
    }
}