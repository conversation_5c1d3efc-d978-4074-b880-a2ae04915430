import { _decorator, Component, Enum } from "cc";
import { releaseType } from "../Enum/Enum";
import { GameModel } from "../model/GameModel";

const {ccclass, property} = _decorator;

@ccclass("BlockWithPlatform")
export class BlockWithPlatform extends Component{
    @property({
        type: Enum(releaseType)
    })
    blockPlatform1: releaseType;
    @property({
        type: Enum(releaseType)
    })
    blockPlatform2: releaseType;
    @property({
        type: Enum(releaseType)
    })
    blockPlatform3: releaseType;

    start(){
        let platform = GameModel.instance.releaseType;
        let blockPlatforms = [this.blockPlatform1, this.blockPlatform2, this.blockPlatform3];
        if(blockPlatforms.includes(platform)){
            this.node.active = false;
        }
    }
}