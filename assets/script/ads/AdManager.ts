
import { releaseType } from "../Enum/Enum";
import { RuntimeData } from "../game/chiDaXi/data/GameData";
import { RemoteServerData } from "../game/chiDaXi/data/RemoteServerData";
import { GameModel } from "../model/GameModel";
import { AdManager_WX } from "./AdManager_WX";
import { AdManager_ZJ } from "./AdManager_ZJ";

export class AdManager {
    private static isLoadAd: boolean = false;   //是否加载过广告了

    /** 回到前台 回调 */
    private static onShowCallbackArr: Function[] = [];
    /** 切换后台 回调 */
    private static onHideCallbackArr: Function[] = [];
    /** 初始化所有广告 */
    public static initAds() {

    }
    /** 加载所有广告 */
    public static loadAds() {
        if (this.isLoadAd) { return; }
        if (GameModel.instance.releaseType == releaseType.applet_ziJie) {
            AdManager_ZJ._ins.loadAllAd();

            // 注册侧边栏检测事件
            this.onShow((res: any) => {
                AdManager_ZJ._ins.onCheckLaunchFromSidebar(res);
            });
        } else if (GameModel.instance.releaseType == releaseType.applet_wechat) {
            AdManager_WX._ins.loadAllAd();
        }

        this.isLoadAd = true;
    }
    /** Banner 广告 */
    public static showBanner() {
        if (GameModel.instance.releaseType == releaseType.applet_ziJie) {
            AdManager_ZJ._ins.showBanner();
        } else if (GameModel.instance.releaseType == releaseType.applet_wechat) {
            AdManager_WX._ins.showBanner();
        }
    }
    /** 隐藏 Banner 广告 */
    public static hideBanner() {
        if (GameModel.instance.releaseType == releaseType.applet_ziJie) {
            AdManager_ZJ._ins.hideBanner();
        } else if (GameModel.instance.releaseType == releaseType.applet_wechat) {
            AdManager_WX._ins.hideBanner();
        }
    }
    /** 插屏广告 */
    public static showIntersAd() {
        let rType = GameModel.instance.releaseType;
        if (rType == releaseType.test_TEST) { return; }

        if (rType == releaseType.h5_weiSan || rType == releaseType.h5_common) {
            //  AdManager_H5._ins.showIntersAd();
        } else if (rType == releaseType.applet_ziJie) {
            AdManager_ZJ._ins.showIntersAd();
        } else if (GameModel.instance.releaseType == releaseType.applet_wechat) {
            AdManager_WX._ins.showIntersAd();
        }
    }
    /**
     * 播放视频激励广告
     * @param finishBack 视频完成回调
     * @param errorBack  视频失败回调
     */
    public static showVideoAd(finishBack?: () => void, errorBack?: () => void) {
        let rType = GameModel.instance.releaseType;

        let newFinishBack = () => {
            finishBack();
            GameModel.instance.watchAdNum += 1;
        }

        if (rType == releaseType.test_TEST) {  //测试直接成功
            if (finishBack) { newFinishBack(); };
            return;
        }
        if (rType == releaseType.h5_weiSan || rType == releaseType.h5_common) {
            // AdManager_H5._ins.showVideoAd(finishBack,errorBack);
        } else if (rType == releaseType.applet_ziJie) {
            AdManager_ZJ._ins.showVideoAd(newFinishBack, errorBack);
        } else if (GameModel.instance.releaseType == releaseType.applet_wechat) {
            AdManager_WX._ins.showVideoAd(newFinishBack, errorBack);
        }
    }



    /**
     * 展示排行榜(官方UI)
     */
    public static showRankList() {
        let rType = GameModel.instance.releaseType;
        if (rType == releaseType.test_TEST) {  //测试直接成功
            return;
        }
        if (rType == releaseType.h5_weiSan || rType == releaseType.h5_common) {
            return;
        } else if (rType == releaseType.applet_ziJie) {
            AdManager_ZJ._ins.showRankList();
        } else if (rType == releaseType.applet_wechat) {
            console.log("功能有待提升");
        }
    }
    public static setRankData(val: any) {
        let rType = GameModel.instance.releaseType;
        if (rType == releaseType.test_TEST) {  //测试直接成功
            return;
        }
        if (rType == releaseType.h5_weiSan || rType == releaseType.h5_common) {
            return;
        } else if (rType == releaseType.applet_ziJie) {
            AdManager_ZJ._ins.setRankData(val);
        } else if (rType == releaseType.applet_wechat) {
            console.log("功能有待提升");
        }
    }

    /**
     * 分享游戏
     */
    public static shareGame(shareTitle?: string, successCb?: Function) {
        let rType = GameModel.instance.releaseType;
        let newSuccessCb = () => {
            successCb && successCb();
            GameModel.instance.shareGameNum += 1;
        }
        if (rType == releaseType.test_TEST) {  //测试直接成功
            newSuccessCb && newSuccessCb();
            return;
        }
        if (rType == releaseType.h5_weiSan || rType == releaseType.h5_common) {
            return;
        } else if (rType == releaseType.applet_ziJie) {
            AdManager_ZJ._ins.shareGame(shareTitle, newSuccessCb.bind(this));
        } else if (rType == releaseType.applet_wechat) {
            // console.log("功能有待提升");
            AdManager_WX._ins.shareFriends(shareTitle, newSuccessCb.bind(this));
        }
    }
    /**
     * 分享游戏视频
     */
    public static shareGameVideo(shareTopics?: [string], shareTitle?: string) {
        let rType = GameModel.instance.releaseType;
        if (rType == releaseType.test_TEST) {  //测试直接成功
            return;
        }
        if (rType == releaseType.h5_weiSan || rType == releaseType.h5_common) {
            return;
        } else if (rType == releaseType.applet_ziJie) {
            AdManager_ZJ._ins.shareScreenVideo(shareTopics, shareTitle);
        } else if (rType == releaseType.applet_wechat) {
            console.log("功能有待提升");
        }
    }

    /**
     * 开始录制游戏视频
     */
    public static createGameVideo() {
        let rType = GameModel.instance.releaseType;
        if (rType == releaseType.test_TEST) {  //测试直接成功
            return;
        }
        if (rType == releaseType.h5_weiSan || rType == releaseType.h5_common) {
            return;
        } else if (rType == releaseType.applet_ziJie) {
            AdManager_ZJ._ins.createVideoScreen();
        } else if (rType == releaseType.applet_wechat) {
            console.log("功能有待提升");
        }
    }

    /**
     * 结束录制游戏视频
     */
    public static stopGameVideo() {
        let rType = GameModel.instance.releaseType;
        if (rType == releaseType.test_TEST) {  //测试直接成功
            return;
        }
        if (rType == releaseType.h5_weiSan || rType == releaseType.h5_common) {
            return;
        } else if (rType == releaseType.applet_ziJie) {
            AdManager_ZJ._ins.stopVideoScreen();
        } else if (rType == releaseType.applet_wechat) {
            console.log("功能有待提升");
        }
    }

    public static onShow(callFunc: Function) {
        if (this.onShowCallbackArr.indexOf(callFunc) == -1) {
            this.onShowCallbackArr.push(callFunc);
        }

        let rType = GameModel.instance.releaseType;
        if (rType == releaseType.applet_ziJie) {
            AdManager_ZJ._ins.onShow(this.onShowCallbackArr);
        } else if (rType == releaseType.applet_wechat) {
            AdManager_WX._ins.onShow(this.onShowCallbackArr);
        }
    }
    public static onHide(callFunc: Function) {
        if (this.onHideCallbackArr.indexOf(callFunc) == -1) {
            this.onHideCallbackArr.push(callFunc);
        }

        let rType = GameModel.instance.releaseType;
        if (rType == releaseType.applet_ziJie) {
            AdManager_ZJ._ins.onHide(this.onHideCallbackArr);
        } else if (rType == releaseType.applet_wechat) {
            AdManager_WX._ins.onHide(this.onHideCallbackArr);
        }
    }

    public static shortVibrate() {
        if (!GameModel.instance.vibrateFlag) return;

        if (GameModel.instance.releaseType == releaseType.applet_wechat) {
            AdManager_WX._ins.shortVibrate();
        } else if (GameModel.instance.releaseType == releaseType.applet_ziJie) {
            AdManager_ZJ._ins.shortVibrate();
        }
    }

    public static isIPhone() {
        if (GameModel.instance.releaseType == releaseType.applet_wechat) {
            // return AdManager_WX._ins.isIPhone();
        } else if (GameModel.instance.releaseType == releaseType.applet_ziJie) {
            return AdManager_ZJ._ins.isIPhone();
        }
        return false;
    }

    public static async getOpenid() {
        if (RuntimeData._ins.openid) {
            return RuntimeData._ins.openid;
        }
        let code: string = null;
        if (GameModel.instance.releaseType == releaseType.applet_wechat) {
            code = await AdManager_WX._ins.getLoginCode();
        } else if (GameModel.instance.releaseType == releaseType.applet_ziJie) {
            code = await AdManager_ZJ._ins.getLoginCode();
        }
        let openid = await RemoteServerData.instance.posttwx(code);
        RuntimeData._ins.openid = openid;
        return openid;
    }
}