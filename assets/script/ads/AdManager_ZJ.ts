import { DailyRefresh, DailyRefreshItems } from "../common/DailyRefresh";
import { releaseType } from "../Enum/Enum";
import { RuntimeData } from "../game/chiDaXi/data/GameData";
import { RemoteServerData } from "../game/chiDaXi/data/RemoteServerData";
import { GameModel } from "../model/GameModel";


export class AdManager_ZJ {
    /** 单例模式 */
    private static _instance: AdManager_ZJ = new AdManager_ZJ();
    private constructor() { }
    public static get _ins() {
        return this._instance;
    }
    /** appID */
    app_id: string = "ttdfbca90140814b7602";
    /** 插屏ID */
    ad_inter_id: string = "2mrqugugp7e9f29c36";
    /** 激励ID */
    ad_video_id: string = "28hl12ib131ikqshns";
    /** bannerID */
    ad_banner_id: string = "5qmmte1agjm5fjd50r";

    /** 录屏相关 */
    recorder: any = null;
    videoPath: any = null;   //录屏路径
    videoTimer: any = null;   //录屏计时器 300s 内要停止录屏

    ad_banner: any = null;  //banner广告;
    ad_video: any = null;  //视频广告;

    videoBack: () => void | null;  //视频广告完成回调
    errorBack: (isOut: boolean) => void | null;  //视频广告失败回调

    /** 设备信息 */
    deviceInfo: any;

    /** 加载或者初始化所有广告 */
    loadAllAd() {
        if (GameModel.instance.releaseType != releaseType.applet_ziJie) { return; }

        this.initBanner();
        this.initVideoAd();
        this._initDeviceInfo();
    };
    /** 初始化加载 视频广告 */
    private initVideoAd() {
        let self = this;
        this.ad_video = tt.createRewardedVideoAd({
            adUnitId: self.ad_video_id,
        });

        this.ad_video.onLoad(() => {
            console.log("视频广告加载完成!");
        });
        this.ad_video.load();
    };
    /** 初始化加载 banner */
    private initBanner() {
        let self = this;
        let iphoneData = tt.getSystemInfoSync();
        var bannerData = {
            left: iphoneData.screenWidth,//广告位区域左上角横坐标
            top: iphoneData.screenHeight,//广告位区域左上角纵坐标
            width: iphoneData.screenWidth,//广告位区域宽度
        }

        this.ad_banner = tt.createBannerAd({
            adUnitId: self.ad_banner_id,
            adIntervals: 20,
            style: bannerData,
        });
    };
    /** 显示Banner广告 */
    public showBanner() {
        if (GameModel.instance.releaseType != releaseType.applet_ziJie) { return; }
        if (!this.ad_banner) {
            this.initBanner();
            // return;
        };

        if (this.ad_banner.show) {
            this.ad_banner.show();
        }

        this.ad_banner.onLoad(() => {
            this.ad_banner.show().then(() => {
                console.log("广告显示成功");
            }).catch((err: any) => {
                console.log("广告组件出现问题", err);
                this.ad_banner = null;
            });
        });
    };
    /** 隐藏Banner广告 */
    public hideBanner() {
        if (GameModel.instance.releaseType != releaseType.applet_ziJie) { return; }
        if (!this.ad_banner) { return; }
        this.ad_banner.hide();
    };
    /** 播放插屏广告 */
    public showIntersAd() {
        if (GameModel.instance.releaseType != releaseType.applet_ziJie) { return; }
        let self = this;
        var interstitialAd = tt.createInterstitialAd({
            adUnitId: self.ad_inter_id,
        });
        interstitialAd.load()
            .then(() => {
                interstitialAd.show().then(() => {
                    console.log("插屏广告展示成功");
                });
            }).catch((err: any) => {
                console.log(err);
            });
        console.log("showIntersAd");
    }
    /** 播放视频广告  成功回调   失败回调 */
    public showVideoAd(finishBack?: () => void, errorBack?: () => void) {
        if (GameModel.instance.releaseType != releaseType.applet_ziJie) { return; }
        this.videoBack = null;
        this.errorBack = null;
        if (finishBack) {
            this.videoBack = finishBack;
        } if (errorBack) {
            this.errorBack = errorBack;
        }

        if (!this.ad_video) {
            this.initVideoAd();
        }

        this.ad_video.show().then(() => {
            console.log("广告显示成功");
        }).catch((err: any) => {
            this.errorVideo();
            console.log("广告组件出现问题", err);
            // 再手动加载一次
            this.ad_video.load().then(() => {
                console.log("手动加载成功");
                this.ad_video.show();  // 加载成功后需要再显示广告
            });
        });

        this.ad_video.onClose((res: any) => {
            if (res.isEnded) {
                console.log("获取奖励")
                this.finishVideo();
            } else {
                console.log("没有观看完毕--")
                this.errorVideo(true);
            }
            if (res.count) {
                //在支持多例模式的版本上会返回该字段，并且是否返回该字段与multiton是否为true无关
                //判断观看了几次广告
            }
        });
    }
    /** 视频播放完成 */
    finishVideo() {
        if (this.videoBack) {
            this.videoBack();
        }
        this.videoBack = null;
        this.errorBack = null;
    }
    /** 视频播放失败 isOut 是否中途退出*/
    errorVideo(isOut: boolean = false) {
        if (this.errorBack) {
            this.errorBack(isOut);
        }
        this.videoBack = null;
        this.errorBack = null;
    };


    /** 开始录屏 */
    createVideoScreen() {
        if (GameModel.instance.releaseType != releaseType.applet_ziJie) { return; }
        if (this.videoTimer !== null) {
            clearTimeout(this.videoTimer);
            this.videoTimer = null;
        }
        var self = this;

        this.recorder = tt.getGameRecorderManager();
        this.recorder.onStart((s: any) => {
            console.log("开始录屏:", s);
        });
        this.recorder.onError((s: any) => {
            console.log("录屏错误:", s);
        });
        this.recorder.start({
            duration: 300
        });

        this.videoTimer = setTimeout(() => {
            self.stopVideoScreen();
        }, 1000 * 280);  //280s 后停止录屏
    };
    /** 停止录屏 */
    stopVideoScreen() {
        if (GameModel.instance.releaseType != releaseType.applet_ziJie) { return; }
        if (this.videoTimer !== null) {
            clearTimeout(this.videoTimer);
            this.videoTimer = null;
        }
        var self = this;

        console.log(this.recorder)
        if (!this.recorder || !this.recorder.stop) { return; }

        this.recorder.onStop((s: any) => {
            self.videoPath = s.videoPath;
        });
        this.recorder.stop();
    };
    /** 分享视频   shareTopics 分享话题  shareTitle 分享内容 */
    shareScreenVideo(shareTopics?: [string], shareTitle?: string) {
        if (GameModel.instance.releaseType != releaseType.applet_ziJie) { return; }
        shareTopics = shareTopics || ['吃大席'];
        shareTitle = shareTitle || '速速干饭~~';

        var self = this;
        tt.shareAppMessage({
            channel: 'video',
            title: shareTitle,
            imageUrl: '',
            query: '',
            extra: {
                videoPath: self.videoPath, // 可用录屏得到的视频地址
                videoTopics: shareTopics
            },
            success() {
                // EventManager.dispachEvent(EventData.SHARE_SUCESS);  //抛出分享成功的事件
                console.log('分享视频成功');
            },
            fail(e: any) {
                console.log('分享视频失败' + e);
            }
        });
        console.log("shareScreenVideo")
    };
    /** 添加更多游戏 */
    addMoreGame() {
        if (GameModel.instance.releaseType != releaseType.applet_ziJie) { return; }

        setTimeout(() => {
            let iphoneData = tt.getSystemInfoSync();
            console.log(iphoneData);
            tt.showGridGamePanel({
                query: {   //Json 格式
                    '花花僵尸': 'ttd12aa7974e142ca002'
                    // '套个甜甜圈神龙版': 'tt3fa54918a09c3fc802',
                    // '山楂串': 'ttcf15b9a8502cccbb02',
                    // '合成大西瓜原创版': 'tt425534e8dd6e24d1'
                },
                type: 'one',  // 'four', 'two'
                size: 'medium',
                position: {
                    top: iphoneData.screenHeight / 2 - 70,
                    left: iphoneData.screenWidth - 70,
                },
                fail(res: any) {
                    console.log(res);
                }
            });
        }, 100);
    };
    /** 隐藏更多游戏 */
    hideMoreGame() {
        if (GameModel.instance.releaseType != releaseType.applet_ziJie) { return; }

        tt.hideGridGamePanel();
    };
    /** 添加桌面功能 */
    addTable(successCb: Function) {
        if (GameModel.instance.releaseType != releaseType.applet_ziJie) { return; }

        tt.addShortcut({
            success: function (res: any) {
                console.log("添加桌面成功！" + res);
                // EventManager.dispachEvent(EventData.ADD_TABLE_SUCESS);
                successCb && successCb();
            },
            fail: function (res: any) {
                console.log("添加桌面失败！" + res);
            }
        });
    }
    /** 是否已经添加过桌面了 */
    isAddTable() {
        if (GameModel.instance.releaseType != releaseType.applet_ziJie) { return; }

        tt.checkShortcut({
            success: function (res: any) {
                console.log(res.status);
                if (res.status.exist) {
                    console.log("已经添加桌面了")
                }
            },
            fail: function (res: any) {
            }
        });
    }

    public setRankData(value: string) {
        let reType = GameModel.instance.releaseType;
        if (reType != releaseType.applet_ziJie) { return; }
        let appInfoName = tt.getSystemInfoSync().appName;
        if (appInfoName == 'Toutiao') {
            return
        }

        console.log("上传成绩：", value);
        tt.setImRankData({
            dataType: 0, //成绩为数字类型
            value, //该用户得了999999分
            priority: 0, //dataType为数字类型，不需要权重，直接传0
            extra: "extra",
            success(res) {
                console.log(`setImRankData success res: ${res}`);
            },
            fail(res) {
                console.log(`setImRankData fail res: ${res.errMsg}`);
            },
        });
    }

    /**
     * 展示排行榜
     */
    public showRankList() {
        let reType = GameModel.instance.releaseType;
        if (reType != releaseType.applet_ziJie) { return; }
        let appInfoName = tt.getSystemInfoSync().appName;
        if (appInfoName == 'Toutiao') {
            return
        }

        tt.login({
            force: true,
            success(res) {
                console.log(`login 调用成功${res.code} ${res.anonymousCode}`);
                tt.getImRankList({
                    relationType: "default", // 都展示
                    dataType: 0, //只圈选type为数字类型的数据进行排序
                    rankType: "week", //每月1号更新，只对当月1号到现在写入的数据进行排序
                    suffix: "关", //数据后缀，成绩后续默认带上 “分”
                    rankTitle: "闯关总数", //标题
                    success(res) {
                        console.log(`getImRankData success res: ${res}`);
                    },
                    fail(res) {
                        console.log(`getImRankData fail res: ${res.errMsg}`);
                    },
                });
            },
            fail(res) {
                console.log(`login 调用失败`);
            },
        });
    }

    /** 分享app   shareTitle 分享内容 */
    shareGame(shareTitle?: string, successCb?: Function) {
        let rType = GameModel.instance.releaseType;
        if (rType != releaseType.applet_ziJie) { return; }
        shareTitle = shareTitle || '开席~~';
        let shareIds = [
            "530cci06m9kde19m6j",
            "6dubh7ome542buooml",
            "4gbm8f3i228h6dc53e",
            "6ecd99cbe4a29kbkgc",
            "55ff0e4c5ff31vi9hh",
            "5glhj5jjecmcm70m6q",
            "45360fv0r4d79d0dhb",
            "4d1unvo2nii27rnai8"
        ];
        let randomShareId = shareIds[Math.floor(Math.random() * shareIds.length)];
        var self = this;
        tt.shareAppMessage({
            channel: 'invite',
            title: shareTitle,
            imageUrl: '',
            templateId: randomShareId, // 替换成通过审核的分享ID
            query: '',
            extra: {},
            success() {
                // EventManager.dispachEvent(EventData.SHARE_SUCESS);  //抛出分享成功的事件
                console.log('分享游戏成功');
                successCb && successCb();
            },
            fail(e: any) {
                console.log('分享游戏失败' + e);
            }
        });
    }

    /** 进入侧边栏 */
    public enterSidebar() {
        let rType = GameModel.instance.releaseType;
        if (rType != releaseType.applet_ziJie) { return; }

        tt.navigateToScene({
            scene: "sidebar",
            success: (res) => {
                console.log("navigate to scene success");
            },
            fail: (res) => {
                console.log("navigate to scene fail: ", res);
                // 跳转失败回调逻辑
            },
        });
    }

    private _isLaunchFromSidebar: boolean = false;
    public onCheckLaunchFromSidebar(res: any) {
        // 注册恢复到前台事件，判定是否在侧边栏启动

        if (res.launch_from && res.launch_from == "homepage") {
            this._isLaunchFromSidebar = true;
            console.log("从侧边栏启动", res.launch_from, res.launch_from.exist);
            let actName = DailyRefreshItems.launchFromSidebar;
            this.getReward(actName);
        } else {
            console.log("非侧边栏进入", res);
            this._isLaunchFromSidebar = false;
        }
    }

    /**领取奖励 */
    public getReward(actName: string) {
        if (DailyRefresh.recordBehaviorNum(actName, 0, true) <= 1) {
            let prop = RuntimeData._ins.getRandomProp();
            console.log("奖励领取成功!");
        } else {
            console.log(`今日已经领取过 ${actName} 奖励了！`);
        }
    }

    public onShow(callFuncArr?: Function[]) {
        tt.onShow((res) => {
            console.log("抖音回到前台");
            callFuncArr && callFuncArr.forEach(func => {
                func && func(res);
            });
        });
    }
    public onHide(callFuncArr?: Function[]) {
        tt.onHide(() => {
            console.log("抖音进入后台");
            callFuncArr && callFuncArr.forEach(func => {
                func && func();
            });
        });
    }

    public shortVibrate() {
        tt.vibrateShort({
            success(res) {
                console.log(`${res}`);
            },
            fail(res) {
                console.log(`vibrateShort调用失败`);
            },
        });
    }

    /** 初始化设备信息 */
    private _initDeviceInfo() {
        try {
            var res = tt.getSystemInfoSync();
            this.deviceInfo = res;
            console.log(`手机型号为 ${res.brand}`);
        } catch (error) {
            console.log(`获取系统信息失败`);
        }
    }

    /** 判断是否是IPhone */
    isIPhone() {
        return this.deviceInfo && (this.deviceInfo.brand == 'iPhone' || this.deviceInfo.brand == 'Apple');
    }

    /** 获取openid */
    getLoginCode(): Promise<string> {
        return new Promise((resolve, reject) => {
            tt.login({
                force: true,
                success(res) {
                    // console.log(`获取openid 时的 ${res} code ${res.code}`);
                    return resolve(res.code);
                },
                fail(res) {
                    console.log(`login 调用失败`);
                    return reject(null);
                },
            });
        })
    }
}