import { _decorator, Component, Node } from 'cc';
import Singleton from '../base/Singleton';
const { ccclass, property } = _decorator;

@ccclass('GameCtrl')
export class GameCtrl extends Singleton {
    //==================== 公有属性 ====================
    public canTouch: boolean = false;
    public gameIsOver: boolean = false;
    //==================== 单例相关 ====================
    static get instance() {
        return super.getInstance<GameCtrl>()
    }
}


