//声明文件
declare interface Window{
    win_Height:number,
    win_Width:number,
    PlatformManager:any,
    NetworkManager:any,
    AdManager:any,
}

/** h5 打包后的相关 声明 */
declare var adBreak:any;
declare var loadInScene:any;
// declare var noAdGoToScene:any;
// declare var adCompleteFlag:any;
// declare var resCompleteFlag:any;
// declare var preloader:any;
// declare var showMyAds:any;
// declare var destroyClickedElement:any;
/** 抖音 声明 */
declare var tt:any;
/** 微信 声明 */
declare var wx:any;
/** 快手 声明 */
declare var ks:any;

