{"ver": "1.0.27", "importer": "image", "imported": true, "uuid": "744c19a1-6c8a-4399-a3e0-fec569638787", "files": [".json", ".png"], "subMetas": {"6c48a": {"importer": "texture", "uuid": "744c19a1-6c8a-4399-a3e0-fec569638787@6c48a", "displayName": "客人", "id": "6c48a", "name": "texture", "userData": {"wrapModeS": "clamp-to-edge", "wrapModeT": "clamp-to-edge", "imageUuidOrDatabaseUri": "744c19a1-6c8a-4399-a3e0-fec569638787", "isUuid": true, "visible": false, "minfilter": "linear", "magfilter": "linear", "mipfilter": "none", "anisotropy": 0}, "ver": "1.0.22", "imported": true, "files": [".json"], "subMetas": {}}, "f9941": {"importer": "sprite-frame", "uuid": "744c19a1-6c8a-4399-a3e0-fec569638787@f9941", "displayName": "客人", "id": "f9941", "name": "spriteFrame", "userData": {"trimType": "auto", "trimThreshold": 1, "rotated": false, "offsetX": 0, "offsetY": 0, "trimX": 0, "trimY": 0, "width": 92, "height": 52, "rawWidth": 92, "rawHeight": 52, "borderTop": 0, "borderBottom": 0, "borderLeft": 0, "borderRight": 0, "packable": true, "pixelsToUnit": 100, "pivotX": 0.5, "pivotY": 0.5, "meshType": 0, "vertices": {"rawPosition": [-46, -26, 0, 46, -26, 0, -46, 26, 0, 46, 26, 0], "indexes": [0, 1, 2, 2, 1, 3], "uv": [0, 52, 92, 52, 0, 0, 92, 0], "nuv": [0, 0, 1, 0, 0, 1, 1, 1], "minPos": [-46, -26, 0], "maxPos": [46, 26, 0]}, "isUuid": true, "imageUuidOrDatabaseUri": "744c19a1-6c8a-4399-a3e0-fec569638787@6c48a", "atlasUuid": ""}, "ver": "1.0.12", "imported": true, "files": [".json"], "subMetas": {}}}, "userData": {"type": "sprite-frame", "hasAlpha": true, "fixAlphaTransparencyArtifacts": false, "redirect": "744c19a1-6c8a-4399-a3e0-fec569638787@6c48a"}}