
import { _decorator, Component, Node, view, macro, v3, Vec3, EventTouch, Skeleton, sp } from 'cc';

import { GameCtrl_MaiBing } from '../ctrl/GameCtrl_MaiBing';

import { GameModel_MaiBing } from '../model/GameModel_MaiBing';


const { ccclass, property } = _decorator;

@ccclass('MainGame')
export class MainGame extends Component {


    /** 屏幕宽度 */
    gameWidth: number;
    /** 屏幕高度 */
    gameHeight: number;

    onLoad() {

        this.gameWidth = view.getVisibleSize().width;
        this.gameHeight = view.getVisibleSize().height;
        GameModel_MaiBing._ins.mainGame = this;

    }

    start() {
        this.initGame();
    }

    update(deltaTime: number) {
        // [4]
    }
    
    /** 初始化游戏 */
    initGame() {
        GameModel_MaiBing._ins.gameScore = 0;
        GameCtrl_MaiBing._ins.initGame();
        GameCtrl_MaiBing._ins.boolTouch = true;
    }
   

  

}

