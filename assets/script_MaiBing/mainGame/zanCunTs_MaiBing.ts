import { _decorator, Component, game, Node } from 'cc';
import { AdManager } from '../../script/ads/AdManager';
import { AudioManager } from '../../script/manager/AudioManager';
import { GameModel } from '../../script/model/GameModel';
import { GameModel_MaiBing } from '../model/GameModel_MaiBing';
const { ccclass, property } = _decorator;

@ccclass('zanCunTs')
export class zanCunTs extends Component {
    /**广告节点 */
    @property(Node)
    adNode: Node = null
    @property(Node)
    /**未解锁遮罩 */
    adMask: Node = null
    /**是否解锁*/
    isLock: boolean = false;
    start() {
        this.node.on(Node.EventType.TOUCH_START, this.touchFunc, this);
    }

    update(deltaTime: number) {

    }
    touchFunc() {
        if (!this.isLock || GameModel_MaiBing._ins.bgTs.hasWon || GameModel_MaiBing._ins.bgTs.isDead || GameModel_MaiBing._ins.bgTs.hasEnd) {
            return
        }
        AudioManager.pauseMusic()
        AdManager.showVideoAd(() => {
            console.log("广告播放成功")
            this.adNode.active = false
            this.adMask.active = false
            this.isLock = false
            AudioManager.stopMusic()
            AudioManager.playMusic("bgm_MaiBing", 1)
        }, () => {
            this.isLock = true
            AudioManager.stopMusic()
            AudioManager.playMusic("bgm_MaiBing", 1)
        });
    }
}

