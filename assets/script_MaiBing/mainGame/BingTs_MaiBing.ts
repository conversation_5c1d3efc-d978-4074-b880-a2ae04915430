import { _decorator, Component, Node, v3, Vec3 } from 'cc';
import { GameModel_MaiBing } from '../model/GameModel_MaiBing';
import { Tools_MaiBing } from '../common/Tools_MaiBing';
import { AudioManager } from '../../script/manager/AudioManager';
import { GameModel } from '../../script/model/GameModel';

const { ccclass, property } = _decorator;

@ccclass('BingTs')
export class BingTs extends Component {
    /**是否被触摸 */
    isCreate: boolean = false;
    /**饼的类型 */
    bingType: number = 0;
    /**是否移动走了 */
    isMove: boolean = false;
    start() {
        this.node.on(Node.EventType.TOUCH_START, this.touchFunc, this);
    }
    update(deltaTime: number) {

    }
    /**触摸函数 */
    touchFunc(touches) {
        console.log(GameModel_MaiBing._ins.bgTs.isCanTouch, GameModel_MaiBing._ins.bgTs.isMove, this.isCreate)
        if (GameModel_MaiBing._ins.bgTs.isFirstPlayGame == true && GameModel_MaiBing._ins.bgTs.finger != null) {
            let touchs: Vec3
            if (GameModel.instance.eatCakeAuto) {
                touchs = this.node.position;
            } else {
                touchs = Tools_MaiBing.getToNodePosForWorld(v3(touches.getUILocation().x, touches.getUILocation().y), GameModel_MaiBing._ins.bgTs.node);
            }
            let dis = Tools_MaiBing.getDistance(touchs, GameModel_MaiBing._ins.bgTs.finger.position)
            console.log(dis, "dis")
            if (dis > 80) {
                return
            } else {
                if (GameModel_MaiBing._ins.bgTs.finger.position.x == GameModel_MaiBing._ins.bgTs.savePanPos[1].x &&
                    GameModel_MaiBing._ins.bgTs.finger.position.y == GameModel_MaiBing._ins.bgTs.savePanPos[1].y) {
                    GameModel_MaiBing._ins.bgTs.finger.destroy()
                    GameModel_MaiBing._ins.bgTs.finger = null
                    GameModel_MaiBing._ins.bgTs.createFinger(GameModel_MaiBing._ins.bgTs.savePanPos[0])
                } else {
                    GameModel_MaiBing._ins.bgTs.finger.destroy()
                    GameModel_MaiBing._ins.bgTs.finger = null
                }
            }
        }
        // 基础状态检查
        if (GameModel_MaiBing._ins.bgTs.isCanTouch || this.isCreate) {
            return
        }
        // 更新最后点击时
        GameModel_MaiBing._ins.bgTs.isCanTouch = true;
        /**音效 */
        AudioManager.playSound("click_MaiBing", 1);
        GameModel_MaiBing._ins.bgTs.moveFunc(this.node);

    }

}

