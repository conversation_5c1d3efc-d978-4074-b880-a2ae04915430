import { _decorator, Component, director, Node, tween, UITransform, v3 } from 'cc';
import { GameModel_MaiBing } from '../model/GameModel_MaiBing';
import { Tools_MaiBing } from '../common/Tools_MaiBing';
import { sceneNamesEnum } from '../../script/game/chiDaXi/Index';
import { AdManager } from '../../script/ads/AdManager';
import { AdManager_ZJ } from '../../script/ads/AdManager_ZJ';

const { ccclass, property } = _decorator;

@ccclass('overUiTs')
export class overUiTs extends Component {
    /**背景 */
    @property(Node)
    bg: Node = null
    /**返回主页 */
    @property(Node)
    backMainBtn: Node = null
    /**重新开始 */
    @property(Node)
    againBtn: Node = null
    /**分享按钮 */
    @property(Node)
    shareBtn: Node = null
    /**录屏按钮 */
    @property(Node)
    luPingBtn: Node = null
    /**右边人物 */
    @property(Node)
    people1: Node = null
    /**右边人说的话 */
    @property(Node)
    word1: Node = null
    /** 左边人物 */
    @property(Node)
    people2: Node = null
    /**左边人说的话 */
    @property(Node)
    word2: Node = null
    /**失败图标 */
    @property(Node)
    Lose: Node = null

    start() {
        AdManager_ZJ._ins.stopVideoScreen()
        GameModel_MaiBing._ins.bgTs.isCanPlayAuto = false
        this.bg.getComponent(UITransform).width = GameModel_MaiBing._ins.bgTs.gameWidth
        this.bg.getComponent(UITransform).height = GameModel_MaiBing._ins.bgTs.gameHeight
        let num = Tools_MaiBing.random(0, 1)
        if (num > 0.5) {
            Tools_MaiBing.setSpriteFrame(this.people1, "overPeople1")
            Tools_MaiBing.setSpriteFrame(this.people2, "overPeople2")
            Tools_MaiBing.setSpriteFrame(this.word1, "word1")
            Tools_MaiBing.setSpriteFrame(this.word1, "word2")
        } else {
            Tools_MaiBing.setSpriteFrame(this.people1, "overPeople3")
            Tools_MaiBing.setSpriteFrame(this.people2, "overPeople4")
            Tools_MaiBing.setSpriteFrame(this.word1, "word3")
            Tools_MaiBing.setSpriteFrame(this.word1, "word4")
        }
    }

    update(deltaTime: number) {

    }



    /**返回主页回调 */
    backMainFunc() {
        this.node.destroy();
        director.loadScene(sceneNamesEnum.主页场景);
    }

    /**再玩一次回调 */
    againFunc() {
        this.scheduleOnce(() => {
            director.preloadScene(GameModel_MaiBing._ins.mainScene, () => {
                director.loadScene(GameModel_MaiBing._ins.mainScene);
            });
            this.node.destroy()
        }, 0.2);
    }


    /**录屏回调 */
    luPingFunc() {
        AdManager_ZJ._ins.shareScreenVideo()
    }
    /**分享按钮 */

    /**分享回调 */
    shareFunc() {
        AdManager.shareGame("吃大席")
    }
}

