import { _decorator, Component, Game, game, Label, Node, tween, UITransform, v3, Vec3 } from 'cc';
import { GameModel_MaiBing } from '../model/GameModel_MaiBing';
import { Tools_MaiBing } from '../common/Tools_MaiBing';
import { PlatformManager_MaiBing } from '../manager/PlatformManager_MaiBing';
import { AudioManager } from '../../script/manager/AudioManager';
import { RuntimeData } from '../../script/game/chiDaXi/data/GameData';
import { AdManager } from '../../script/ads/AdManager';
import { GameModel } from '../../script/model/GameModel';

const { ccclass, property } = _decorator;

@ccclass('winTs')
export class winTs extends Component {
    @property(Node)
    mask: Node = null
    @property(Node)
    light: Node = null
    @property(Node)
    people: Node = null
    @property(Node)
    complete: Node = null
    @property(Node)
    shareBtn: Node = null
    @property(Node)
    nextBtn: Node = null
    start() {
        /**音效 */
        AudioManager.pauseMusic()
        GameModel_MaiBing._ins.bgTs.levelLable.active = false
        GameModel_MaiBing._ins.bgTs.auto.active = false
        GameModel_MaiBing._ins.bgTs.chongZhi.active = false
        this.mask.getComponent(UITransform).height = GameModel_MaiBing._ins.bgTs.gameHeight
        this.mask.getComponent(UITransform).width = GameModel_MaiBing._ins.bgTs.gameWidth
        this.light.scale = v3(0, 0, 0)
        this.people.children[0].scale = v3(0, 0, 0)
        this.people.children[1].scale = v3(0, 0, 0)
        this.people.children[2].scale = v3(0, 0, 0)
        this.people.children[3].scale = v3(0, 0, 0)
        this.people.children[4].scale = v3(0, 0, 0)
        this.nextBtn.scale = v3(0, 0, 0)
        this.shareBtn.scale = v3(0, 0, 0)
        this.complete.scale = v3(0, 0, 0)
        this.action()
        /**音效 */
        AudioManager.playSound("success_MaiBing", 1)
        console.log(GameModel_MaiBing._ins.bgTs.saveTipFloor, "GameModel_MaiBing._ins.bgTs.saveTipFloor")
        GameModel_MaiBing._ins.bgTs.isCanPlayAuto = false
        // this.shareBtn.on(Node.EventType.TOUCH_START, this.share, this)
    }
    update(deltaTime: number) {

    }
    /**效果动画 */
    action() {
        tween(this.light)
            .by(1, { angle: -180 })
            .repeatForever()
            .start()
        tween(this.light)
            .to(0.3, { scale: v3(1, 1, 1) })
            .call(() => {
                this.createCaidai()
                tween(this.complete)
                    .to(0.3, { scale: v3(1.4, 1.4, 1.4) })
                    .call(() => {
                        tween(this.people.children[0])
                            .to(0.1, { scale: v3(1.2, 1.2, 1.2) })
                            .call(() => {
                                tween(this.people.children[0])
                                    .to(0.2, { scale: v3(1, 1, 1) })
                                    .by(0.2, { scale: v3(0., -0.12, 0) }, { easing: 'sineInOut' })
                                    .by(0.2, { scale: v3(0., 0.12, 0) }, { easing: 'sineInOut' })
                                    .union()
                                    .repeatForever()
                                    .start();
                            })
                            .start();
                        tween(this.people.children[1])
                            .to(0.1, { scale: v3(1.2, 1.2, 1.2) })
                            .call(() => {
                                tween(this.people.children[1])
                                    .to(0.2, { scale: v3(1, 1, 1) })
                                    .by(0.2, { scale: v3(0., -0.12, 0) }, { easing: 'sineInOut' })
                                    .by(0.2, { scale: v3(0., 0.12, 0) }, { easing: 'sineInOut' })
                                    .union()
                                    .repeatForever()
                                    .start();
                            })
                            .start();
                        tween(this.people.children[2])
                            .to(0.1, { scale: v3(1.2, 1.2, 1.2) })
                            .call(() => {
                                tween(this.people.children[2])
                                    .to(0.2, { scale: v3(1, 1, 1) })
                                    .by(0.2, { scale: v3(0., -0.12, 0) }, { easing: 'sineInOut' })
                                    .by(0.2, { scale: v3(0., 0.12, 0) }, { easing: 'sineInOut' })
                                    .union()
                                    .repeatForever()
                                    .start();
                            })
                            .start();
                        tween(this.people.children[3])
                            .to(0.1, { scale: v3(1.2, 1.2, 1.2) })
                            .call(() => {
                                tween(this.people.children[3])
                                    .to(0.2, { scale: v3(1, 1, 1) })
                                    .by(0.2, { scale: v3(0., -0.12, 0) }, { easing: 'sineInOut' })
                                    .by(0.2, { scale: v3(0., 0.12, 0) }, { easing: 'sineInOut' })
                                    .union()
                                    .repeatForever()
                                    .start();
                            })
                            .start();
                        tween(this.people.children[4])
                            .to(0.1, { scale: v3(1.2, 1.2, 1.2) })
                            .call(() => {
                                tween(this.people.children[4])
                                    .to(0.2, { scale: v3(1, 1, 1) })
                                    .by(0.2, { scale: v3(0., -0.12, 0) }, { easing: 'sineInOut' })
                                    .by(0.2, { scale: v3(0., 0.12, 0) }, { easing: 'sineInOut' })
                                    .union()
                                    .repeatForever()
                                    .start();
                            })
                            .start();
                    })
                    .to(0.2, { scale: v3(1.2, 1.2, 1.2) })
                    .call(() => {
                        tween(this.shareBtn)
                            .to(0.2, { scale: v3(1.48, 1.48, 1.48) })
                            .delay(0.1)
                            .to(0.1, { scale: v3(1.38, 1.38, 1.38) })
                            .delay(0.1)
                            .call(() => {
                                tween(this.nextBtn)
                                    .to(0.2, { scale: v3(1.48, 1.48, 1.48) })
                                    .delay(0.1)
                                    .to(0.1, { scale: v3(1.38, 1.38, 1.38) })
                                    .delay(1)
                                    .call(() => {
                                        if (GameModel_MaiBing._ins.bgTs.isAuto) {
                                            this.nextFunc()
                                        }
                                    })
                                    .start()
                            })
                            .start()
                    })
                    .start()
            })
            .start()

    }
    /**生成彩带 */
    createCaidai() {
        /**音效 */
        AudioManager.playSound("cd_MaiBing", 1)
        for (let i = 0; i < 60; i++) {
            let num = Tools_MaiBing.random(1, 8, true)
            let caidai = Tools_MaiBing.newPrefab("cd")
            caidai.setParent(this.node)
            caidai.setPosition(v3(0, 0))
            Tools_MaiBing.setSpriteFrame(caidai, "cd" + num.toString())
        }
    }
    /**下一关 */
    nextLevel() {
        if (GameModel_MaiBing._ins.bgTs.isFirstPlayGame == true) {
            GameModel_MaiBing._ins.bgTs.isFirstPlayGame = false
        }
        GameModel_MaiBing._ins.bgTs.levelLable.active = true
        GameModel_MaiBing._ins.bgTs.auto.active = true
        GameModel_MaiBing._ins.bgTs.chongZhi.active = true
        GameModel_MaiBing._ins.bgTs.level++
        RuntimeData._ins.eatCakeLevel = GameModel_MaiBing._ins.bgTs.level
        GameModel_MaiBing._ins.bgTs.levelLable.children[0].getComponent(Label).string = Number(GameModel_MaiBing._ins.bgTs.level).toString()
        GameModel_MaiBing._ins.bgTs.orders = []
        GameModel_MaiBing._ins.bgTs.pileConfigurations = []
        GameModel_MaiBing._ins.bgTs.isMove = false;
        GameModel_MaiBing._ins.bgTs.disPlayIndex = []
        GameModel_MaiBing._ins.bgTs.hasWon = false
        GameModel_MaiBing._ins.bgTs.bingLuoNum = 0
        GameModel_MaiBing._ins.bgTs.bingNum = 0
        GameModel_MaiBing._ins.bgTs.bingColor = 0
        GameModel_MaiBing._ins.bgTs.saveBing = []
        GameModel_MaiBing._ins.bgTs.saveZancunBing = []
        GameModel_MaiBing._ins.bgTs.savePeople = [null, null, null]
        GameModel_MaiBing._ins.bgTs.saveTip = [null, null, null, null]
        GameModel_MaiBing._ins.bgTs.saveTipFloor = [null, null, null, null]
        GameModel_MaiBing._ins.bgTs.tipPos = []
        GameModel_MaiBing._ins.bgTs.savePanPos = []
        GameModel_MaiBing._ins.bgTs.saveZancunPos = []
        GameModel_MaiBing._ins.bgTs.savePeoplePos = []
        GameModel_MaiBing._ins.bgTs.savePan = []
        GameModel_MaiBing._ins.bgTs.saveZancun = []
        GameModel_MaiBing._ins.bgTs.saveTipColors = [null, null, null, null]
        GameModel_MaiBing._ins.bgTs.zanCunAnimationState = [0, 0, 0, 0, 0, 0]
        GameModel_MaiBing._ins.bgTs.createPanPos();
        GameModel_MaiBing._ins.bgTs.createZancunPos();
        GameModel_MaiBing._ins.bgTs.createPeoplePos();
        GameModel_MaiBing._ins.bgTs.createZancun();
        GameModel_MaiBing._ins.bgTs.createPan();
        GameModel_MaiBing._ins.bgTs.setLevel();
        /**音效 */
        AudioManager.playMusic("bgm_MaiBing", 1)
    }

    nextFunc() {
        // 遍历所有子节点,除了mask以外都缩小消失
        this.node.children.forEach(child => {
            if (child.name !== 'mask') {
                tween(child)
                    .delay(0.2)
                    .to(0.3, { scale: v3(0, 0, 0) }, { easing: 'sineIn' })
                    .delay(0.1)
                    .call(() => {
                        this.node.destroy()
                        this.nextLevel();
                    })
                    .start();
            }
        });
    }
    /**分享按钮事件 */
    // share() {
    //     tween(this.share)
    //         .to(0.1, { scale: v3(1.2, 1.2, 1.2) })
    //         .to(0.1, { scale: v3(1, 1, 1) })
    //         .delay(0.5)
    //         .call(() => {
    //             this.shareFunc()
    //         })
    //         .start()
    // }
    shareFunc() {
        AdManager.shareGame("吃大席");
    }
}
