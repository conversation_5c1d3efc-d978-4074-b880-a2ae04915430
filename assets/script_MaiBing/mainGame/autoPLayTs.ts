import { _decorator, Component, Node } from 'cc';
import { GameModel_MaiBing } from '../model/GameModel_MaiBing';
import { BingTs } from './BingTs_MaiBing';
import { peopleTs } from './peopleTs_MaiBing';
import { kuangTs } from './kuangTs_MaiBing';
const { ccclass } = _decorator;

@ccclass('autoPLayTs')
export class autoPLayTs extends Component {

    /**操作间隔 */
    playTime: number = 2;
    /**操作步数 */
    playStep: number = 0
    start() {

    }

    update(deltaTime: number) {
        console.log(!GameModel_MaiBing._ins.bgTs.isAuto || !GameModel_MaiBing._ins.bgTs.isCanPlayAuto, "!GameModel_MaiBing._ins.bgTs.isAuto || !GameModel_MaiBing._ins.bgTs.isCanPlayAuto")
        if (!GameModel_MaiBing._ins.bgTs.isAuto || !GameModel_MaiBing._ins.bgTs.isCanPlayAuto) { return }
        this.playTime -= deltaTime
        if (this.playTime <= 0) {
            this.playStep++
            this.autoPlay()
            this.playTime = 1
        }
    }

    /**自动操作逻辑 */
    autoPlay() {
        /**教学关时的操作逻辑 */
        if (GameModel_MaiBing._ins.bgTs.isFirstPlayGame) {
            if (this.playStep == 1) {
                GameModel_MaiBing._ins.bgTs.saveBing[1][0].getComponent(BingTs).touchFunc(GameModel_MaiBing._ins.bgTs.saveBing[1][0].position)
            }
            if (this.playStep == 2) {
                GameModel_MaiBing._ins.bgTs.saveBing[0][0].getComponent(BingTs).touchFunc(GameModel_MaiBing._ins.bgTs.saveBing[0][0].position)
            }
            if (this.playStep > 2) {
                for (let i = GameModel_MaiBing._ins.bgTs.saveBing.length - 1; i >= 0; i--) {
                    if (GameModel_MaiBing._ins.bgTs.saveBing[i].length > 0) {
                        GameModel_MaiBing._ins.bgTs.saveBing[i][0].getComponent(BingTs).touchFunc(GameModel_MaiBing._ins.bgTs.saveBing[i][0].position)
                        break;
                    }
                }
            }
        }

        /**普通关卡时的操作逻辑 */

        if (!GameModel_MaiBing._ins.bgTs.isFirstPlayGame) {
            this.autoDeliveryLogic();
        }
    }

    /**
     * 自动交付逻辑：遍历顾客需求，找到最适合交付的饼摞
     */
    autoDeliveryLogic() {
        let bgTs = GameModel_MaiBing._ins.bgTs;

        // 如果正在移动或者不能触摸，则跳过
        console.log(bgTs.isMove, bgTs.isCanTouch, "bgTs.isMove || !bgTs.isCanTouch")
        if (bgTs.isMove || !bgTs.isCanTouch) {
            return;
        }

        // 收集所有顾客的需求信息
        let customerNeeds = this.collectCustomerNeeds();
        console.log(customerNeeds.length, "customerNeeds.length")
        if (customerNeeds.length === 0) {
            return; // 没有顾客需求
        }

        // 评估所有饼摞的适配度
        let bestDelivery = this.findBestDeliveryOption(customerNeeds);
        console.log(bestDelivery)
        if (bestDelivery) {
            // 执行最佳交付
            this.executeBestDelivery(bestDelivery);
        }
    }

    /**
     * 收集所有顾客的需求信息
     */
    collectCustomerNeeds() {
        let bgTs = GameModel_MaiBing._ins.bgTs;
        let customerNeeds = [];

        for (let peopleIndex = 0; peopleIndex < bgTs.savePeople.length; peopleIndex++) {
            let people = bgTs.savePeople[peopleIndex];
            if (!people) continue;

            let kuang = people.getComponent(peopleTs).kuang;
            if (!kuang) continue;

            let kuangT = kuang.getComponent(kuangTs);
            let needBingType = kuangT.needBingType;
            let bingNum = kuangT.bingNum;

            if (!needBingType || !bingNum) continue;

            // 计算该顾客的完成度和剩余需求
            let totalNeeded = 0;
            let totalRemaining = 0;
            let activeNeeds = [];

            for (let needIndex = 0; needIndex < needBingType.length; needIndex++) {
                if (bingNum[needIndex] > 0 && bingNum[needIndex] !== -2) {
                    // 计算总需求量
                    if (kuangT.saveBing && kuangT.saveBing[needIndex]) {
                        totalNeeded += kuangT.saveBing[needIndex].length;
                    }
                    totalRemaining += bingNum[needIndex];

                    activeNeeds.push({
                        bingType: needBingType[needIndex],
                        needIndex: needIndex,
                        remainingCount: bingNum[needIndex]
                    });
                }
            }

            let completionRate = totalNeeded > 0 ? (totalNeeded - totalRemaining) / totalNeeded : 0;

            customerNeeds.push({
                peopleIndex: peopleIndex,
                completionRate: completionRate,
                totalRemaining: totalRemaining,
                activeNeeds: activeNeeds
            });
        }

        // 按完成度排序，优先处理完成度高的顾客
        customerNeeds.sort((a, b) => {
            if (Math.abs(a.completionRate - b.completionRate) < 0.01) {
                return a.totalRemaining - b.totalRemaining;
            }
            return b.completionRate - a.completionRate;
        });

        return customerNeeds;
    }

    /**
     * 找到最适合交付的饼摞选项
     */
    findBestDeliveryOption(customerNeeds: any[]) {
        let bgTs = GameModel_MaiBing._ins.bgTs;
        let bestOption = null;
        let bestScore = -1;

        // 遍历所有暂存区的饼摞
        for (let zancunIndex = 0; zancunIndex < bgTs.saveZancunBing.length; zancunIndex++) {
            let stack = bgTs.saveZancunBing[zancunIndex];
            if (!stack || stack.length === 0) continue;

            // 从摞顶开始检查每个饼
            for (let bingIndex = stack.length - 1; bingIndex >= 0; bingIndex--) {
                let bing = stack[bingIndex];
                let bingType = bing.getComponent(BingTs).bingType;

                // 计算这个饼摞位置的适配度
                let deliveryOption = this.evaluateDeliveryOption(
                    zancunIndex,
                    bingIndex,
                    bingType,
                    customerNeeds
                );

                if (deliveryOption && deliveryOption.score > bestScore) {
                    bestScore = deliveryOption.score;
                    bestOption = deliveryOption;
                }
            }
        }

        return bestOption;
    }

    /**
     * 评估特定饼摞位置的交付选项
     */
    evaluateDeliveryOption(zancunIndex: number, bingIndex: number, bingType: number, customerNeeds: any[]) {
        let bestCustomer = null;
        let bestScore = 0;

        // 遍历所有顾客需求，找到最匹配的
        for (let customer of customerNeeds) {
            for (let need of customer.activeNeeds) {
                if (need.bingType === bingType) {
                    // 计算适配度分数
                    let score = this.calculateAdaptationScore(
                        zancunIndex,
                        bingIndex,
                        customer,
                        need
                    );

                    if (score > bestScore) {
                        bestScore = score;
                        bestCustomer = {
                            peopleIndex: customer.peopleIndex,
                            needIndex: need.needIndex,
                            completionRate: customer.completionRate,
                            remainingCount: need.remainingCount
                        };
                    }
                }
            }
        }

        if (bestCustomer && bestScore > 0) {
            return {
                zancunIndex: zancunIndex,
                bingIndex: bingIndex,
                bingType: bingType,
                targetCustomer: bestCustomer,
                score: bestScore
            };
        }

        return null;
    }

    /**
     * 计算适配度分数
     * 考虑因素：
     * 1. 最上层颜色匹配度（权重最高）
     * 2. 下层颜色的适配度
     * 3. 顾客完成度优先级
     * 4. 可连续交付的饼数量
     */
    calculateAdaptationScore(zancunIndex: number, bingIndex: number, customer: any, need: any): number {
        let bgTs = GameModel_MaiBing._ins.bgTs;
        let stack = bgTs.saveZancunBing[zancunIndex];
        let score = 0;

        // 基础分数：最上层颜色匹配
        score += 100;

        // 顾客完成度加成：完成度越高，优先级越高
        score += customer.completionRate * 50;

        // 剩余需求量加成：需求量越少，优先级越高（接近完成的订单）
        score += (10 - Math.min(customer.totalRemaining, 10)) * 10;

        // 计算可连续交付的饼数量加成
        let consecutiveCount = this.getConsecutiveBingCount(stack, bingIndex, need.bingType);

        // 如果能完全满足该需求，给予大幅加成
        if (consecutiveCount >= need.remainingCount) {
            score += 200; // 完全满足加成
        } else {
            score += consecutiveCount * 20; // 部分满足加成
        }

        // 下层颜色适配度加成
        let lowerLayerScore = this.calculateLowerLayerAdaptation(stack, bingIndex, customer);
        score += lowerLayerScore;

        return score;
    }

    /**
     * 获取从指定位置开始连续相同类型饼的数量
     */
    getConsecutiveBingCount(stack: Node[], startIndex: number, bingType: number): number {
        let count = 0;
        for (let i = startIndex; i >= 0; i--) {
            let bing = stack[i];
            if (bing && bing.getComponent(BingTs).bingType === bingType) {
                count++;
            } else {
                break;
            }
        }
        return count;
    }

    /**
     * 计算下层颜色的适配度
     */
    calculateLowerLayerAdaptation(stack: Node[], currentIndex: number, customer: any): number {
        let adaptationScore = 0;

        // 检查当前位置下方的饼
        for (let i = currentIndex - 1; i >= 0; i--) {
            let bing = stack[i];
            if (!bing) continue;

            let bingType = bing.getComponent(BingTs).bingType;

            // 检查这种颜色是否是该顾客需要的
            for (let need of customer.activeNeeds) {
                if (need.bingType === bingType && need.remainingCount > 0) {
                    adaptationScore += 5; // 下层匹配加成
                    break;
                }
            }
        }

        return adaptationScore;
    }

    /**
     * 执行最佳交付选项
     */
    executeBestDelivery(deliveryOption: any) {
        let bgTs = GameModel_MaiBing._ins.bgTs;
        let stack = bgTs.saveZancunBing[deliveryOption.zancunIndex];

        if (!stack || stack.length === 0 || deliveryOption.bingIndex >= stack.length) {
            return;
        }

        // 获取要交付的饼
        let targetBing = stack[deliveryOption.bingIndex];
        if (!targetBing) {
            return;
        }

        // 模拟点击该饼来触发交付
        targetBing.getComponent(BingTs).touchFunc(targetBing.position);

        console.log(`自动交付: 饼摞${deliveryOption.zancunIndex}, 饼类型${deliveryOption.bingType}, 目标顾客${deliveryOption.targetCustomer.peopleIndex}, 适配度分数${deliveryOption.score}`);
    }
}
