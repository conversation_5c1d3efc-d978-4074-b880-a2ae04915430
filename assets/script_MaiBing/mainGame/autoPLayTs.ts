import { _decorator, Component, Node } from 'cc';
import { GameModel } from '../../script/model/GameModel';
import { GameModel_MaiBing } from '../model/GameModel_MaiBing';
import { BingTs } from './BingTs_MaiBing';
const { ccclass, property } = _decorator;

@ccclass('autoPLayTs')
export class autoPLayTs extends Component {

    /**操作间隔 */
    playTime: number = 2;
    /**操作步数 */
    playStep: number = 0
    start() {

    }

    update(deltaTime: number) {
        if (!GameModel_MaiBing._ins.bgTs.isAuto || !GameModel_MaiBing._ins.bgTs.isCanPlayAuto) { return }
        this.playTime -= deltaTime
        if (this.playTime <= 0) {
            this.playStep++
            this.autoPlay()
            this.playTime = 1
        }
    }

    /**自动操作逻辑 */
    autoPlay() {
        /**教学关时的操作逻辑 */
        if (GameModel_MaiBing._ins.bgTs.isFirstPlayGame) {
            if (this.playStep == 1) {
                GameModel_MaiBing._ins.bgTs.saveBing[1][0].getComponent(BingTs).touchFunc(GameModel_MaiBing._ins.bgTs.saveBing[1][0].position)
            }
            if (this.playStep == 2) {
                GameModel_MaiBing._ins.bgTs.saveBing[0][0].getComponent(BingTs).touchFunc(GameModel_MaiBing._ins.bgTs.saveBing[0][0].position)
            }
            if (this.playStep > 2) {
                for (let i = GameModel_MaiBing._ins.bgTs.saveBing.length - 1; i >= 0; i--) {
                    if (GameModel_MaiBing._ins.bgTs.saveBing[i].length > 0) {
                        GameModel_MaiBing._ins.bgTs.saveBing[i][0].getComponent(BingTs).touchFunc(GameModel_MaiBing._ins.bgTs.saveBing[i][0].position)
                        break;
                    }
                }
            }
        }

        /**普通关卡时的操作逻辑 */
        if (!GameModel_MaiBing._ins.bgTs.isFirstPlayGame) {




        }
    }
}
