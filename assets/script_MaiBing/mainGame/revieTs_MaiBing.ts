import { _decorator, Component, EventTouch, Node, settings, tween, UI, UITransform, v3 } from 'cc';
import { GameModel_MaiBing } from '../model/GameModel_MaiBing';
import { AdManager_ZJ } from '../../script/ads/AdManager_ZJ';
import { AdManager } from '../../script/ads/AdManager';
import { AudioManager } from '../../script/manager/AudioManager';
import { DailyRefresh, DailyRefreshItems } from '../../script/common/DailyRefresh';
const { ccclass, property } = _decorator;

@ccclass('revieTs')
export class revieTs extends Component {
    /**遮罩 */
    @property(Node)
    maskNode: Node = null
    /**背景 */
    @property(Node)
    bgNode: Node = null
    /**同意按钮 */
    @property(Node)
    yesBtn: Node = null
    /**分享按钮 */
    @property(Node)
    shareBtn: Node = null
    /**关闭按钮 */
    @property(Node)
    closeBtn: Node = null
    /**人 */
    @property(Node)
    people: Node = null
    /**气泡 */
    @property(Node)
    qiPao: Node = null
    /**说话的话语 */
    @property(Node)
    TalkKuang: Node = null
    /**分享的话语 */
    @property(Node)
    shareWord: Node = null
    /**看视频话语 */
    @property(Node)
    videoWord: Node = null
    start() {
        GameModel_MaiBing._ins.bgTs.isCanPlayAuto = false
        this.maskNode.getComponent(UITransform).width = GameModel_MaiBing._ins.bgTs.gameWidth
        this.maskNode.getComponent(UITransform).height = GameModel_MaiBing._ins.bgTs.gameHeight
        this.closeBtn.on(Node.EventType.TOUCH_START, this.closeFunc, this);
        this.yesBtn.on(Node.EventType.TOUCH_START, this.adYes, this)
        this.shareBtn.on(Node.EventType.TOUCH_START, this.shareYes, this)
        let num = DailyRefresh.recordBehaviorNum(DailyRefreshItems.showReliveCount, 0, true)
        console.log("今天分享过几次", num)
        // if (num <= 1) {
        //     this.videoWord.active = false
        //     this.yesBtn.active = false
        // } else {
        this.shareBtn.active = false
        this.shareWord.active = false
        // }

    }
    update(deltaTime: number) {

    }
    /**关闭按钮 */
    closeFunc() {
        tween(this.closeBtn)
            .to(0.1, { scale: v3(1.2, 1.2, 1.2) })
            .to(0.1, { scale: v3(1, 1, 1) })
            .call(() => {
                this.close()
            })
            .start()
    }
    close() {
        this.node.destroy()
        GameModel_MaiBing._ins.bgTs.openOverUi()
    }
    /**同意按钮 */
    adYes() {
        tween(this.yesBtn)
            .to(0.1, { scale: v3(1.2, 1.2, 1.2) })
            .to(0.1, { scale: v3(1, 1, 1) })
            .call(() => {
                // AudioManager.pauseMusic()
                // AdManager.showVideoAd(() => {
                this.agineGame()
                AudioManager.stopMusic()
                AudioManager.playMusic("bgm_MaiBing", 1)

                // }, () => {
                //     AudioManager.stopMusic()
                //     AudioManager.playMusic("bgm_MaiBing", 1)
                // });
            })
            .start()
    }
    agineGame() {
        this.node.destroy()
        GameModel_MaiBing._ins.bgTs.isRevie = true
        GameModel_MaiBing._ins.bgTs.isDead = false
        GameModel_MaiBing._ins.bgTs.hasEnd = false
        GameModel_MaiBing._ins.bgTs.resetOrder()
        GameModel_MaiBing._ins.bgTs.isCanPlayAuto = true
    }


    /**同意按钮 */
    shareYes() {
        tween(this.shareBtn)
            .to(0.1, { scale: v3(1.2, 1.2, 1.2) })
            .to(0.1, { scale: v3(1, 1, 1) })
            .call(() => {
                AudioManager.pauseMusic()
                AdManager.shareGame("吃大席", () => {
                    this.shareGame()
                    AudioManager.stopMusic()
                    AudioManager.playMusic("bgm_MaiBing", 1)
                },);
            })
            .start()
    }
    shareGame() {
        this.node.destroy()
        GameModel_MaiBing._ins.bgTs.isRevie = true
        GameModel_MaiBing._ins.bgTs.isDead = false
        GameModel_MaiBing._ins.bgTs.hasEnd = false
        GameModel_MaiBing._ins.bgTs.resetOrder()
    }

}

