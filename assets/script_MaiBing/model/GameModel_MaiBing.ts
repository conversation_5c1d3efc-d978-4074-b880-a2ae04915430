import { Sprite, SpriteFrame, resources } from "cc";
import { MainGame } from "../game/MainGame";
import { bgTs } from "../mainGame/bgTs_MaiBing";

/** 游戏数据相关 */
export class GameModel_MaiBing {
    /** 单例模式 */
    private static _instance: GameModel_MaiBing = new GameModel_MaiBing();
    private constructor() { }
    public static get _ins() {
        return this._instance;
    }
    /** 主场景 名字 */
    mainScene: string = "MainGame_MaiBing";

    /** MainGame脚本 */
    mainGame: MainGame = null;
    /** 游戏分数 */
    gameScore: number = 1;
    /** 平均分  用来计算超越了多少玩家 */
    standScore: number = 50;
    /** 最高分  用来计算超越了多少玩家 */
    gameMaxScore: number = 100;
    /**背景函数 */
    bgTs: bgTs = null
}