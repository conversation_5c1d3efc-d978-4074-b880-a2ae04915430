import { AudioClip, Material, Prefab, SpriteFrame, Texture2D } from "cc";
import { LoadTools_MaiBing } from "../common/LoadTools_MaiBing";

/** 资源存放的数据类 */
export class ResModel {
    /** 单例模式 */
    private static _instance: ResModel = new ResModel();
    private constructor() { }
    public static get _ins() {
        return this._instance;
    }

    /** 存储resArr脚本拖动的图片字典 */
    public SpriteFrameDic: Map<string, SpriteFrame> = new Map();
    /** 存储resArr脚本拖动的预制体字典 */
    public PrefabDic: Map<string, Prefab> = new Map();
    /** 存储resArr脚本拖动的音效字典 */
    public AudioClipDic: Map<string, AudioClip> = new Map();
    /** 存储resArr脚本拖动的材质字典 */
    public MaterialDic: Map<string, Material> = new Map();
    /** 存储resArr脚本拖动的Texture字典 */
    public TextureDic: Map<string, Texture2D> = new Map();

    /**  resArr清理字典 */
    public clearResDic() {
        this.AudioClipDic.clear();
        this.SpriteFrameDic.clear();
        this.PrefabDic.clear();
        this.MaterialDic.clear();
        this.TextureDic.clear();
    }


    /** 获取拖动到res 里面的 Texture 资源 */
    getTexture(key: string, callFunc?: (age: Texture2D) => {}): Texture2D {
        if (this.TextureDic[key]) {
            return this.TextureDic[key];
        } else {
            LoadTools_MaiBing._ins.loadResAny(key + "/texture", Texture2D, callFunc);
        }
    }
    /** 获取拖动到res 里面的 材质资源 */
    getMaterial(key: string, callFunc?: (age: Material) => {}) {
        if (this.MaterialDic[key]) {
            return this.MaterialDic[key];
        } else {
            LoadTools_MaiBing._ins.loadResAny(key, Material, callFunc);
        }
    }
}