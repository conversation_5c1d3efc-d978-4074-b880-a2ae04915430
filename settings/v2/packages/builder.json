{"__version__": "1.3.9", "bundleConfig": {"custom": {"default": {"displayName": "i18n:builder.asset_bundle.defaultConfig", "configs": {"native": {"preferredOptions": {"isRemote": false, "compressionType": "merge_dep"}}, "web": {"preferredOptions": {"isRemote": false, "compressionType": "merge_dep"}, "fallbackOptions": {"compressionType": "merge_dep"}}, "miniGame": {"fallbackOptions": {"isRemote": false, "compressionType": "merge_dep"}, "configMode": "overwrite", "overwriteSettings": {"bytedance-mini-game": {"isRemote": false, "compressionType": "subpackage"}, "alipay-mini-game": {"isRemote": false, "compressionType": "merge_dep"}, "fb-instant-games": {"isRemote": false, "compressionType": "merge_dep"}, "huawei-quick-game": {"isRemote": false, "compressionType": "merge_dep"}, "migu-mini-game": {"isRemote": false, "compressionType": "merge_dep"}, "oppo-mini-game": {"isRemote": false, "compressionType": "merge_dep"}, "taobao-mini-game": {"isRemote": false, "compressionType": "merge_dep"}, "vivo-mini-game": {"isRemote": false, "compressionType": "merge_dep"}, "wechatgame": {"isRemote": false, "compressionType": "subpackage"}, "xiaomi-quick-game": {"isRemote": false, "compressionType": "merge_dep"}}}}}}}, "textureCompressConfig": {"userPreset": {"dciU1U+0VJXqcBvtE9wtN+": null}}, "splash-setting": {"logo": {"type": "none"}, "background": {"color": {"x": 1, "y": 1, "z": 1, "w": 1}, "type": "color"}}}