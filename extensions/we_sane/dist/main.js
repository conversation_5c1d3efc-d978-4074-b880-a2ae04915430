
var Fs = require("fs");
const buildH5 = require("./buildH5");

exports.methods = {
    /** 打微伞包 */
	weSaneZip(){
		buildH5.writeIndex();  //写入Index
		buildH5.copyFloder("web-mobile_WeSane");  //复制替换文件 并打包zip
	},
    /** 打通用包 */
    commonH5Zip(){
        buildH5.writeIndex();  //写入Index
		buildH5.copyFloder("web-mobile_Other");  //复制替换文件 并打包zip
    }
};









/**
 * 启动的时候执行的初始化方法
 */
 exports.load = function() {
    buildH5.load();  //初始化  打包H5
	console.warn("we_sane is not compiled yet.");
 };
 /**
  * 插件被关闭的时候执行的卸载方法
  */
 exports.unload = function() {};
 

 /** buildH5Json  配置信息
  * indexUrl   index.html 的工程路径
  * replaceUrls   替换文件的 配置路径
  * webUrls  对应替换文件  需要copy 到 web里面的路径
  * writeIndexData  写入index.html 文件的配置   rowNum:行数  str:需要添加的代码
  */