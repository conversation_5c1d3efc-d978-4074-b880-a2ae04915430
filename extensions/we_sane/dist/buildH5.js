var Fs = require("fs");
var fsextra = require("fs-extra");
var zlib = require('zlib');
var archiver = require('archiver');
const path = require('path');

module.exports = {
  load() {
    this.webUrl = Editor.Project.path + "/build/web-mobile";    //打h5包的路径
    this.replaceUrl = Editor.Project.path + "/build/replace_H5"; //替换文件路径
    this.indexUrl = this.webUrl + "/index.html";  //index 位置

    //需要 复制 的文件
    this.replaceUrls = [
      "/weSane.js",
      "/res",
    ]
    /** index 需要写入的  数据  row行数 */
    this.writeIndexData = [
      { rowNum: 35, writeType: 'ADD', str: ' <meta data-react-helmet="true" property="og:title" content="Game Name"> ' },
      { rowNum: 36, writeType: 'ADD', str: ' <meta data-react-helmet="true" property="og:description" content="Game description.">' },
      { rowNum: 37, writeType: 'ADD', str: ' <meta data-react-helmet="true" property="og:image" content="res/share.jpg"> ' },
      { rowNum: 38, writeType: 'ADD', str: ' <meta http-equiv="cache-control" content="no-cache,no-store,must-revalidate" /> ' },
      { rowNum: 39, writeType: 'ADD', str: ' <meta http-equiv="Pragma" content="no-cache" /> ' },
      { rowNum: 40, writeType: 'ADD', str: ' <meta http-equiv="expires" content="-1" /> ' },

      { rowNum: 49, writeType: 'ADD', str: '<div id="splash"><div class="progress-bar stripes"><span style="width: 0%"></span></div></div>' },
      { rowNum: 51, writeType: 'ADD', str: '<div id="loadingImg" style="top:24%;align:center;width:100%;position:absolute;z-index:10;">  <img style="border-radius: 15px;" src="res/logo.png" width="90" height="90" />  </div>' },
      { rowNum: 52, writeType: 'ADD', str: '<div id="webText" type="text"  style="width:100%;text-align:center;position:absolute;top:38%;z-index:99;font-size:16px;color:#1e2222" > </div> ' },
      { rowNum: 53, writeType: 'ADD', str: '<div id="loadingText" type="text"  style="width:100%;text-align:center;position:absolute;top:56%;z-index:99;font-size:17px;color:#e86c00" > </div> ' },
      { rowNum: 54, writeType: 'ADD', str: '<div style="align:center;display: none"><img src="res/share.jpg" width="10%" /></div> ' },
      
      { rowNum: 55, writeType: 'ADD', str: ' <script src="weSane.js" charset="utf-8"> </script> ' },

      // { rowNum: 56, writeType: 'ADD', str: ' <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script> ' },
      // { rowNum: 57, writeType: 'ADD', str: ' <script src="https://www.wesane.com/Public/js/Anti-addiction.js"></script> ' },
    ]
  },

  /** 写入 index 文件 */
  writeIndex() {
    let indexStr = Fs.readFileSync(this.indexUrl, 'utf8');//读取文件
    let strArr = indexStr.split("\n"); //按回车分割成数组
    this.addStrArrData(this.writeIndexData, strArr);

    let weSaneJs = Fs.readFileSync(this.replaceUrl + "/weSane.js", 'utf8').split("\n");
    strArr[5] = "    <title>" + weSaneJs[4].split('"')[1] + '</title>'; //修改标题

    let writeStr = "";  //写入inex的字符串
    for (let i = 0; i < strArr.length; i++) {
      if (i < strArr.length - 1) {
        writeStr += (strArr[i] + "\n");
      }
    }
    Fs.writeFileSync(this.indexUrl, writeStr, 'utf8');
    console.log("index 写入成功");
  },
  /** 复制文件  打包zip 的名字 */
  copyFloder(zipName) {
    for (let i = 0; i < this.replaceUrls.length; i++) {
      fsextra.copySync(this.replaceUrl + this.replaceUrls[i], this.webUrl + this.replaceUrls[i], 0);
    }
    console.log("复制文件完成");
    // this.gzip(Editor.Project.path + "/build/web-mobile/weSane.js" );
    let outurl = Editor.Project.path + '/build/' + zipName + '.zip';  //zip 输出目录
    this.deleteFile(outurl);

    this.zipFolder(this.webUrl, outurl, function (err, msg) {
      console.log(err, msg);
    });
  },
  /** 删除文件 */
  deleteFile(url) {
    if (Fs.existsSync(url)) {
      Fs.unlinkSync(url);
    }
  },
  /** 打包Zip */
  zipFolder(sourceFolder, destZip, cb, subdir) {
    var output = Fs.createWriteStream(destZip);
    var archive = archiver('zip', {
      zlib: { level: 9 }
    });
    archive.pipe(output);
    archive.directory(sourceFolder, subdir ? sourceFolder.substr(path.dirname(sourceFolder).length + 1) : false);
    archive.finalize();
    console.log("打包zip 完成");
  },

  /** 添加 需要写入的配置里面的字符串 */
  addStrArrData(dataArr, strArr) {

    for (let i = 0; i < dataArr.length; i++) {
      if (dataArr[i].writeType == 'ADD') {
        if (strArr[dataArr[i].rowNum - 1] == dataArr[i].str) { continue; }
        strArr.splice(dataArr[i].rowNum - 1, 0, dataArr[i].str);
      } else if (dataArr[i].writeType == 'REVISE') {
        strArr[dataArr[i].rowNum - 1] = dataArr[i].str;
      } else if (dataArr[i].writeType == 'DELETE') {
        strArr[dataArr[i].rowNum - 1] = '';
      }
    }

  }
};